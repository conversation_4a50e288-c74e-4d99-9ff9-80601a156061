'use client';

import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import {
  CreditCard,
  DollarSign,
  AlertTriangle,
  CheckCircle,
  Clock,
  Loader2
} from 'lucide-react';
import { getExistingProjectDepositPayment, checkDepositStatus } from '@/lib/deposit-payment';
import { useOptimizedAuth } from '@/hooks/useOptimizedAuth';
import { supabase } from '@/lib/supabase';

interface DepositPaymentButtonProps {
  projectId: string;
  projectTitle: string;
  onPaymentSuccess?: () => void;
  className?: string;
}

export default function DepositPaymentButton({
  projectId,
  projectTitle,
  onPaymentSuccess,
  className = ''
}: DepositPaymentButtonProps) {
  const { user } = useOptimizedAuth();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [depositStatus, setDepositStatus] = useState<{
    isPaid: boolean;
    depositMilestone?: any;
    error?: string;
  } | null>(null);
  const [userPaymentMethod, setUserPaymentMethod] = useState<{
    payment_type: string;
    provider_name: string;
    icon: string;
  } | null>(null);

  React.useEffect(() => {
    checkDeposit();
    fetchUserPaymentMethod();
  }, [projectId, user]);

  const fetchUserPaymentMethod = async () => {
    if (!user) return;

    try {
      const { data: paymentMethods, error } = await supabase
        .from('payment_methods')
        .select('payment_type')
        .eq('user_id', user.id)
        .order('is_default', { ascending: false })
        .limit(1);

      if (error) {
        console.error('Error fetching payment method:', error);
        return;
      }

      const defaultMethod = paymentMethods?.[0];
      const paymentType = defaultMethod?.payment_type || 'paypal';

      // Map payment types to display information
      const paymentMethodInfo = {
        paypal: {
          payment_type: 'paypal',
          provider_name: 'PayPal',
          icon: '💳'
        },
        tappay: {
          payment_type: 'tappay',
          provider_name: 'TapPay',
          icon: '💳'
        },
        clickpay: {
          payment_type: 'clickpay',
          provider_name: 'ClickPay',
          icon: '💳'
        }
      };

      setUserPaymentMethod(paymentMethodInfo[paymentType as keyof typeof paymentMethodInfo] || paymentMethodInfo.paypal);
    } catch (error) {
      console.error('Error fetching user payment method:', error);
      // Default to PayPal if there's an error
      setUserPaymentMethod({
        payment_type: 'paypal',
        provider_name: 'PayPal',
        icon: '💳'
      });
    }
  };

  const checkDeposit = async () => {
    try {
      const status = await checkDepositStatus(projectId);
      setDepositStatus(status);
    } catch (error) {
      console.error('Error checking deposit status:', error);
    }
  };

  const handlePayDeposit = async () => {
    setLoading(true);
    setError(null);

    try {
      const result = await getExistingProjectDepositPayment(projectId);
      
      if (result.success && result.paymentUrl) {
        // Redirect to payment (PayPal, TapPay, ClickPay, etc.)
        window.location.href = result.paymentUrl;
      } else {
        setError(result.error || 'Failed to create payment');
      }
    } catch (error) {
      console.error('Error creating deposit payment:', error);
      setError('Failed to create deposit payment');
    } finally {
      setLoading(false);
    }
  };

  // Don't render if deposit is already paid
  if (depositStatus?.isPaid) {
    return (
      <Card className={`border-green-200 bg-green-50 ${className}`}>
        <CardContent className="p-4">
          <div className="flex items-center gap-2 text-green-700">
            <CheckCircle className="h-5 w-5" />
            <span className="font-medium">Initial Deposit Paid</span>
          </div>
          <p className="text-sm text-green-600 mt-1">
            Project is active and ready for work
          </p>
        </CardContent>
      </Card>
    );
  }

  // Don't render if no deposit milestone found
  if (depositStatus?.error) {
    return null;
  }

  return (
    <Card className={`border-orange-200 bg-orange-50 ${className}`}>
      <CardHeader className="pb-3">
        <CardTitle className="flex items-center gap-2 text-orange-800">
          <AlertTriangle className="h-5 w-5" />
          Initial Deposit Required
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="font-medium text-gray-900">
                {projectTitle}
              </p>
              <p className="text-sm text-gray-600">
                Project cannot start until initial deposit is paid
              </p>
            </div>
            <Badge variant="outline" className="border-orange-300 text-orange-700">
              <Clock className="h-3 w-3 mr-1" />
              Pending Payment
            </Badge>
          </div>

          {depositStatus?.depositMilestone && (
            <div className="bg-white rounded-lg p-3 border">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium text-gray-700">
                  Deposit Amount:
                </span>
                <span className="text-lg font-bold text-gray-900">
                  ${depositStatus.depositMilestone.amount?.toFixed(2) || '0.00'}
                </span>
              </div>
            </div>
          )}

          {error && (
            <Alert variant="destructive">
              <AlertTriangle className="h-4 w-4" />
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          <Button
            onClick={handlePayDeposit}
            disabled={loading}
            className="w-full bg-blue-600 hover:bg-blue-700 text-white"
            size="lg"
          >
            {loading ? (
              <>
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                Creating Payment...
              </>
            ) : (
              <>
                <CreditCard className="h-4 w-4 mr-2" />
                Pay Initial Deposit
                {userPaymentMethod && userPaymentMethod.payment_type !== 'paypal' && (
                  <span className="ml-2 text-xs opacity-75">
                    via {userPaymentMethod.provider_name}
                  </span>
                )}
              </>
            )}
          </Button>

          <div className="text-xs text-gray-500 text-center">
            <p>
              Secure payment powered by {userPaymentMethod?.provider_name || 'PayPal'}
            </p>
            <p>Your payment is held in escrow until project completion</p>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}

// Compact version for use in project lists
export function CompactDepositPaymentButton({
  projectId,
  projectTitle,
  onPaymentSuccess,
  className = ''
}: DepositPaymentButtonProps) {
  const { user } = useOptimizedAuth();
  const [loading, setLoading] = useState(false);
  const [depositStatus, setDepositStatus] = useState<{
    isPaid: boolean;
    depositMilestone?: any;
  } | null>(null);
  const [userPaymentMethod, setUserPaymentMethod] = useState<{
    payment_type: string;
    provider_name: string;
  } | null>(null);

  React.useEffect(() => {
    checkDeposit();
    fetchCompactUserPaymentMethod();
  }, [projectId, user]);

  const fetchCompactUserPaymentMethod = async () => {
    if (!user) return;

    try {
      const { data: paymentMethods, error } = await supabase
        .from('payment_methods')
        .select('payment_type')
        .eq('user_id', user.id)
        .order('is_default', { ascending: false })
        .limit(1);

      if (error) {
        console.error('Error fetching payment method:', error);
        return;
      }

      const defaultMethod = paymentMethods?.[0];
      const paymentType = defaultMethod?.payment_type || 'paypal';

      const paymentMethodInfo = {
        paypal: { payment_type: 'paypal', provider_name: 'PayPal' },
        tappay: { payment_type: 'tappay', provider_name: 'TapPay' },
        clickpay: { payment_type: 'clickpay', provider_name: 'ClickPay' }
      };

      setUserPaymentMethod(paymentMethodInfo[paymentType as keyof typeof paymentMethodInfo] || paymentMethodInfo.paypal);
    } catch (error) {
      console.error('Error fetching user payment method:', error);
      setUserPaymentMethod({ payment_type: 'paypal', provider_name: 'PayPal' });
    }
  };

  const checkDeposit = async () => {
    try {
      const status = await checkDepositStatus(projectId);
      setDepositStatus(status);
    } catch (error) {
      console.error('Error checking deposit status:', error);
    }
  };

  const handlePayDeposit = async () => {
    setLoading(true);

    try {
      const result = await getExistingProjectDepositPayment(projectId);
      
      if (result.success && result.paymentUrl) {
        window.location.href = result.paymentUrl;
      }
    } catch (error) {
      console.error('Error creating deposit payment:', error);
    } finally {
      setLoading(false);
    }
  };

  // Show paid status
  if (depositStatus?.isPaid) {
    return (
      <Badge variant="outline" className="border-green-300 text-green-700 bg-green-50">
        <CheckCircle className="h-3 w-3 mr-1" />
        Deposit Paid
      </Badge>
    );
  }

  // Show payment button
  return (
    <Button
      onClick={handlePayDeposit}
      disabled={loading}
      size="sm"
      className={`bg-orange-600 hover:bg-orange-700 text-white ${className}`}
      title={userPaymentMethod ? `Pay via ${userPaymentMethod.provider_name}` : 'Pay Deposit'}
    >
      {loading ? (
        <Loader2 className="h-3 w-3 mr-1 animate-spin" />
      ) : (
        <DollarSign className="h-3 w-3 mr-1" />
      )}
      Pay Deposit
      {userPaymentMethod && userPaymentMethod.payment_type !== 'paypal' && (
        <span className="ml-1 text-xs opacity-75">
          ({userPaymentMethod.provider_name})
        </span>
      )}
    </Button>
  );
}
