// Quick test to verify content filtering is working
const { filterMessage, shouldBlockMessage } = require('./src/lib/content-filter.ts');

const testMessages = [
  "My <NAME_EMAIL>",
  "Call me at 555-123-4567", 
  "Contact me outside the platform",
  "This is a normal message",
  "Here's my WhatsApp: +1234567890"
];

console.log("🧪 Testing Content Filter System\n");

testMessages.forEach((message, index) => {
  console.log(`Test ${index + 1}: "${message}"`);
  
  try {
    const result = filterMessage(message);
    const shouldBlock = shouldBlockMessage(result);
    
    console.log(`  ✅ Blocked: ${result.isBlocked}`);
    console.log(`  🚫 Should Block: ${shouldBlock}`);
    console.log(`  📝 Detected: [${result.blockedContent.join(', ')}]`);
    console.log(`  ⚠️  Type: ${result.warningType} (${result.severity})`);
    console.log(`  🔄 Filtered: "${result.filteredMessage}"`);
  } catch (error) {
    console.log(`  ❌ Error: ${error.message}`);
  }
  
  console.log("");
});
