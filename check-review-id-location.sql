-- =====================================================
-- CHECK WHERE REVIEW ID EXISTS
-- This helps debug the missing review issue
-- =====================================================

-- Check if the specific review ID exists in quality_reviews_new
SELECT 
    'quality_reviews_new' as table_name,
    id,
    status,
    project_id,
    designer_id,
    created_at,
    'Found in quality_reviews_new' as location
FROM quality_reviews_new 
WHERE id = '0d167e18-75b9-4cfe-8a37-b1e8f539e3f0'

UNION ALL

-- Check if it exists in the old quality_reviews table (if it exists)
SELECT 
    'quality_reviews' as table_name,
    id,
    status,
    project_id,
    designer_id,
    created_at,
    'Found in old quality_reviews table' as location
FROM quality_reviews 
WHERE id = '0d167e18-75b9-4cfe-8a37-b1e8f539e3f0'
AND EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'quality_reviews')

UNION ALL

-- Check if it exists in project_submissions (maybe it's a submission ID, not review ID)
SELECT 
    'project_submissions' as table_name,
    id,
    status,
    project_id,
    designer_id,
    submitted_at as created_at,
    'Found in project_submissions - this might be a submission ID' as location
FROM project_submissions 
WHERE id = '0d167e18-75b9-4cfe-8a37-b1e8f539e3f0';

-- Also check what quality reviews exist for debugging
SELECT 
    'Available Reviews' as info,
    COUNT(*) as total_reviews,
    COUNT(*) FILTER (WHERE status = 'pending') as pending,
    COUNT(*) FILTER (WHERE status = 'in_review') as in_review,
    COUNT(*) FILTER (WHERE status = 'approved') as approved,
    MIN(created_at) as oldest,
    MAX(created_at) as newest
FROM quality_reviews_new;

-- Show some sample review IDs
SELECT 
    'Sample Review IDs' as info,
    id,
    status,
    project_id,
    created_at
FROM quality_reviews_new 
ORDER BY created_at DESC 
LIMIT 5;

-- Check if there's a relationship between submissions and reviews
SELECT 
    'Submission-Review Relationship' as info,
    ps.id as submission_id,
    ps.quality_review_id,
    qr.id as review_id,
    ps.status as submission_status,
    qr.status as review_status
FROM project_submissions ps
LEFT JOIN quality_reviews_new qr ON ps.quality_review_id = qr.id
WHERE ps.id = '0d167e18-75b9-4cfe-8a37-b1e8f539e3f0'
   OR qr.id = '0d167e18-75b9-4cfe-8a37-b1e8f539e3f0';

-- Check if the ID might be in a different format or table
SELECT
    'ID Format Check' as info,
    '0d167e18-75b9-4cfe-8a37-b1e8f539e3f0' as searched_id,
    LENGTH('0d167e18-75b9-4cfe-8a37-b1e8f539e3f0') as id_length,
    'Standard UUID format' as format_type;

-- ADDITIONAL INVESTIGATION: Check submission-review workflow
-- This checks if quality reviews are being created for submissions

-- Check all submissions and their review status
SELECT
    'Submission-Review Workflow Analysis' as analysis,
    ps.id as submission_id,
    ps.status as submission_status,
    ps.quality_review_id,
    qr.id as actual_review_id,
    qr.status as review_status,
    CASE
        WHEN ps.quality_review_id IS NULL THEN '❌ No review ID assigned'
        WHEN qr.id IS NULL THEN '❌ Review ID exists but review not found'
        WHEN ps.quality_review_id = qr.id THEN '✅ Correct relationship'
        ELSE '⚠️ Mismatched IDs'
    END as relationship_status,
    ps.submitted_at,
    ps.submission_source
FROM project_submissions ps
LEFT JOIN quality_reviews_new qr ON ps.quality_review_id = qr.id
ORDER BY ps.submitted_at DESC
LIMIT 10;

-- Check if there are orphaned quality reviews (reviews without submissions)
SELECT
    'Orphaned Reviews Check' as analysis,
    qr.id as review_id,
    qr.status,
    qr.submission_id,
    ps.id as actual_submission_id,
    CASE
        WHEN ps.id IS NULL THEN '❌ Review has no matching submission'
        WHEN qr.submission_id = ps.id THEN '✅ Correct submission_id relationship'
        ELSE '⚠️ Mismatched submission_id'
    END as submission_relationship,
    qr.created_at
FROM quality_reviews_new qr
LEFT JOIN project_submissions ps ON qr.submission_id = ps.id
ORDER BY qr.created_at DESC
LIMIT 10;

-- Check the specific problematic submission
SELECT
    'Problematic Submission Analysis' as analysis,
    ps.id as submission_id,
    ps.title,
    ps.status as submission_status,
    ps.quality_review_id,
    ps.submission_source,
    ps.submitted_at,
    qr.id as review_id,
    qr.status as review_status,
    qr.submission_id as review_submission_id,
    CASE
        WHEN ps.quality_review_id IS NULL THEN 'Need to create quality review for this submission'
        WHEN qr.id IS NULL THEN 'Quality review ID exists but review not found - data integrity issue'
        ELSE 'Review exists and linked correctly'
    END as recommendation
FROM project_submissions ps
LEFT JOIN quality_reviews_new qr ON ps.quality_review_id = qr.id
WHERE ps.id = '0d167e18-75b9-4cfe-8a37-b1e8f539e3f0';

-- Show the workflow that should happen
SELECT
    'Expected Workflow' as info,
    '1. Submission created in project_submissions' as step1,
    '2. Quality review created in quality_reviews_new' as step2,
    '3. project_submissions.quality_review_id = quality_reviews_new.id' as step3,
    '4. quality_reviews_new.submission_id = project_submissions.id' as step4,
    '5. Review button uses quality_review_id to navigate' as step5;
