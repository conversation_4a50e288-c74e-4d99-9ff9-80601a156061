-- Query to check the structure of files in project_submissions
SELECT 
    ps.id,
    ps.project_id,
    p.title as project_title,
    ps.files,
    ps.submitted_at,
    jsonb_array_length(ps.files::jsonb) as file_count
FROM project_submissions ps
LEFT JOIN projects p ON p.id = ps.project_id
WHERE ps.files IS NOT NULL 
  AND ps.files != '[]'
  AND jsonb_array_length(ps.files::jsonb) > 0
ORDER BY ps.submitted_at DESC
LIMIT 5;

-- Sample file structure analysis
SELECT 
    ps.id,
    ps.project_id,
    jsonb_array_elements(ps.files::jsonb) as file_data
FROM project_submissions ps
WHERE ps.files IS NOT NULL 
  AND ps.files != '[]'
  AND jsonb_array_length(ps.files::jsonb) > 0
LIMIT 3;
