'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import QualityDesignerChat from '@/components/quality/QualityDesignerChat';
import { useOptimizedAuth } from '@/hooks/useOptimizedAuth';
import { supabase } from '@/lib/supabase';
import { 
  MessageSquare, 
  AlertTriangle, 
  Users, 
  Clock,
  RefreshCw
} from 'lucide-react';

interface ActiveConversation {
  review_id: string;
  project_title: string;
  designer_id: string;
  designer_name: string;
  designer_avatar: string;
  last_message_at: string;
  unread_count: number;
}

export default function CommunicationHubPage() {
  const { user, profile, loading } = useOptimizedAuth();
  const [activeConversations, setActiveConversations] = useState<ActiveConversation[]>([]);
  const [selectedConversation, setSelectedConversation] = useState<ActiveConversation | null>(null);
  const [loadingConversations, setLoadingConversations] = useState(true);

  useEffect(() => {
    if (user && ['quality_team', 'admin'].includes(profile?.role)) {
      fetchActiveConversations();
    }
  }, [user, profile]);

  const fetchActiveConversations = async () => {
    setLoadingConversations(true);
    try {
      // Fetch active conversations from quality reviews with recent messages
      const { data: reviews, error } = await supabase
        .from('quality_reviews_new')
        .select(`
          id,
          project_id,
          reviewer_id,
          status,
          created_at,
          projects!inner(
            title,
            designer_id,
            profiles!designer_id(
              full_name,
              avatar_url
            )
          ),
          quality_chat_messages(
            id,
            created_at,
            status
          )
        `)
        .in('status', ['pending', 'in_review', 'needs_revision'])
        .order('created_at', { ascending: false });

      if (error) throw error;

      // Transform reviews into conversations
      const conversations: ActiveConversation[] = [];

      reviews?.forEach(review => {
        const messages = review.quality_chat_messages || [];
        const unreadMessages = messages.filter((msg: any) => msg.status === 'unread');
        const lastMessage = messages.sort((a: any, b: any) =>
          new Date(b.created_at).getTime() - new Date(a.created_at).getTime()
        )[0];

        if (review.projects?.profiles) {
          conversations.push({
            review_id: review.id,
            project_title: review.projects.title,
            designer_id: review.projects.designer_id,
            designer_name: review.projects.profiles.full_name,
            designer_avatar: review.projects.profiles.avatar_url || '',
            last_message_at: lastMessage?.created_at || review.created_at,
            unread_count: unreadMessages.length
          });
        }
      });

      // Sort by last message time
      conversations.sort((a, b) =>
        new Date(b.last_message_at).getTime() - new Date(a.last_message_at).getTime()
      );

      setActiveConversations(conversations);
    } catch (error) {
      console.error('Error fetching conversations:', error);
      setActiveConversations([]);
    } finally {
      setLoadingConversations(false);
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (!user || !['quality_team', 'admin'].includes(profile?.role)) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <Card className="w-96">
          <CardContent className="p-6 text-center">
            <AlertTriangle className="h-12 w-12 text-red-500 mx-auto mb-4" />
            <h2 className="text-xl font-semibold mb-2">Access Denied</h2>
            <p className="text-gray-600">You don't have permission to access this page.</p>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="mb-8">
        <div className="flex items-center gap-3 mb-2">
          <MessageSquare className="h-8 w-8 text-blue-500" />
          <h1 className="text-3xl font-bold">Communication Hub</h1>
        </div>
        <p className="text-gray-600">
          Real-time communication with designers and feedback management
        </p>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 h-[calc(100vh-200px)]">
        {/* Conversations List */}
        <Card className="lg:col-span-1">
          <CardHeader className="pb-3">
            <div className="flex items-center justify-between">
              <CardTitle className="flex items-center gap-2">
                <Users className="h-5 w-5" />
                Active Conversations
              </CardTitle>
              <Button
                variant="outline"
                size="sm"
                onClick={fetchActiveConversations}
                disabled={loadingConversations}
              >
                <RefreshCw className={`h-4 w-4 ${loadingConversations ? 'animate-spin' : ''}`} />
              </Button>
            </div>
          </CardHeader>
          <CardContent className="p-0">
            {loadingConversations ? (
              <div className="flex items-center justify-center py-8">
                <RefreshCw className="h-6 w-6 text-gray-400 animate-spin" />
              </div>
            ) : activeConversations.length === 0 ? (
              <div className="text-center py-8 text-gray-500">
                <MessageSquare className="h-12 w-12 mx-auto mb-4" />
                <p>No active conversations</p>
                <p className="text-sm mt-2">Conversations will appear when designers submit work for review</p>
              </div>
            ) : (
              <div className="space-y-1">
                {activeConversations.map((conversation) => (
                  <button
                    key={conversation.review_id}
                    onClick={() => setSelectedConversation(conversation)}
                    className={`w-full text-left p-4 hover:bg-gray-50 border-b transition-colors ${
                      selectedConversation?.review_id === conversation.review_id ? 'bg-blue-50 border-blue-200' : ''
                    }`}
                  >
                    <div className="flex items-start justify-between">
                      <div className="flex-1 min-w-0">
                        <h3 className="font-medium text-sm truncate">{conversation.project_title}</h3>
                        <p className="text-sm text-gray-600 truncate">{conversation.designer_name}</p>
                        <div className="flex items-center gap-2 mt-1">
                          <Clock className="h-3 w-3 text-gray-400" />
                          <span className="text-xs text-gray-500">
                            {new Date(conversation.last_message_at).toLocaleTimeString()}
                          </span>
                        </div>
                      </div>
                      {conversation.unread_count > 0 && (
                        <Badge variant="default" className="bg-blue-500 text-white">
                          {conversation.unread_count}
                        </Badge>
                      )}
                    </div>
                  </button>
                ))}
              </div>
            )}
          </CardContent>
        </Card>

        {/* Chat Interface */}
        <div className="lg:col-span-2">
          {selectedConversation ? (
            <QualityDesignerChat
              projectId="project-id" // This would come from the conversation
              reviewId={selectedConversation.review_id}
              designerId={selectedConversation.designer_id}
              designerName={selectedConversation.designer_name}
              designerAvatar={selectedConversation.designer_avatar}
              qualityTeamId={user.id}
              qualityTeamName={profile?.full_name || 'Quality Team'}
              qualityTeamAvatar={profile?.avatar_url}
              onNewMessage={(message) => {
                // Handle new message
                console.log('New message:', message);
              }}
            />
          ) : (
            <Card className="h-full">
              <CardContent className="flex items-center justify-center h-full">
                <div className="text-center text-gray-500">
                  <MessageSquare className="h-16 w-16 mx-auto mb-4" />
                  <h3 className="text-lg font-medium mb-2">Select a Conversation</h3>
                  <p>Choose a conversation from the list to start chatting with designers</p>
                </div>
              </CardContent>
            </Card>
          )}
        </div>
      </div>
    </div>
  );
}
