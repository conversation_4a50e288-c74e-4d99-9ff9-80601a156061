-- Fix the priority constraint violation in quality_reviews_new table
-- The issue is that triggers are creating records with 'medium' priority
-- but the constraint only allows ('low', 'normal', 'high', 'urgent')

-- Step 1: Drop any problematic triggers that create quality reviews with medium priority
DROP TRIGGER IF EXISTS trigger_create_quality_review ON project_submissions;
DROP TRIGGER IF EXISTS trigger_create_quality_assignment ON project_submissions;
DROP TRIGGER IF EXISTS trigger_create_quality_assignment_new ON project_submissions;

-- Step 2: Fix existing records with 'medium' priority
UPDATE quality_reviews_new 
SET priority = 'normal' 
WHERE priority = 'medium';

-- Step 3: Create a fixed trigger function that uses 'normal' instead of 'medium'
CREATE OR REPLACE FUNCTION create_quality_review_on_submission()
RETURNS TRIGGER AS $$
BEGIN
    -- Only create quality review for submitted status
    IF NEW.status = 'submitted' AND (OLD.status IS NULL OR OLD.status != 'submitted') THEN
        INSERT INTO quality_reviews_new (
            project_id,
            submission_id,
            milestone_id,
            designer_id,
            review_type,
            status,
            priority,
            created_at
        ) VALUES (
            NEW.project_id,
            NEW.id,
            NEW.milestone_id,
            NEW.designer_id,
            'submission',
            'pending',
            'normal',  -- Use 'normal' instead of 'medium'
            NOW()
        );
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Step 4: Update the auto-assignment trigger to use valid priority
CREATE OR REPLACE FUNCTION trigger_auto_assign_review()
RETURNS TRIGGER AS $$
BEGIN
  -- Only auto-assign if no reviewer is already assigned
  IF NEW.reviewer_id IS NULL AND NEW.status = 'pending' THEN
    PERFORM assign_quality_review(NEW.id);
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Step 5: Verify the fix
SELECT 'Priority values in quality_reviews_new:' as info;
SELECT priority, COUNT(*) 
FROM quality_reviews_new 
GROUP BY priority
ORDER BY priority;

-- Step 6: Check if any records still have invalid priority
SELECT 'Records with invalid priority:' as info;
SELECT COUNT(*) as invalid_count
FROM quality_reviews_new 
WHERE priority NOT IN ('low', 'normal', 'high', 'urgent');

SELECT 'Fix completed successfully!' as status;
