-- =====================================================
-- COMPREHENSIVE QUALITY REVIEW SUBMISSION TEST
-- This simulates the exact process that happens when submitting a review
-- =====================================================

-- Test 1: Check if we have the required data
SELECT 
    'Data Availability Check' as test_name,
    (SELECT COUNT(*) FROM quality_reviews_new) as reviews_count,
    (SELECT COUNT(*) FROM quality_standards) as standards_count,
    (SELECT COUNT(*) FROM profiles WHERE role = 'quality_team') as quality_team_count;

-- Test 2: Get a sample review to test with
DO $$
DECLARE
    test_review_id UUID;
    test_designer_id UUID;
    test_project_id UUID;
    test_standard_id UUID;
    review_exists BOOLEAN;
BEGIN
    -- Get a real review from quality_reviews_new
    SELECT id, designer_id, project_id INTO test_review_id, test_designer_id, test_project_id
    FROM quality_reviews_new 
    WHERE status IN ('pending', 'in_review')
    LIMIT 1;
    
    -- Get a quality standard
    SELECT id INTO test_standard_id
    FROM quality_standards
    WHERE is_active = true
    LIMIT 1;
    
    IF test_review_id IS NOT NULL THEN
        RAISE NOTICE '✅ Test Review Found: %', test_review_id;
        RAISE NOTICE '   Designer: %, Project: %', test_designer_id, test_project_id;
        
        -- Test the exact operations that happen during review submission
        
        -- 1. Update the review (simulating review completion)
        UPDATE quality_reviews_new 
        SET 
            status = 'approved',
            overall_score = 4,
            feedback = 'Test feedback from automated test',
            revision_notes = 'Test revision notes',
            reviewed_at = NOW(),
            revision_count = 0
        WHERE id = test_review_id;
        
        RAISE NOTICE '✅ Review update successful';
        
        -- 2. Insert quality feedback (this is where the error was happening)
        IF test_standard_id IS NOT NULL THEN
            INSERT INTO quality_feedback (
                review_id,
                standard_id,
                passed,
                score,
                comments,
                suggestions
            ) VALUES (
                test_review_id,
                test_standard_id,
                true,
                4,
                'Test feedback comment - automated test',
                'Test suggestions - automated test'
            );
            
            RAISE NOTICE '✅ Quality feedback insert successful';
            
            -- Clean up test feedback
            DELETE FROM quality_feedback 
            WHERE review_id = test_review_id 
            AND comments LIKE '%automated test%';
            
            RAISE NOTICE '✅ Test cleanup completed';
        ELSE
            RAISE NOTICE '⚠️ No quality standards found for feedback test';
        END IF;
        
        -- Reset the review status
        UPDATE quality_reviews_new 
        SET 
            status = 'pending',
            overall_score = NULL,
            feedback = NULL,
            revision_notes = NULL,
            reviewed_at = NULL
        WHERE id = test_review_id;
        
        RAISE NOTICE '✅ Review status reset to original state';
        
    ELSE
        RAISE NOTICE '❌ No test review found in quality_reviews_new table';
    END IF;
    
EXCEPTION
    WHEN OTHERS THEN
        RAISE NOTICE '❌ Test FAILED with error: %', SQLERRM;
        RAISE NOTICE '   Error Code: %', SQLSTATE;
END $$;

-- Test 3: Verify foreign key constraint is working correctly
SELECT 
    'Foreign Key Constraint Check' as test_name,
    tc.constraint_name,
    tc.table_name as source_table,
    ccu.table_name as target_table,
    ccu.column_name as target_column,
    CASE 
        WHEN ccu.table_name = 'quality_reviews_new' THEN '✅ CORRECT'
        WHEN ccu.table_name = 'quality_reviews' THEN '❌ WRONG - POINTS TO OLD TABLE'
        ELSE '❓ UNKNOWN TARGET'
    END as constraint_status
FROM information_schema.table_constraints tc
JOIN information_schema.constraint_column_usage ccu 
    ON tc.constraint_name = ccu.constraint_name
WHERE tc.constraint_name = 'quality_feedback_review_id_fkey'
AND tc.table_name = 'quality_feedback';

-- Test 4: Check RLS policy effectiveness
DO $$
DECLARE
    policy_count INTEGER;
    null_qual_count INTEGER;
BEGIN
    SELECT COUNT(*) INTO policy_count
    FROM pg_policies 
    WHERE tablename = 'quality_feedback';
    
    SELECT COUNT(*) INTO null_qual_count
    FROM pg_policies 
    WHERE tablename = 'quality_feedback' 
    AND qual IS NULL;
    
    RAISE NOTICE 'RLS Policy Status:';
    RAISE NOTICE '  Total policies: %', policy_count;
    RAISE NOTICE '  Policies with null qual: %', null_qual_count;
    
    IF null_qual_count > 0 THEN
        RAISE NOTICE '⚠️ WARNING: Some policies have null qual - this may cause permission issues';
    ELSE
        RAISE NOTICE '✅ All policies have proper qual expressions';
    END IF;
END $$;

-- Test 5: Final verification query
SELECT 
    'Final Status Check' as test_name,
    'quality_feedback' as table_name,
    COUNT(*) as total_records,
    COUNT(DISTINCT review_id) as unique_reviews,
    COUNT(DISTINCT standard_id) as unique_standards,
    MIN(created_at) as oldest_feedback,
    MAX(created_at) as newest_feedback
FROM quality_feedback;

-- Show sample of existing feedback data
SELECT 
    'Sample Feedback Data' as info,
    qf.id,
    qf.review_id,
    qf.passed,
    qf.score,
    LEFT(qf.comments, 50) as comments_preview,
    qf.created_at
FROM quality_feedback qf
ORDER BY qf.created_at DESC
LIMIT 3;
