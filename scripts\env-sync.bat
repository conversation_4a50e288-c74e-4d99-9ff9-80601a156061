@echo off
echo.
echo ========================================
echo   Environment Variables Sync Script
echo ========================================
echo.

REM Check if Node.js is installed
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo Error: Node.js is not installed or not in PATH
    echo Please install Node.js from https://nodejs.org/
    pause
    exit /b 1
)

REM Check if we're in the right directory
if not exist "package.json" (
    echo Error: package.json not found
    echo Please run this script from the project root directory
    pause
    exit /b 1
)

REM Run the sync script
echo Running environment sync script...
echo.
node scripts/env-sync.js

if %errorlevel% equ 0 (
    echo.
    echo ========================================
    echo   Sync completed successfully!
    echo ========================================
    echo.
    echo Files generated:
    echo   - production.env
    echo   - env-comparison.json
    echo.
    echo Next steps:
    echo   1. Review production.env file
    echo   2. Import to Vercel: npx vercel env add ^< production.env
    echo.
) else (
    echo.
    echo ========================================
    echo   Sync failed!
    echo ========================================
    echo.
    echo Please check the error messages above.
    echo Make sure you are logged in to Vercel CLI.
    echo.
)

pause
