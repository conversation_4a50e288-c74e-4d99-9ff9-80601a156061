"use client";

import { useState, useEffect } from "react";
import { useOptimizedAuth } from "@/hooks/useOptimizedAuth";
import { supabase } from "@/lib/supabase";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { RefreshCw, Search, Database } from "lucide-react";

export default function DebugEscrowPage() {
  const { user, profile } = useOptimizedAuth();
  const [loading, setLoading] = useState(false);
  const [results, setResults] = useState<any>({});

  const checkAllEscrowTables = async () => {
    setLoading(true);
    const results: any = {};

    try {
      // 1. Check payments table for completed PayPal deposits
      const { data: payments, error: paymentsError } = await supabase
        .from('payments')
        .select('*')
        .eq('payment_method', 'paypal')
        .eq('payment_type', 'deposit')
        .eq('status', 'completed')
        .order('created_at', { ascending: false });

      results.payments = { data: payments, error: paymentsError };

      // 2. Check transactions table
      const { data: transactions, error: transactionsError } = await supabase
        .from('transactions')
        .select('*')
        .eq('payment_method', 'paypal')
        .eq('type', 'payment')
        .order('created_at', { ascending: false });

      results.transactions = { data: transactions, error: transactionsError };

      // 3. Check escrow_holds table (without payment_method filter)
      const { data: escrowHolds, error: escrowHoldsError } = await supabase
        .from('escrow_holds')
        .select('*')
        .order('created_at', { ascending: false });

      results.escrow_holds = { data: escrowHolds, error: escrowHoldsError };

      // 4. Check paypal_escrow_holds table
      const { data: paypalEscrowHolds, error: paypalEscrowHoldsError } = await supabase
        .from('paypal_escrow_holds')
        .select('*')
        .order('created_at', { ascending: false });

      results.paypal_escrow_holds = { data: paypalEscrowHolds, error: paypalEscrowHoldsError };

      // 5. Check escrow_transactions table
      const { data: escrowTransactions, error: escrowTransactionsError } = await supabase
        .from('escrow_transactions')
        .select('*')
        .eq('status', 'held')
        .order('created_at', { ascending: false });

      results.escrow_transactions = { data: escrowTransactions, error: escrowTransactionsError };

      // 6. Check escrow_accounts table
      const { data: escrowAccounts, error: escrowAccountsError } = await supabase
        .from('escrow_accounts')
        .select('*')
        .order('created_at', { ascending: false });

      results.escrow_accounts = { data: escrowAccounts, error: escrowAccountsError };

      // 7. Check project_milestones for paid deposits (without type filter)
      const { data: milestones, error: milestonesError } = await supabase
        .from('project_milestones')
        .select('*, projects(title, client_id, designer_id)')
        .eq('status', 'paid')
        .order('created_at', { ascending: false });

      results.paid_milestones = { data: milestones, error: milestonesError };

    } catch (error) {
      console.error('Error checking escrow tables:', error);
    }

    setResults(results);
    setLoading(false);
  };

  useEffect(() => {
    if (user && (profile?.role === 'admin' || profile?.role === 'manager')) {
      checkAllEscrowTables();
    }
  }, [user, profile]);

  if (!user || (profile?.role !== 'admin' && profile?.role !== 'manager')) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-900 mb-4">Access Denied</h1>
          <p className="text-gray-600">Only admins and managers can access this debug page.</p>
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-7xl mx-auto p-6 space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Escrow System Debug</h1>
          <p className="text-gray-600 mt-2">Diagnostic tool to find where PayPal deposits are stored</p>
        </div>
        <Button onClick={checkAllEscrowTables} disabled={loading}>
          <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
          Refresh Data
        </Button>
      </div>

      {loading ? (
        <div className="flex items-center justify-center py-12">
          <RefreshCw className="h-8 w-8 animate-spin text-blue-600" />
          <span className="ml-2">Checking all escrow tables...</span>
        </div>
      ) : (
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {Object.entries(results).map(([tableName, result]: [string, any]) => (
            <Card key={tableName}>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Database className="h-5 w-5" />
                  {tableName.replace(/_/g, ' ').toUpperCase()}
                  <span className={`px-2 py-1 rounded text-xs ${
                    result.error ? 'bg-red-100 text-red-700' : 
                    result.data?.length > 0 ? 'bg-green-100 text-green-700' : 
                    'bg-gray-100 text-gray-700'
                  }`}>
                    {result.error ? 'ERROR' : `${result.data?.length || 0} records`}
                  </span>
                </CardTitle>
              </CardHeader>
              <CardContent>
                {result.error ? (
                  <div className="text-red-600 text-sm">
                    <strong>Error:</strong> {result.error.message}
                  </div>
                ) : result.data?.length > 0 ? (
                  <div className="space-y-2">
                    {result.data.slice(0, 3).map((record: any, index: number) => (
                      <div key={index} className="bg-gray-50 p-3 rounded text-sm">
                        <div><strong>ID:</strong> {record.id}</div>
                        {record.amount && <div><strong>Amount:</strong> ${record.amount}</div>}
                        {record.gross_amount && <div><strong>Gross Amount:</strong> ${record.gross_amount}</div>}
                        {record.status && <div><strong>Status:</strong> {record.status}</div>}
                        {record.project_id && <div><strong>Project ID:</strong> {record.project_id}</div>}
                        {record.created_at && <div><strong>Created:</strong> {new Date(record.created_at).toLocaleString()}</div>}
                      </div>
                    ))}
                    {result.data.length > 3 && (
                      <div className="text-gray-500 text-sm">
                        ... and {result.data.length - 3} more records
                      </div>
                    )}
                  </div>
                ) : (
                  <div className="text-gray-500 text-sm">No records found</div>
                )}
              </CardContent>
            </Card>
          ))}
        </div>
      )}

      {results.payments?.data?.length > 0 && (
        <Card className="mt-6">
          <CardHeader>
            <CardTitle>🎯 Analysis & Recommendations</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="bg-blue-50 border border-blue-200 p-4 rounded">
              <h4 className="font-semibold text-blue-900 mb-2">Found PayPal Deposit Payments!</h4>
              <p className="text-blue-800 text-sm">
                Found {results.payments.data.length} completed PayPal deposit payment(s). 
                These should have corresponding escrow records.
              </p>
            </div>

            {results.escrow_holds?.data?.length === 0 && 
             results.paypal_escrow_holds?.data?.length === 0 && 
             results.escrow_transactions?.data?.length === 0 && (
              <div className="bg-red-50 border border-red-200 p-4 rounded">
                <h4 className="font-semibold text-red-900 mb-2">⚠️ Missing Escrow Records!</h4>
                <p className="text-red-800 text-sm">
                  PayPal payments were completed but no escrow records were created. 
                  This means the escrow creation step failed during payment processing.
                </p>
              </div>
            )}

            <div className="bg-green-50 border border-green-200 p-4 rounded">
              <h4 className="font-semibold text-green-900 mb-2">✅ Next Steps:</h4>
              <ul className="text-green-800 text-sm space-y-1">
                <li>1. Check which table has your escrow records</li>
                <li>2. Verify the manager escrow page is looking in the right tables</li>
                <li>3. Create missing escrow records if needed</li>
                <li>4. Ensure consistent escrow table usage across the system</li>
              </ul>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
