"use client";

import { useState, useEffect } from "react";
import { useOptimizedAuth } from "@/hooks/useOptimizedAuth";
import { supabase } from "@/lib/supabase";
import { use<PERSON>ara<PERSON>, useRouter } from "next/navigation";
import { But<PERSON> } from "@/components/ui/button";
import {
  ArrowLeft,
  Save,
  AlertCircle,
  CheckCircle,
  User,
  Mail,
  Phone,
  Briefcase,
  MapPin,
  Calendar,
  Clock,
  Shield,
  FileText,
  FolderKanban,
  CreditCard,
  XCircle,
  ArrowRight,
  Trash
} from "lucide-react";
import Link from "next/link";

type UserProfile = {
  id: string;
  full_name: string;
  email: string;
  role: 'client' | 'designer' | 'admin';
  phone: string | null;
  company: string | null;
  location: string | null;
  bio: string | null;
  website: string | null;
  avatar_url: string | null;
  specialization: string | null;
  years_experience: number | null;
  hourly_rate: number | null;
  created_at: string;
  updated_at: string | null;
  last_sign_in_at: string | null;
  is_active: boolean;
};

type UserStats = {
  project_count: number;
  completed_projects: number;
  active_projects: number;
  total_payments: number;
};

export default function UserDetail() {
  const { user: currentUser } = useOptimizedAuth();
  const params = useParams();
  const router = useRouter();
  const userId = params.id as string;

  const [profile, setProfile] = useState<UserProfile | null>(null);
  const [stats, setStats] = useState<UserStats>({
    project_count: 0,
    completed_projects: 0,
    active_projects: 0,
    total_payments: 0
  });
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [deleting, setDeleting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [deleteConfirmModal, setDeleteConfirmModal] = useState(false);

  useEffect(() => {
    if (currentUser && userId) {
      fetchUserData();
    }
  }, [currentUser, userId]);

  const fetchUserData = async () => {
    setLoading(true);
    try {
      // Fetch user profile
      const { data: profileData, error: profileError } = await supabase
        .from('profiles')
        .select('*')
        .eq('id', userId)
        .single();

      if (profileError) throw profileError;
      setProfile(profileData);

      // Fetch user statistics based on role
      if (profileData.role === 'client') {
        await fetchClientStats(userId);
      } else if (profileData.role === 'designer') {
        await fetchDesignerStats(userId);
      }
    } catch (error: Error | unknown) {
      console.error('Error fetching user data:', error);
      setError(error instanceof Error ? error.message : 'Failed to load user data');
    } finally {
      setLoading(false);
    }
  };

  const fetchClientStats = async (clientId: string) => {
    try {
      const [
        { count: projectCount, error: projectError },
        { count: completedCount, error: completedError },
        { count: activeCount, error: activeError }
      ] = await Promise.all([
        supabase.from('projects').select('*', { count: 'exact' }).eq('client_id', clientId),
        supabase.from('projects').select('*', { count: 'exact' }).eq('client_id', clientId).eq('status', 'completed'),
        supabase.from('projects').select('*', { count: 'exact' }).eq('client_id', clientId).in('status', ['active', 'in_progress'])
      ]);

      if (projectError) throw projectError;
      if (completedError) throw completedError;
      if (activeError) throw activeError;

      // Fetch payment data (placeholder for now)
      const totalPayments = 0; // This would be calculated from actual payment records

      setStats({
        project_count: projectCount || 0,
        completed_projects: completedCount || 0,
        active_projects: activeCount || 0,
        total_payments: totalPayments
      });
    } catch (error) {
      console.error('Error fetching client stats:', error);
    }
  };

  const fetchDesignerStats = async (designerId: string) => {
    try {
      const [
        { count: projectCount, error: projectError },
        { count: completedCount, error: completedError },
        { count: activeCount, error: activeError }
      ] = await Promise.all([
        supabase.from('projects').select('*', { count: 'exact' }).eq('designer_id', designerId),
        supabase.from('projects').select('*', { count: 'exact' }).eq('designer_id', designerId).eq('status', 'completed'),
        supabase.from('projects').select('*', { count: 'exact' }).eq('designer_id', designerId).in('status', ['active', 'in_progress'])
      ]);

      if (projectError) throw projectError;
      if (completedError) throw completedError;
      if (activeError) throw activeError;

      // Fetch payment data (placeholder for now)
      const totalPayments = 0; // This would be calculated from actual payment records

      setStats({
        project_count: projectCount || 0,
        completed_projects: completedCount || 0,
        active_projects: activeCount || 0,
        total_payments: totalPayments
      });
    } catch (error) {
      console.error('Error fetching designer stats:', error);
    }
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setProfile(prev => {
      if (!prev) return prev;
      return {
        ...prev,
        [name]: value
      };
    });
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!profile) return;
    
    setSaving(true);
    setError(null);
    setSuccess(null);
    
    try {
      // Update profile in database
      const { error } = await supabase
        .from('profiles')
        .update({
          full_name: profile.full_name,
          role: profile.role,
          phone: profile.phone,
          company: profile.company,
          location: profile.location,
          bio: profile.bio,
          website: profile.website,
          specialization: profile.specialization,
          years_experience: profile.years_experience,
          hourly_rate: profile.hourly_rate,
          is_active: profile.is_active,
          updated_at: new Date().toISOString()
        })
        .eq('id', userId);

      if (error) throw error;
      
      setSuccess('User profile updated successfully');
      
      // Clear success message after 3 seconds
      setTimeout(() => {
        setSuccess(null);
      }, 3000);
    } catch (error: Error | unknown) {
      console.error('Error updating profile:', error);
      setError(error instanceof Error ? error.message : 'Failed to update profile');
    } finally {
      setSaving(false);
    }
  };

  const toggleUserStatus = async () => {
    if (!profile) return;

    try {
      const newStatus = !profile.is_active;

      const { error } = await supabase
        .from('profiles')
        .update({
          is_active: newStatus,
          updated_at: new Date().toISOString()
        })
        .eq('id', userId);

      if (error) throw error;

      setProfile(prev => {
        if (!prev) return prev;
        return {
          ...prev,
          is_active: newStatus
        };
      });

      setSuccess(`User ${newStatus ? 'activated' : 'deactivated'} successfully`);

      // Clear success message after 3 seconds
      setTimeout(() => {
        setSuccess(null);
      }, 3000);
    } catch (error: Error | unknown) {
      console.error('Error toggling user status:', error);
      setError(error instanceof Error ? error.message : 'Failed to update user status');
    }
  };

  const handleDeleteUser = async () => {
    if (!profile) return;

    // Additional frontend validation
    if (profile.role === 'admin') {
      setError('Cannot delete admin users for security reasons');
      return;
    }

    if (profile.id === currentUser?.id) {
      setError('Cannot delete your own account');
      return;
    }

    setDeleting(true);
    setError(null);

    try {
      console.log('Attempting to delete user:', profile);

      // Get auth token
      const { data: { session }, error: sessionError } = await supabase.auth.getSession();
      if (sessionError || !session?.access_token) {
        throw new Error('Authentication required - Please log in again');
      }

      const response = await fetch(`/api/admin/users/${profile.id}/delete`, {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${session.access_token}`,
        },
      });

      console.log('Delete response status:', response.status);

      const data = await response.json();
      console.log('Delete response data:', data);

      if (!response.ok) {
        const errorMessage = data.error || `Failed to delete user (Status: ${response.status})`;
        console.error('Delete failed:', errorMessage);
        throw new Error(errorMessage);
      }

      // Success - redirect to users list
      setSuccess(data.message || 'User deleted successfully');

      // Redirect after a short delay
      setTimeout(() => {
        router.push('/admin/users');
      }, 2000);

    } catch (error: Error | unknown) {
      console.error('Error deleting user:', error);
      const errorMessage = error instanceof Error ? error.message : 'Failed to delete user - Unknown error';
      setError(`Delete failed: ${errorMessage}`);
    } finally {
      setDeleting(false);
      setDeleteConfirmModal(false);
    }
  };

  const openDeleteConfirmModal = () => {
    setDeleteConfirmModal(true);
  };

  const closeDeleteConfirmModal = () => {
    setDeleteConfirmModal(false);
  };

  const formatDate = (dateString: string | null) => {
    if (!dateString) return 'Never';
    return new Date(dateString).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric'
    });
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount);
  };

  if (loading) {
    return (
      <div className="p-8 flex justify-center items-center min-h-screen">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto"></div>
          <p className="mt-4 text-gray-500">Loading user data...</p>
        </div>
      </div>
    );
  }

  if (error && !profile) {
    return (
      <div className="p-8">
        <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg">
          <p className="flex items-center">
            <AlertCircle className="h-5 w-5 mr-2" />
            {error}
          </p>
          <div className="mt-4">
            <Link href="/admin/users">
              <Button variant="outline">
                Back to Users
              </Button>
            </Link>
          </div>
        </div>
      </div>
    );
  }

  if (!profile) {
    return (
      <div className="p-8">
        <div className="bg-yellow-50 border border-yellow-200 text-yellow-700 px-4 py-3 rounded-lg">
          <p className="flex items-center">
            <AlertCircle className="h-5 w-5 mr-2" />
            User not found
          </p>
          <div className="mt-4">
            <Link href="/admin/users">
              <Button variant="outline">
                Back to Users
              </Button>
            </Link>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="p-8">
      <div className="mb-8 flex items-center">
        <Link href="/admin/users" className="mr-4">
          <Button variant="ghost" className="p-0 h-auto">
            <ArrowLeft className="h-5 w-5" />
          </Button>
        </Link>
        <div>
          <h1 className="text-2xl font-bold">{profile.full_name}</h1>
          <p className="text-gray-500">{profile.email}</p>
        </div>
      </div>

      {error && (
        <div className="mb-6 bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg flex items-start">
          <AlertCircle className="h-5 w-5 mr-2 mt-0.5" />
          <span>{error}</span>
        </div>
      )}

      {success && (
        <div className="mb-6 bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded-lg flex items-start">
          <CheckCircle className="h-5 w-5 mr-2 mt-0.5" />
          <span>{success}</span>
        </div>
      )}

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        <div className="lg:col-span-2">
          <div className="bg-white rounded-lg shadow-md overflow-hidden">
            <div className="px-6 py-4 border-b">
              <h2 className="text-lg font-semibold">User Profile</h2>
            </div>
            <form onSubmit={handleSubmit} className="p-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label htmlFor="full_name" className="block text-sm font-medium text-gray-700 mb-1">
                    Full Name
                  </label>
                  <input
                    type="text"
                    id="full_name"
                    name="full_name"
                    value={profile.full_name}
                    onChange={handleChange}
                    className="w-full px-4 py-2 border rounded-md"
                  />
                </div>

                <div>
                  <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-1">
                    Email Address
                  </label>
                  <input
                    type="email"
                    id="email"
                    name="email"
                    value={profile.email}
                    disabled
                    className="w-full px-4 py-2 border rounded-md bg-gray-50"
                  />
                </div>

                <div>
                  <label htmlFor="role" className="block text-sm font-medium text-gray-700 mb-1">
                    Role
                  </label>
                  <select
                    id="role"
                    name="role"
                    value={profile.role}
                    onChange={handleChange}
                    className="w-full px-4 py-2 border rounded-md"
                  >
                    <option value="client">Client</option>
                    <option value="designer">Designer</option>
                    <option value="admin">Admin</option>
                  </select>
                </div>

                <div>
                  <label htmlFor="phone" className="block text-sm font-medium text-gray-700 mb-1">
                    Phone Number
                  </label>
                  <input
                    type="tel"
                    id="phone"
                    name="phone"
                    value={profile.phone || ''}
                    onChange={handleChange}
                    className="w-full px-4 py-2 border rounded-md"
                  />
                </div>

                <div>
                  <label htmlFor="company" className="block text-sm font-medium text-gray-700 mb-1">
                    Company
                  </label>
                  <input
                    type="text"
                    id="company"
                    name="company"
                    value={profile.company || ''}
                    onChange={handleChange}
                    className="w-full px-4 py-2 border rounded-md"
                  />
                </div>

                <div>
                  <label htmlFor="location" className="block text-sm font-medium text-gray-700 mb-1">
                    Location
                  </label>
                  <input
                    type="text"
                    id="location"
                    name="location"
                    value={profile.location || ''}
                    onChange={handleChange}
                    className="w-full px-4 py-2 border rounded-md"
                  />
                </div>

                <div>
                  <label htmlFor="website" className="block text-sm font-medium text-gray-700 mb-1">
                    Website
                  </label>
                  <input
                    type="url"
                    id="website"
                    name="website"
                    value={profile.website || ''}
                    onChange={handleChange}
                    className="w-full px-4 py-2 border rounded-md"
                  />
                </div>

                <div className="md:col-span-2">
                  <label htmlFor="bio" className="block text-sm font-medium text-gray-700 mb-1">
                    Bio
                  </label>
                  <textarea
                    id="bio"
                    name="bio"
                    value={profile.bio || ''}
                    onChange={handleChange}
                    rows={4}
                    className="w-full px-4 py-2 border rounded-md"
                  ></textarea>
                </div>

                {profile.role === 'designer' && (
                  <>
                    <div>
                      <label htmlFor="specialization" className="block text-sm font-medium text-gray-700 mb-1">
                        Specialization
                      </label>
                      <input
                        type="text"
                        id="specialization"
                        name="specialization"
                        value={profile.specialization || ''}
                        onChange={handleChange}
                        className="w-full px-4 py-2 border rounded-md"
                      />
                    </div>

                    <div>
                      <label htmlFor="years_experience" className="block text-sm font-medium text-gray-700 mb-1">
                        Years of Experience
                      </label>
                      <input
                        type="number"
                        id="years_experience"
                        name="years_experience"
                        value={profile.years_experience || ''}
                        onChange={handleChange}
                        min="0"
                        className="w-full px-4 py-2 border rounded-md"
                      />
                    </div>

                    <div>
                      <label htmlFor="hourly_rate" className="block text-sm font-medium text-gray-700 mb-1">
                        Hourly Rate (USD)
                      </label>
                      <input
                        type="number"
                        id="hourly_rate"
                        name="hourly_rate"
                        value={profile.hourly_rate || ''}
                        onChange={handleChange}
                        min="0"
                        className="w-full px-4 py-2 border rounded-md"
                      />
                    </div>
                  </>
                )}
              </div>

              <div className="mt-8 flex justify-end">
                <Button
                  type="button"
                  variant={profile.is_active ? "secondary" : "outline"}
                  onClick={toggleUserStatus}
                  className="mr-4"
                >
                  {profile.is_active ? 'Deactivate User' : 'Activate User'}
                </Button>
                <Button type="submit" disabled={saving} className="flex items-center">
                  {saving ? (
                    <span className="flex items-center">
                      <span className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></span>
                      Saving...
                    </span>
                  ) : (
                    <>
                      <Save className="h-4 w-4 mr-2" />
                      Save Changes
                    </>
                  )}
                </Button>
              </div>
            </form>
          </div>
        </div>

        <div className="space-y-8">
          {/* User Info Card */}
          <div className="bg-white rounded-lg shadow-md overflow-hidden">
            <div className="px-6 py-4 border-b">
              <h2 className="text-lg font-semibold">User Information</h2>
            </div>
            <div className="p-6">
              <div className="flex items-center justify-center mb-6">
                <div className="w-24 h-24 bg-gray-200 rounded-full flex items-center justify-center">
                  {profile.avatar_url ? (
                    <img
                      src={profile.avatar_url}
                      alt={profile.full_name}
                      className="w-24 h-24 rounded-full object-cover"
                    />
                  ) : (
                    <User className="h-12 w-12 text-gray-400" />
                  )}
                </div>
              </div>

              <div className="space-y-4">
                <div className="flex items-start">
                  <Shield className="h-5 w-5 text-gray-400 mt-0.5 mr-3" />
                  <div>
                    <p className="text-sm text-gray-500">Role</p>
                    <p className="font-medium">
                      {profile.role.charAt(0).toUpperCase() + profile.role.slice(1)}
                    </p>
                  </div>
                </div>

                <div className="flex items-start">
                  <Calendar className="h-5 w-5 text-gray-400 mt-0.5 mr-3" />
                  <div>
                    <p className="text-sm text-gray-500">Member Since</p>
                    <p className="font-medium">{formatDate(profile.created_at)}</p>
                  </div>
                </div>

                <div className="flex items-start">
                  <Clock className="h-5 w-5 text-gray-400 mt-0.5 mr-3" />
                  <div>
                    <p className="text-sm text-gray-500">Last Sign In</p>
                    <p className="font-medium">{formatDate(profile.last_sign_in_at)}</p>
                  </div>
                </div>

                <div className="flex items-start">
                  <CheckCircle className={`h-5 w-5 ${profile.is_active ? 'text-green-500' : 'text-red-500'} mt-0.5 mr-3`} />
                  <div>
                    <p className="text-sm text-gray-500">Status</p>
                    <p className="font-medium">{profile.is_active ? 'Active' : 'Inactive'}</p>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Stats Card */}
          <div className="bg-white rounded-lg shadow-md overflow-hidden">
            <div className="px-6 py-4 border-b">
              <h2 className="text-lg font-semibold">User Statistics</h2>
            </div>
            <div className="p-6">
              <div className="space-y-4">
                <div className="flex items-start">
                  <FolderKanban className="h-5 w-5 text-gray-400 mt-0.5 mr-3" />
                  <div>
                    <p className="text-sm text-gray-500">Total Projects</p>
                    <p className="font-medium">{stats.project_count}</p>
                  </div>
                </div>

                <div className="flex items-start">
                  <CheckCircle className="h-5 w-5 text-gray-400 mt-0.5 mr-3" />
                  <div>
                    <p className="text-sm text-gray-500">Completed Projects</p>
                    <p className="font-medium">{stats.completed_projects}</p>
                  </div>
                </div>

                <div className="flex items-start">
                  <Clock className="h-5 w-5 text-gray-400 mt-0.5 mr-3" />
                  <div>
                    <p className="text-sm text-gray-500">Active Projects</p>
                    <p className="font-medium">{stats.active_projects}</p>
                  </div>
                </div>

                <div className="flex items-start">
                  <CreditCard className="h-5 w-5 text-gray-400 mt-0.5 mr-3" />
                  <div>
                    <p className="text-sm text-gray-500">Total Payments</p>
                    <p className="font-medium">{formatCurrency(stats.total_payments)}</p>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Quick Actions */}
          <div className="bg-white rounded-lg shadow-md overflow-hidden">
            <div className="px-6 py-4 border-b">
              <h2 className="text-lg font-semibold">Quick Actions</h2>
            </div>
            <div className="p-6 space-y-3">
              {profile.role === 'client' && (
                <Link href={`/admin/projects?client=${profile.id}`}>
                  <Button variant="outline" className="w-full justify-between">
                    View Client Projects
                    <FolderKanban className="h-4 w-4 ml-2" />
                  </Button>
                </Link>
              )}
              
              {profile.role === 'designer' && (
                <Link href={`/admin/projects?designer=${profile.id}`}>
                  <Button variant="outline" className="w-full justify-between">
                    View Designer Projects
                    <FolderKanban className="h-4 w-4 ml-2" />
                  </Button>
                </Link>
              )}
              
              <Link href={`/admin/finance/transactions?user=${profile.id}`}>
                <Button variant="outline" className="w-full justify-between">
                  View Payment History
                  <CreditCard className="h-4 w-4 ml-2" />
                </Button>
              </Link>
              
              <Button
                variant={profile.is_active ? "secondary" : "default"}
                className="w-full justify-between"
                onClick={toggleUserStatus}
              >
                {profile.is_active ? 'Deactivate Account' : 'Activate Account'}
                {profile.is_active ? (
                  <AlertCircle className="h-4 w-4 ml-2" />
                ) : (
                  <CheckCircle className="h-4 w-4 ml-2" />
                )}
              </Button>

              <Button
                variant="destructive"
                className="w-full justify-between"
                onClick={openDeleteConfirmModal}
                disabled={deleting || profile.role === 'admin' || profile.id === currentUser?.id}
                title={
                  profile.role === 'admin'
                    ? 'Cannot delete admin users for security reasons'
                    : profile.id === currentUser?.id
                    ? 'Cannot delete your own account'
                    : 'Delete user permanently'
                }
              >
                {deleting ? (
                  <>
                    <span className="flex items-center">
                      <span className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></span>
                      Deleting...
                    </span>
                  </>
                ) : (
                  <>
                    Delete User Permanently
                    <Trash className="h-4 w-4 ml-2" />
                  </>
                )}
              </Button>
            </div>
          </div>
        </div>
      </div>

      {/* Delete Confirmation Modal */}
      {deleteConfirmModal && profile && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg shadow-xl max-w-md w-full mx-4">
            <div className="px-6 py-4 border-b border-gray-200">
              <h2 className="text-xl font-semibold text-gray-900 flex items-center">
                <AlertCircle className="h-5 w-5 text-red-600 mr-2" />
                Delete User
              </h2>
            </div>

            <div className="p-6">
              <p className="text-gray-700 mb-4">
                Are you sure you want to permanently delete this user? This action cannot be undone.
              </p>

              {/* Show warning for admin users */}
              {profile.role === 'admin' && (
                <div className="bg-red-50 border border-red-200 text-red-700 px-3 py-2 rounded-md mb-4">
                  <p className="text-sm font-medium">⚠️ Cannot delete admin users for security reasons</p>
                </div>
              )}

              {/* Show warning for self-deletion */}
              {profile.id === currentUser?.id && (
                <div className="bg-red-50 border border-red-200 text-red-700 px-3 py-2 rounded-md mb-4">
                  <p className="text-sm font-medium">⚠️ Cannot delete your own account</p>
                </div>
              )}

              <div className="bg-gray-50 p-4 rounded-md">
                <h4 className="font-medium text-gray-900 mb-2">User Details:</h4>
                <div className="text-sm text-gray-600 space-y-1">
                  <p><strong>Name:</strong> {profile.full_name}</p>
                  <p><strong>Email:</strong> {profile.email}</p>
                  <p><strong>Role:</strong> {profile.role.charAt(0).toUpperCase() + profile.role.slice(1)}</p>
                  <p><strong>Projects:</strong> {stats.project_count} total</p>
                </div>
              </div>
            </div>

            <div className="px-6 py-4 border-t border-gray-200 flex justify-end space-x-3">
              <Button
                variant="outline"
                onClick={closeDeleteConfirmModal}
                disabled={deleting}
              >
                Cancel
              </Button>
              <Button
                onClick={handleDeleteUser}
                disabled={
                  deleting ||
                  profile.role === 'admin' ||
                  profile.id === currentUser?.id
                }
                className={`flex items-center ${
                  profile.role === 'admin' || profile.id === currentUser?.id
                    ? 'bg-gray-400 cursor-not-allowed text-white'
                    : 'bg-red-600 hover:bg-red-700 text-white'
                }`}
              >
                {deleting ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                    Deleting...
                  </>
                ) : (
                  <>
                    <Trash className="h-4 w-4 mr-2" />
                    {profile.role === 'admin' || profile.id === currentUser?.id
                      ? 'Cannot Delete'
                      : 'Delete User'
                    }
                  </>
                )}
              </Button>
            </div>
          </div>
        </div>
      )}
    </div>
    );
  }