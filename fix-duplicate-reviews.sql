-- =====================================================
-- FIX DUPLICATE QUALITY REVIEWS
-- This identifies and cleans up duplicate reviews for the same submission
-- =====================================================

-- Step 1: Identify submissions with multiple reviews
SELECT 
    'Duplicate Reviews Analysis' as analysis,
    submission_id,
    COUNT(*) as review_count,
    STRING_AGG(id::text, ', ') as review_ids,
    STRING_AGG(status, ', ') as statuses,
    MIN(created_at) as first_created,
    MAX(created_at) as last_created
FROM quality_reviews_new 
WHERE submission_id IS NOT NULL
GROUP BY submission_id
HAVING COUNT(*) > 1
ORDER BY review_count DESC;

-- Step 2: Check the specific problematic submission
SELECT 
    'Specific Submission Duplicates' as analysis,
    id as review_id,
    submission_id,
    status,
    created_at,
    updated_at,
    overall_score,
    feedback,
    ROW_NUMBER() OVER (PARTITION BY submission_id ORDER BY created_at DESC) as row_num
FROM quality_reviews_new 
WHERE submission_id = '0d167e18-75b9-4cfe-8a37-b1e8f539e3f0'
ORDER BY created_at DESC;

-- Step 3: Show which reviews have feedback (to preserve important data)
SELECT 
    'Reviews with Feedback' as analysis,
    qr.id as review_id,
    qr.submission_id,
    qr.status,
    qr.overall_score,
    qr.created_at,
    COUNT(qf.id) as feedback_count,
    CASE 
        WHEN COUNT(qf.id) > 0 THEN '⚠️ Has feedback - preserve'
        ELSE '✅ No feedback - safe to remove'
    END as preservation_status
FROM quality_reviews_new qr
LEFT JOIN quality_feedback qf ON qr.id = qf.review_id
WHERE qr.submission_id = '0d167e18-75b9-4cfe-8a37-b1e8f539e3f0'
GROUP BY qr.id, qr.submission_id, qr.status, qr.overall_score, qr.created_at
ORDER BY qr.created_at DESC;

-- Step 4: Safe cleanup strategy (commented out for safety)
-- This would remove duplicate reviews, keeping the most recent one
-- ONLY run this after reviewing the above queries!

/*
-- DANGER: This will delete duplicate reviews!
-- Review the analysis above before uncommenting and running this

WITH ranked_reviews AS (
    SELECT 
        id,
        submission_id,
        ROW_NUMBER() OVER (PARTITION BY submission_id ORDER BY created_at DESC) as row_num
    FROM quality_reviews_new 
    WHERE submission_id IS NOT NULL
),
reviews_to_delete AS (
    SELECT id 
    FROM ranked_reviews 
    WHERE row_num > 1
    AND id NOT IN (
        -- Preserve reviews that have feedback
        SELECT DISTINCT qr.id 
        FROM quality_reviews_new qr
        INNER JOIN quality_feedback qf ON qr.id = qf.review_id
    )
)
DELETE FROM quality_reviews_new 
WHERE id IN (SELECT id FROM reviews_to_delete);
*/

-- Step 5: Alternative approach - Mark duplicates as cancelled instead of deleting
-- This is safer as it preserves data

/*
-- SAFER: Mark duplicate reviews as cancelled instead of deleting
WITH ranked_reviews AS (
    SELECT 
        id,
        submission_id,
        ROW_NUMBER() OVER (PARTITION BY submission_id ORDER BY created_at DESC) as row_num
    FROM quality_reviews_new 
    WHERE submission_id IS NOT NULL
)
UPDATE quality_reviews_new 
SET 
    status = 'cancelled',
    updated_at = NOW()
WHERE id IN (
    SELECT id 
    FROM ranked_reviews 
    WHERE row_num > 1
)
AND status != 'cancelled';
*/

-- Step 6: Verify the fix would work
SELECT 
    'Post-Cleanup Preview' as analysis,
    submission_id,
    COUNT(*) as total_reviews,
    COUNT(*) FILTER (WHERE status != 'cancelled') as active_reviews,
    STRING_AGG(
        CASE WHEN status != 'cancelled' THEN id::text END, 
        ', '
    ) as active_review_ids
FROM quality_reviews_new 
WHERE submission_id = '0d167e18-75b9-4cfe-8a37-b1e8f539e3f0'
GROUP BY submission_id;

-- Step 7: Show recommended action
SELECT 
    'Recommended Action' as recommendation,
    'Run the analysis queries above first' as step1,
    'Check which reviews have feedback to preserve' as step2,
    'Use the SAFER approach to mark duplicates as cancelled' as step3,
    'Test the application after cleanup' as step4,
    'The application now handles multiple reviews by taking the most recent' as step5;
