#!/usr/bin/env node

// Test script to verify Cloudflare R2 configuration and file access
console.log('🧪 Testing Cloudflare R2 Configuration...\n');

// Check environment variables
console.log('Environment Variables:');
console.log('CLOUDFLARE_R2_PUBLIC_URL:', process.env.CLOUDFLARE_R2_PUBLIC_URL);
console.log('NEXT_PUBLIC_CLOUDFLARE_R2_PUBLIC_URL:', process.env.NEXT_PUBLIC_CLOUDFLARE_R2_PUBLIC_URL);
console.log('NODE_ENV:', process.env.NODE_ENV);
console.log('');

// Test URL construction
const testFileKey = 'projects/test-project/sample-file.jpg';
const publicUrl = process.env.NEXT_PUBLIC_CLOUDFLARE_R2_PUBLIC_URL || process.env.CLOUDFLARE_R2_PUBLIC_URL;

if (publicUrl) {
  const constructedUrl = `${publicUrl}/${testFileKey}`;
  console.log('✅ URL Construction Test:');
  console.log('Base URL:', publicUrl);
  console.log('Test Key:', testFileKey);
  console.log('Constructed URL:', constructedUrl);
  console.log('');
  
  // Test URL accessibility (Note: This is just URL construction, not actual access test)
  console.log('🔗 Example URLs that would be generated:');
  const sampleFiles = [
    'submissions/abc123/design-v1.pdf',
    'submissions/def456/floor-plan.dwg',
    'submissions/ghi789/render.jpg'
  ];
  
  sampleFiles.forEach((file, index) => {
    console.log(`  ${index + 1}. ${publicUrl}/${file}`);
  });
} else {
  console.log('❌ No public URL configured!');
  console.log('Please set NEXT_PUBLIC_CLOUDFLARE_R2_PUBLIC_URL in your .env.local file');
}

console.log('\n🔍 For debugging in browser console, check for logs starting with:');
console.log('  - 🔧 [DEBUG] Cloudflare R2 Configuration');
console.log('  - 🔍 [FILE ANNOTATIONS] Raw file');
console.log('  - ✅ [FILE ANNOTATIONS] Using/Constructed');
console.log('  - ⚠️ [FILE ANNOTATIONS] No suitable URL property found');
