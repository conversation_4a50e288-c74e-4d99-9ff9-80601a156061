import { supabase } from '@/lib/supabase';

interface MilestonePaymentData {
  milestoneId: string;
  projectId: string;
  clientId: string;
  amount: number; // in cents
  milestoneTitle: string;
  projectTitle: string;
  designerName: string;
}

interface PaymentMethod {
  id: string;
  payment_type: string;
  paypal_email?: string;
  card_brand?: string;
  last_four?: string;
  is_default: boolean;
}

/**
 * Create a quick payment link for milestone payment
 */
export async function createMilestonePaymentLink(data: MilestonePaymentData): Promise<string> {
  try {
    // Get authentication token
    const { data: { session } } = await supabase.auth.getSession();
    if (!session?.access_token) {
      throw new Error('Authentication required');
    }

    // Create payment using unified API that respects user's default payment method
    const response = await fetch('/api/payments/milestone/create', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${session.access_token}`
      },
      body: JSON.stringify({
        milestoneId: data.milestoneId,
        projectId: data.projectId,
        clientId: data.clientId,
        amount: data.amount,
        description: `Payment for milestone: ${data.milestoneTitle}`
      }),
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || 'Failed to create payment link');
    }

    const paymentData = await response.json();

    // Handle different payment methods
    if (paymentData.paymentMethod === 'paypal') {
      return paymentData.approvalUrl;
    } else if (paymentData.paymentMethod === 'tappay' || paymentData.paymentMethod === 'clickpay') {
      return paymentData.redirectUrl;
    } else {
      // Fallback to approval URL for backward compatibility
      return paymentData.approvalUrl || paymentData.redirectUrl;
    }

  } catch (error) {
    console.error('Error creating milestone payment link:', error);
    throw error;
  }
}

/**
 * Send payment notification to client when milestone is approved
 */
export async function sendMilestonePaymentNotification(
  milestoneId: string,
  projectId: string,
  clientId: string
): Promise<void> {
  try {
    // Get milestone and project data
    const { data: milestone, error: milestoneError } = await supabase
      .from('project_milestones')
      .select(`
        id,
        title,
        amount,
        status,
        project:projects!project_id(
          id,
          title,
          designer:profiles!designer_id(
            id,
            full_name
          )
        )
      `)
      .eq('id', milestoneId)
      .single();

    if (milestoneError || !milestone) {
      throw new Error('Milestone not found');
    }

    // Get client's preferred payment method
    const { data: paymentMethods, error: paymentError } = await supabase
      .from('payment_methods')
      .select('*')
      .eq('user_id', clientId)
      .order('is_default', { ascending: false });

    if (paymentError) {
      console.error('Error fetching payment methods:', paymentError);
    }

    const preferredPaymentMethod = paymentMethods?.[0];
    const hasPayPal = paymentMethods?.some(pm => pm.payment_type === 'paypal');

    // Create payment link
    const paymentLink = await createMilestonePaymentLink({
      milestoneId,
      projectId,
      clientId,
      amount: Math.round(milestone.amount * 100), // Convert to cents
      milestoneTitle: milestone.title,
      projectTitle: milestone.project.title,
      designerName: milestone.project.designer.full_name
    });

    // Create notification
    const notificationContent = hasPayPal 
      ? `Milestone "${milestone.title}" has been completed and is ready for payment. Click to pay with your saved PayPal account.`
      : `Milestone "${milestone.title}" has been completed and is ready for payment. Click to pay with PayPal.`;

    const { error: notificationError } = await supabase
      .from('notifications')
      .insert({
        user_id: clientId,
        type: 'payment_required',
        title: `Payment Required: ${milestone.title}`,
        content: notificationContent,
        link: paymentLink,
        metadata: {
          milestone_id: milestoneId,
          project_id: projectId,
          amount: milestone.amount,
          payment_link: paymentLink,
          payment_method: preferredPaymentMethod?.payment_type || 'paypal'
        },
        read: false
      });

    if (notificationError) {
      console.error('Error creating payment notification:', notificationError);
      throw notificationError;
    }

    console.log(`Payment notification sent for milestone ${milestoneId}`);

  } catch (error) {
    console.error('Error sending milestone payment notification:', error);
    throw error;
  }
}

/**
 * Get client's payment methods for display
 */
export async function getClientPaymentMethods(clientId: string): Promise<PaymentMethod[]> {
  const { data: paymentMethods, error } = await supabase
    .from('payment_methods')
    .select('id, payment_type, paypal_email, card_brand, last_four, is_default')
    .eq('user_id', clientId)
    .order('is_default', { ascending: false });

  if (error) {
    console.error('Error fetching payment methods:', error);
    return [];
  }

  return paymentMethods || [];
}

/**
 * Check if client has PayPal payment method
 */
export async function clientHasPayPal(clientId: string): Promise<boolean> {
  const { data: paypalMethods, error } = await supabase
    .from('payment_methods')
    .select('id')
    .eq('user_id', clientId)
    .eq('payment_type', 'paypal')
    .limit(1);

  if (error) {
    console.error('Error checking PayPal payment methods:', error);
    return false;
  }

  return (paypalMethods?.length || 0) > 0;
}

/**
 * Format payment method for display
 */
export function formatPaymentMethod(paymentMethod: PaymentMethod): string {
  if (paymentMethod.payment_type === 'paypal') {
    return `PayPal (${paymentMethod.paypal_email})`;
  } else if (paymentMethod.payment_type === 'card') {
    return `${paymentMethod.card_brand?.toUpperCase()} •••• ${paymentMethod.last_four}`;
  }
  return 'Unknown payment method';
}

/**
 * Create quick payment button data
 */
export function createQuickPaymentData(
  milestoneId: string,
  projectId: string,
  amount: number,
  title: string
) {
  return {
    milestoneId,
    projectId,
    amount: Math.round(amount * 100), // Convert to cents
    description: `Payment for milestone: ${title}`,
    paymentType: 'milestone' as const
  };
}

/**
 * Process milestone approval and trigger payment notification
 */
export async function processMilestoneApproval(
  milestoneId: string,
  projectId: string,
  clientId: string
): Promise<void> {
  try {
    // Update milestone status to approved
    const { error: updateError } = await supabase
      .from('project_milestones')
      .update({ 
        status: 'approved',
        approved_at: new Date().toISOString()
      })
      .eq('id', milestoneId);

    if (updateError) {
      throw updateError;
    }

    // Send payment notification
    await sendMilestonePaymentNotification(milestoneId, projectId, clientId);

    console.log(`Milestone ${milestoneId} approved and payment notification sent`);

  } catch (error) {
    console.error('Error processing milestone approval:', error);
    throw error;
  }
}
