#!/usr/bin/env node

/**
 * Environment Variables Sync Script
 * 
 * This script helps you:
 * 1. Fetch production environment variables from Vercel
 * 2. Compare them with your local .env.local file
 * 3. Generate a production-ready environment file for import
 * 4. Identify missing or different variables
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

// Configuration
const LOCAL_ENV_FILE = '.env.local';
const OUTPUT_FILE = 'production.env';
const COMPARISON_FILE = 'env-comparison.json';

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
  white: '\x1b[37m'
};

function log(message, color = 'white') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function parseEnvFile(filePath) {
  const envVars = {};
  
  if (!fs.existsSync(filePath)) {
    log(`Warning: ${filePath} not found`, 'yellow');
    return envVars;
  }

  const content = fs.readFileSync(filePath, 'utf8');
  const lines = content.split('\n');

  for (const line of lines) {
    const trimmedLine = line.trim();
    
    // Skip empty lines, comments, and commands
    if (!trimmedLine || trimmedLine.startsWith('#') || trimmedLine.startsWith('npx')) {
      continue;
    }

    const equalIndex = trimmedLine.indexOf('=');
    if (equalIndex > 0) {
      const key = trimmedLine.substring(0, equalIndex).trim();
      const value = trimmedLine.substring(equalIndex + 1).trim();
      
      // Remove quotes if present
      const cleanValue = value.replace(/^["']|["']$/g, '');
      envVars[key] = cleanValue;
    }
  }

  return envVars;
}

function fetchVercelEnvVars() {
  try {
    log('Fetching production environment variables from Vercel...', 'blue');

    // Fetch production environment variables
    const result = execSync('npx vercel env ls production', {
      encoding: 'utf8',
      stdio: ['pipe', 'pipe', 'pipe']
    });

    const envVars = {};

    // Parse the table output from Vercel CLI
    const lines = result.split('\n');
    let dataStarted = false;

    for (const line of lines) {
      const trimmedLine = line.trim();

      // Skip header lines until we find the data section
      if (trimmedLine.includes('name') && trimmedLine.includes('value') && trimmedLine.includes('environments')) {
        dataStarted = true;
        continue;
      }

      // Skip empty lines and separator lines
      if (!dataStarted || !trimmedLine || trimmedLine.startsWith('-') || trimmedLine.startsWith('=')) {
        continue;
      }

      // Parse environment variable lines
      // Format: name    value    environments    created
      const parts = trimmedLine.split(/\s+/);
      if (parts.length >= 4) {
        const name = parts[0];
        const value = parts[1];
        const environments = parts[2];

        // Only include production environment variables
        if (environments.toLowerCase().includes('production')) {
          // Note: Vercel encrypts values, so we'll mark them as encrypted
          envVars[name] = value === 'Encrypted' ? '[ENCRYPTED]' : value;
        }
      }
    }

    log(`✓ Found ${Object.keys(envVars).length} production environment variables`, 'green');
    log('Note: Vercel encrypts environment variable values for security', 'yellow');
    return envVars;

  } catch (error) {
    log('Error fetching Vercel environment variables:', 'red');
    log('Make sure you are logged in to Vercel CLI and have access to the project', 'yellow');
    log('Run: npx vercel login', 'cyan');

    if (error.message.includes('not found')) {
      log('Vercel CLI not found. Install it with: npm i -g vercel', 'yellow');
    }

    throw error;
  }
}

function compareEnvironments(local, production) {
  const comparison = {
    onlyLocal: [],
    onlyProduction: [],
    different: [],
    same: [],
    existsInBoth: []
  };

  const allKeys = new Set([...Object.keys(local), ...Object.keys(production)]);

  for (const key of allKeys) {
    const localValue = local[key];
    const prodValue = production[key];

    if (localValue && prodValue) {
      // Since Vercel encrypts values, we can't compare actual values
      // We'll just note that the variable exists in both environments
      if (prodValue === '[ENCRYPTED]') {
        comparison.existsInBoth.push({
          key,
          local: localValue,
          production: 'Encrypted in Vercel'
        });
      } else if (localValue === prodValue) {
        comparison.same.push({ key, value: localValue });
      } else {
        comparison.different.push({
          key,
          local: localValue,
          production: prodValue
        });
      }
    } else if (localValue && !prodValue) {
      comparison.onlyLocal.push({ key, value: localValue });
    } else if (!localValue && prodValue) {
      comparison.onlyProduction.push({ key, value: prodValue });
    }
  }

  return comparison;
}

function generateProductionEnv(local, production, comparison) {
  const lines = [];
  lines.push('# Production Environment Variables');
  lines.push('# Generated by env-sync script');
  lines.push(`# Generated on: ${new Date().toISOString()}`);
  lines.push('# Note: Vercel encrypts environment variable values for security');
  lines.push('');

  // Add variables that exist in both environments
  if (comparison.existsInBoth.length > 0) {
    lines.push('# Variables that exist in both local and production (using local values)');
    lines.push('# These variables are encrypted in Vercel for security');
    for (const item of comparison.existsInBoth) {
      lines.push(`${item.key}=${item.local}`);
    }
    lines.push('');
  }

  // Add variables that match exactly
  if (comparison.same.length > 0) {
    lines.push('# Variables that match between local and production');
    for (const item of comparison.same) {
      lines.push(`${item.key}=${item.value}`);
    }
    lines.push('');
  }

  // Add variables that are different (use local values but add comments)
  if (comparison.different.length > 0) {
    lines.push('# Variables that differ between local and production (using local values)');
    for (const item of comparison.different) {
      lines.push(`# Production value: ${item.production}`);
      lines.push(`${item.key}=${item.local}`);
    }
    lines.push('');
  }

  // Add variables only in local
  if (comparison.onlyLocal.length > 0) {
    lines.push('# Variables only in local environment (new variables to add to production)');
    for (const item of comparison.onlyLocal) {
      lines.push(`${item.key}=${item.value}`);
    }
    lines.push('');
  }

  // Add variables only in production as comments for reference
  if (comparison.onlyProduction.length > 0) {
    lines.push('# Variables only in production (commented out - review if needed locally)');
    for (const item of comparison.onlyProduction) {
      lines.push(`# ${item.key}=${item.value}`);
    }
    lines.push('');
  }

  return lines.join('\n');
}

function displayComparison(comparison) {
  log('\n=== Environment Variables Comparison ===', 'cyan');

  log(`\n🔐 Exist in both environments (encrypted in Vercel): ${comparison.existsInBoth.length}`, 'green');
  if (comparison.existsInBoth.length > 0) {
    comparison.existsInBoth.forEach(item => {
      log(`  ${item.key}`, 'white');
    });
  }

  log(`\n✓ Same values in both environments: ${comparison.same.length}`, 'green');
  if (comparison.same.length > 0) {
    comparison.same.forEach(item => {
      log(`  ${item.key}`, 'white');
    });
  }

  log(`\n⚠ Different values: ${comparison.different.length}`, 'yellow');
  if (comparison.different.length > 0) {
    comparison.different.forEach(item => {
      log(`  ${item.key}:`, 'white');
      log(`    Local: ${item.local.substring(0, 50)}${item.local.length > 50 ? '...' : ''}`, 'cyan');
      log(`    Prod:  ${item.production.substring(0, 50)}${item.production.length > 50 ? '...' : ''}`, 'magenta');
    });
  }

  log(`\n📝 Only in local (new variables): ${comparison.onlyLocal.length}`, 'blue');
  if (comparison.onlyLocal.length > 0) {
    comparison.onlyLocal.forEach(item => {
      log(`  ${item.key}`, 'white');
    });
  }

  log(`\n🔍 Only in production: ${comparison.onlyProduction.length}`, 'red');
  if (comparison.onlyProduction.length > 0) {
    comparison.onlyProduction.forEach(item => {
      log(`  ${item.key}`, 'white');
    });
  }
}

async function main() {
  try {
    log('🚀 Starting environment variables sync...', 'cyan');
    
    // Parse local environment file
    log('\n📖 Reading local environment file...', 'blue');
    const localEnv = parseEnvFile(LOCAL_ENV_FILE);
    log(`✓ Found ${Object.keys(localEnv).length} local environment variables`, 'green');

    // Fetch production environment variables
    const productionEnv = fetchVercelEnvVars();

    // Compare environments
    log('\n🔍 Comparing environments...', 'blue');
    const comparison = compareEnvironments(localEnv, productionEnv);

    // Display comparison
    displayComparison(comparison);

    // Generate production environment file
    log('\n📝 Generating production environment file...', 'blue');
    const productionEnvContent = generateProductionEnv(localEnv, productionEnv, comparison);
    
    fs.writeFileSync(OUTPUT_FILE, productionEnvContent);
    log(`✓ Production environment file saved to: ${OUTPUT_FILE}`, 'green');

    // Save comparison data
    fs.writeFileSync(COMPARISON_FILE, JSON.stringify(comparison, null, 2));
    log(`✓ Comparison data saved to: ${COMPARISON_FILE}`, 'green');

    // Summary
    log('\n=== Summary ===', 'cyan');
    log(`📁 Files generated:`, 'white');
    log(`  • ${OUTPUT_FILE} - Ready for Vercel import`, 'green');
    log(`  • ${COMPARISON_FILE} - Detailed comparison data`, 'blue');
    
    log('\n📋 Next steps:', 'yellow');
    log('1. Review the generated production.env file', 'white');
    log('2. Update any values that need to be different for production', 'white');
    log('3. Import to Vercel using: npx vercel env add < production.env', 'white');
    log('4. Or use the Vercel dashboard to import the file', 'white');

  } catch (error) {
    log(`\n❌ Error: ${error.message}`, 'red');
    process.exit(1);
  }
}

// Run the script
if (require.main === module) {
  main();
}

module.exports = { parseEnvFile, compareEnvironments, generateProductionEnv };
