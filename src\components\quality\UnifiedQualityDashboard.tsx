"use client";

import React, { useState, useEffect } from "react";
import { useOptimizedAuth } from "@/hooks/useOptimizedAuth";
import { supabase } from "@/lib/supabase";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import {
  Clock,
  AlertTriangle,
  CheckCircle,
  XCircle,
  Eye,
  Star,
  FileText,
  User,
  Calendar,
  TrendingUp,
  RefreshCw,
  Filter,
  Search
} from "lucide-react";

interface QualityReview {
  id: string;
  project_id: string;
  submission_id: string;
  designer_id: string;
  status: 'pending' | 'in_review' | 'approved' | 'rejected';
  priority: 'low' | 'normal' | 'high' | 'urgent';
  overall_score?: number;
  sla_deadline: string;
  created_at: string;
  reviewed_at?: string;
  projects?: {
    title: string;
    client_profile?: {
      full_name: string;
    };
  };
  designer_profile?: {
    full_name: string;
    email: string;
  };
  project_submissions?: {
    files: any[];
    description: string;
  };
}

interface QualityStats {
  total_reviews: number;
  pending_reviews: number;
  in_review: number;
  completed_today: number;
  overdue_count: number;
  average_score: number;
  sla_compliance: number;
}

export default function UnifiedQualityDashboard() {
  const { user, profile } = useOptimizedAuth();
  const [reviews, setReviews] = useState<QualityReview[]>([]);
  const [stats, setStats] = useState<QualityStats>({
    total_reviews: 0,
    pending_reviews: 0,
    in_review: 0,
    completed_today: 0,
    overdue_count: 0,
    average_score: 0,
    sla_compliance: 0
  });
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState('pending');
  const [searchTerm, setSearchTerm] = useState('');
  const [priorityFilter, setPriorityFilter] = useState('all');

  useEffect(() => {
    if (user && profile?.role === 'quality_team') {
      fetchData();
    }
  }, [user, profile]); // Remove activeTab dependency to prevent reload on tab change

  const fetchData = async () => {
    setLoading(true);
    try {
      await Promise.all([
        fetchReviews(),
        fetchStats()
      ]);
    } catch (error) {
      console.error('Error fetching quality dashboard data:', error);
    } finally {
      setLoading(false);
    }
  };

  const fetchReviews = async () => {
    try {
      console.log('🔍 [QUALITY DASHBOARD] Fetching all reviews...');

      // Fetch all reviews at once for client-side filtering
      const statusFilter = ['pending', 'in_review', 'approved', 'rejected'];

      const { data, error } = await supabase
        .from('quality_reviews_new')
        .select(`
          id,
          project_id,
          submission_id,
          designer_id,
          status,
          priority,
          overall_score,
          sla_deadline,
          created_at,
          reviewed_at,
          projects!project_id (
            title,
            client_id,
            client_profile:profiles!client_id (
              full_name
            )
          ),
          designer_profile:profiles!designer_id (
            full_name,
            email
          ),
          project_submissions!submission_id (
            files,
            description
          )
        `)
        .in('status', statusFilter)
        .order('created_at', { ascending: false })
        .limit(50);

      if (error) throw error;

      console.log('✅ [QUALITY DASHBOARD] Reviews fetched:', data?.length || 0);

      // Debug the first review structure
      if (data && data.length > 0) {
        console.log('🔍 [QUALITY DASHBOARD] First review structure:', {
          projects: data[0].projects,
          designer_profile: data[0].designer_profile,
          client_profile: data[0].projects?.client_profile
        });
      }

      setReviews(data || []);
    } catch (error) {
      console.error('❌ [QUALITY DASHBOARD] Error fetching reviews:', error);
    }
  };

  const fetchStats = async () => {
    try {
      const today = new Date().toISOString().split('T')[0];
      
      // Get basic counts
      const { data: allReviews, error } = await supabase
        .from('quality_reviews_new')
        .select('id, status, overall_score, sla_deadline, reviewed_at, created_at');

      if (error) throw error;

      const now = new Date();
      const totalReviews = allReviews?.length || 0;
      const pendingReviews = allReviews?.filter(r => r.status === 'pending').length || 0;
      const inReview = allReviews?.filter(r => r.status === 'in_review').length || 0;
      const completedToday = allReviews?.filter(r => 
        r.reviewed_at && r.reviewed_at.startsWith(today)
      ).length || 0;
      
      const overdueCount = allReviews?.filter(r => 
        ['pending', 'in_review'].includes(r.status) && 
        new Date(r.sla_deadline) < now
      ).length || 0;

      const reviewsWithScores = allReviews?.filter(r => r.overall_score) || [];
      const averageScore = reviewsWithScores.length > 0
        ? reviewsWithScores.reduce((sum, r) => sum + (r.overall_score || 0), 0) / reviewsWithScores.length
        : 0;

      const slaCompliance = totalReviews > 0 
        ? Math.round(((totalReviews - overdueCount) / totalReviews) * 100)
        : 100;

      setStats({
        total_reviews: totalReviews,
        pending_reviews: pendingReviews,
        in_review: inReview,
        completed_today: completedToday,
        overdue_count: overdueCount,
        average_score: Math.round(averageScore * 10) / 10,
        sla_compliance: slaCompliance
      });

      console.log('✅ [QUALITY DASHBOARD] Stats calculated:', {
        totalReviews,
        pendingReviews,
        overdueCount,
        slaCompliance
      });
    } catch (error) {
      console.error('❌ [QUALITY DASHBOARD] Error fetching stats:', error);
    }
  };

  const getStatusBadge = (status: string) => {
    const variants = {
      pending: 'bg-yellow-100 text-yellow-800',
      in_review: 'bg-blue-100 text-blue-800',
      approved: 'bg-green-100 text-green-800',
      rejected: 'bg-red-100 text-red-800'
    };
    return variants[status as keyof typeof variants] || 'bg-gray-100 text-gray-800';
  };

  const getPriorityBadge = (priority: string) => {
    const variants = {
      low: 'bg-gray-100 text-gray-800',
      normal: 'bg-blue-100 text-blue-800',
      high: 'bg-orange-100 text-orange-800',
      urgent: 'bg-red-100 text-red-800'
    };
    return variants[priority as keyof typeof variants] || 'bg-gray-100 text-gray-800';
  };

  const getSLAStatus = (deadline: string) => {
    const now = new Date();
    const slaDeadline = new Date(deadline);
    const hoursRemaining = (slaDeadline.getTime() - now.getTime()) / (1000 * 60 * 60);

    if (hoursRemaining < 0) {
      return { status: 'overdue', color: 'bg-red-100 text-red-800', text: 'Overdue' };
    } else if (hoursRemaining < 2) {
      return { status: 'critical', color: 'bg-red-100 text-red-800', text: 'Due Soon' };
    } else if (hoursRemaining < 24) {
      return { status: 'warning', color: 'bg-orange-100 text-orange-800', text: 'Due Today' };
    } else {
      return { status: 'ok', color: 'bg-green-100 text-green-800', text: 'On Track' };
    }
  };

  const formatTimeRemaining = (deadline: string) => {
    const now = new Date();
    const slaDeadline = new Date(deadline);
    const hoursRemaining = Math.round((slaDeadline.getTime() - now.getTime()) / (1000 * 60 * 60));

    if (hoursRemaining < 0) {
      return `${Math.abs(hoursRemaining)}h overdue`;
    } else if (hoursRemaining < 24) {
      return `${hoursRemaining}h remaining`;
    } else {
      const daysRemaining = Math.round(hoursRemaining / 24);
      return `${daysRemaining}d remaining`;
    }
  };

  const filteredReviews = reviews.filter(review => {
    // Filter by active tab
    const matchesTab = activeTab === 'all' || review.status === activeTab;

    // Filter by search term
    const matchesSearch = !searchTerm ||
      review.projects?.title?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      review.designer_profile?.full_name?.toLowerCase().includes(searchTerm.toLowerCase());

    // Filter by priority
    const matchesPriority = priorityFilter === 'all' || review.priority === priorityFilter;

    return matchesTab && matchesSearch && matchesPriority;
  });

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col lg:flex-row lg:items-center justify-between gap-4">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Quality Dashboard</h1>
          <p className="text-gray-600">Monitor and manage design quality reviews</p>
        </div>
        <Button onClick={fetchData} className="flex items-center gap-2">
          <RefreshCw className="h-4 w-4" />
          Refresh
        </Button>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Pending Reviews</p>
                <p className="text-2xl font-bold text-yellow-600">{stats.pending_reviews}</p>
              </div>
              <Clock className="h-6 w-6 text-yellow-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">In Review</p>
                <p className="text-2xl font-bold text-blue-600">{stats.in_review}</p>
              </div>
              <Eye className="h-6 w-6 text-blue-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Completed Today</p>
                <p className="text-2xl font-bold text-green-600">{stats.completed_today}</p>
              </div>
              <CheckCircle className="h-6 w-6 text-green-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">SLA Compliance</p>
                <p className="text-2xl font-bold text-purple-600">{stats.sla_compliance}%</p>
              </div>
              <TrendingUp className="h-6 w-6 text-purple-500" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Filters */}
      <div className="flex flex-col md:flex-row gap-4">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
          <input
            type="text"
            placeholder="Search reviews..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-10 pr-4 py-2 border border-gray-300 rounded-md w-full focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
          />
        </div>
        
        <select
          value={priorityFilter}
          onChange={(e) => setPriorityFilter(e.target.value)}
          className="px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
        >
          <option value="all">All Priorities</option>
          <option value="urgent">Urgent</option>
          <option value="high">High</option>
          <option value="normal">Normal</option>
          <option value="low">Low</option>
        </select>
      </div>

      {/* Reviews Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="all">All ({stats.total_reviews})</TabsTrigger>
          <TabsTrigger value="pending">Pending ({stats.pending_reviews})</TabsTrigger>
          <TabsTrigger value="in_review">In Review ({stats.in_review})</TabsTrigger>
          <TabsTrigger value="approved">Approved</TabsTrigger>
          <TabsTrigger value="rejected">Rejected</TabsTrigger>
        </TabsList>

        <TabsContent value={activeTab} className="space-y-4">
          {filteredReviews.length === 0 ? (
            <Card>
              <CardContent className="p-8 text-center">
                <FileText className="h-12 w-12 text-gray-300 mx-auto mb-4" />
                <h3 className="font-medium text-gray-900 mb-2">No reviews found</h3>
                <p className="text-gray-500">No reviews match your current filters.</p>
              </CardContent>
            </Card>
          ) : (
            <div className="space-y-4">
              {filteredReviews.map((review) => {
                const slaStatus = getSLAStatus(review.sla_deadline);
                
                return (
                  <Card key={review.id} className="hover:shadow-md transition-shadow">
                    <CardHeader className="pb-3">
                      <div className="flex items-start justify-between">
                        <div className="space-y-1 flex-1">
                          <CardTitle className="text-lg">
                            {review.projects?.title || 'Unknown Project'}
                          </CardTitle>
                          <div className="flex items-center gap-4 text-sm text-gray-600">
                            <span>Designer: {review.designer_profile?.full_name || 'Unknown'}</span>
                            <span>Client: {review.projects?.client_profile?.full_name || 'Unknown'}</span>
                            <span>Files: {review.project_submissions?.files?.length || 0}</span>
                          </div>
                        </div>
                        <div className="flex items-center gap-2">
                          <Badge className={getPriorityBadge(review.priority)}>
                            {review.priority}
                          </Badge>
                          <Badge className={getStatusBadge(review.status)}>
                            {review.status.replace('_', ' ')}
                          </Badge>
                          <Badge className={slaStatus.color}>
                            {formatTimeRemaining(review.sla_deadline)}
                          </Badge>
                        </div>
                      </div>
                    </CardHeader>
                    
                    <CardContent className="pt-0">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-4 text-sm text-gray-500">
                          <span>Created: {new Date(review.created_at).toLocaleDateString()}</span>
                          {review.overall_score && (
                            <div className="flex items-center gap-1">
                              <Star className="h-4 w-4 text-yellow-500" />
                              <span>{review.overall_score}/5</span>
                            </div>
                          )}
                        </div>
                        
                        <div className="flex items-center gap-2">
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => window.location.href = `/quality/reviews/${review.id}`}
                          >
                            <Eye className="h-3 w-3 mr-1" />
                            Review
                          </Button>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                );
              })}
            </div>
          )}
        </TabsContent>
      </Tabs>
    </div>
  );
}
