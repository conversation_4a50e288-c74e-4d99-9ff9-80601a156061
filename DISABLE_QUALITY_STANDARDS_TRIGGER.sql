-- Simple fix: Disable the quality standards trigger completely
-- This will prevent the "project_type" column error

-- Drop the problematic trigger
DROP TRIGGER IF EXISTS assign_quality_standards_trigger ON quality_reviews_new;

-- Drop the trigger function as well
DROP FUNCTION IF EXISTS trigger_assign_quality_standards();

-- Verify the trigger is gone
SELECT 
    schemaname,
    tablename,
    triggername,
    triggerevent
FROM pg_trigger pt
JOIN pg_class pc ON pt.tgrelid = pc.oid
JOIN pg_namespace pn ON pc.relnamespace = pn.oid
WHERE pc.relname = 'quality_reviews_new'
    AND NOT pt.tgisinternal;

SELECT 'Quality standards trigger disabled - project_type error should be fixed!' as status;
