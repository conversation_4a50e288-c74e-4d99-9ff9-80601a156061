-- Debug Test Submissions
-- This script creates test data to debug the client submissions page
-- Run this to add test submissions for debugging

-- First, let's see what projects exist
SELECT 'Existing projects:' as info;
SELECT id, title, client_id, designer_id FROM projects LIMIT 5;

-- Check if we have any profiles/users
SELECT 'Existing profiles:' as info;
SELECT id, email, role FROM profiles WHERE role IN ('client', 'designer', 'manager') LIMIT 5;

-- Check existing submissions in both tables
SELECT 'Legacy submissions:' as info;
SELECT COUNT(*) as count FROM submissions;

SELECT 'Project submissions:' as info;
SELECT COUNT(*) as count FROM project_submissions;

-- Create a test project if none exists
INSERT INTO projects (id, title, client_id, designer_id, status, created_at)
VALUES (
    'test-project-001',
    'Test Architecture Project',
    (SELECT id FROM profiles WHERE role = 'client' LIMIT 1),
    (SELECT id FROM profiles WHERE role = 'designer' LIMIT 1),
    'active',
    NOW()
) ON CONFLICT (id) DO NOTHING;

-- Create a test legacy submission
INSERT INTO submissions (
    id,
    project_id,
    designer_id,
    title,
    description,
    status,
    created_at,
    updated_at
) VALUES (
    'test-legacy-submission-001',
    'test-project-001',
    (SELECT id FROM profiles WHERE role = 'designer' LIMIT 1),
    'Initial Design Concept',
    'First draft of the architectural design showing the overall layout and concept.',
    'approved',
    NOW() - INTERVAL '2 days',
    NOW() - INTERVAL '1 day'
) ON CONFLICT (id) DO NOTHING;

-- Create a test project submission with quality review
DO $$
DECLARE
    test_submission_id UUID := 'test-project-submission-001';
    test_quality_review_id UUID := 'test-quality-review-001';
    test_designer_id UUID;
BEGIN
    -- Get a designer ID
    SELECT id INTO test_designer_id FROM profiles WHERE role = 'designer' LIMIT 1;
    
    IF test_designer_id IS NULL THEN
        RAISE NOTICE 'No designer found, cannot create test data';
        RETURN;
    END IF;

    -- Create quality review first
    INSERT INTO quality_reviews_new (
        id,
        submission_id,
        project_id,
        designer_id,
        status,
        quality_score,
        reviewed_by,
        reviewed_at,
        created_at
    ) VALUES (
        test_quality_review_id,
        test_submission_id,
        'test-project-001',
        test_designer_id,
        'approved',
        8,
        (SELECT id FROM profiles WHERE role = 'quality_team' LIMIT 1),
        NOW() - INTERVAL '6 hours',
        NOW() - INTERVAL '1 day'
    ) ON CONFLICT (id) DO NOTHING;

    -- Create project submission
    INSERT INTO project_submissions (
        id,
        project_id,
        designer_id,
        title,
        description,
        status,
        version,
        submission_type,
        quality_review_id,
        submitted_at,
        created_at,
        updated_at
    ) VALUES (
        test_submission_id,
        'test-project-001',
        test_designer_id,
        'Detailed Floor Plans',
        'Comprehensive floor plans with dimensions and technical specifications.',
        'submitted',
        1,
        'design',
        test_quality_review_id,
        NOW() - INTERVAL '1 day',
        NOW() - INTERVAL '1 day',
        NOW() - INTERVAL '6 hours'
    ) ON CONFLICT (id) DO NOTHING;

    RAISE NOTICE 'Created test project submission with quality review';
END $$;

-- Create another submission that needs revision
INSERT INTO submissions (
    id,
    project_id,
    designer_id,
    title,
    description,
    status,
    revision_requested,
    feedback,
    created_at,
    updated_at
) VALUES (
    'test-legacy-submission-002',
    'test-project-001',
    (SELECT id FROM profiles WHERE role = 'designer' LIMIT 1),
    'Revised Elevation Views',
    'Updated building elevation views based on client feedback.',
    'needs_revision',
    true,
    'Please adjust the window sizing on the east facade.',
    NOW() - INTERVAL '3 days',
    NOW() - INTERVAL '1 hour'
) ON CONFLICT (id) DO NOTHING;

-- Show what we created
SELECT 'Test data created - Legacy submissions:' as info;
SELECT id, title, status, created_at FROM submissions WHERE project_id = 'test-project-001';

SELECT 'Test data created - Project submissions:' as info;
SELECT ps.id, ps.title, ps.status, qr.status as quality_status, ps.created_at 
FROM project_submissions ps
LEFT JOIN quality_reviews_new qr ON ps.quality_review_id = qr.id
WHERE ps.project_id = 'test-project-001';

SELECT 'Quality reviews:' as info;
SELECT id, status, quality_score, reviewed_at FROM quality_reviews_new WHERE project_id = 'test-project-001';
