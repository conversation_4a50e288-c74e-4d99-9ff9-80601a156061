-- Fix workflow_notifications table to add missing metadata column
-- Run this in your Supabase SQL Editor

-- Add metadata column if it doesn't exist
ALTER TABLE workflow_notifications 
ADD COLUMN IF NOT EXISTS metadata JSONB DEFAULT '{}';

-- Verify the table structure
SELECT column_name, data_type, is_nullable, column_default
FROM information_schema.columns 
WHERE table_name = 'workflow_notifications'
ORDER BY ordinal_position;
