// Test if the project_type column error is fixed
const { createClient } = require('@supabase/supabase-js');
require('dotenv').config({ path: '.env.local' });

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_SERVICE_KEY || process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('Missing Supabase configuration.');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

async function testProjectTypeFix() {
  try {
    console.log('Testing project_type column fix...');
    
    // Test 1: Try to create a quality review directly
    console.log('Test 1: Creating quality review directly...');
    
    const testReviewData = {
      project_id: '48f13612-175b-4e2f-bf78-fb40efc3e73c',
      review_type: 'submission',
      status: 'pending',
      priority: 'normal',
      revision_count: 0
    };
    
    const { data: review, error: reviewError } = await supabase
      .from('quality_reviews_new')
      .insert(testReviewData)
      .select()
      .single();
    
    if (reviewError) {
      console.error('❌ Direct quality review creation failed:', reviewError.message);
      if (reviewError.message.includes('project_type')) {
        console.log('🎯 ISSUE: project_type column error still exists');
        console.log('🔧 SOLUTION: Execute DISABLE_QUALITY_STANDARDS_TRIGGER.sql');
      }
    } else {
      console.log('✅ Direct quality review creation successful!');
      console.log('Review ID:', review.id);
      
      // Clean up
      await supabase
        .from('quality_reviews_new')
        .delete()
        .eq('id', review.id);
    }
    
    // Test 2: Try to create a project submission (this triggers the quality review creation)
    console.log('\\nTest 2: Creating project submission...');
    
    const testSubmissionData = {
      project_id: '48f13612-175b-4e2f-bf78-fb40efc3e73c',
      description: 'Test submission for project_type fix',
      status: 'draft', // Start with draft
      submission_type: 'milestone',
      files: []
    };
    
    const { data: submission, error: submissionError } = await supabase
      .from('project_submissions')
      .insert(testSubmissionData)
      .select()
      .single();
    
    if (submissionError) {
      console.error('❌ Submission creation failed:', submissionError.message);
    } else {
      console.log('✅ Submission created successfully!');
      
      // Now try to update to submitted status (this should trigger quality review creation)
      console.log('Test 3: Updating to submitted status...');
      
      const { error: updateError } = await supabase
        .from('project_submissions')
        .update({ status: 'submitted' })
        .eq('id', submission.id);
      
      if (updateError) {
        console.error('❌ Update to submitted failed:', updateError.message);
        if (updateError.message.includes('project_type')) {
          console.log('🎯 ISSUE: project_type column error occurs during submission update');
          console.log('🔧 SOLUTION: Execute DISABLE_QUALITY_STANDARDS_TRIGGER.sql');
        } else if (updateError.message.includes('priority')) {
          console.log('🎯 ISSUE: Priority constraint error still exists');
          console.log('🔧 SOLUTION: Execute FINAL_PRIORITY_FIX.sql');
        }
      } else {
        console.log('✅ Update to submitted successful!');
        console.log('✅ Both priority and project_type issues are fixed!');
      }
      
      // Clean up
      await supabase
        .from('project_submissions')
        .delete()
        .eq('id', submission.id);
    }
    
    console.log('\\n📋 SUMMARY:');
    console.log('- If you see project_type errors: Execute DISABLE_QUALITY_STANDARDS_TRIGGER.sql');
    console.log('- If you see priority errors: Execute FINAL_PRIORITY_FIX.sql');
    console.log('- If both work: Your submission creation should now work!');
    
  } catch (error) {
    console.error('❌ Test error:', error);
  }
}

testProjectTypeFix();
