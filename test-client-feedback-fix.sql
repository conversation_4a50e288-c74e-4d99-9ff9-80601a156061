-- Quick test of the corrected client feedback integration SQL
-- Run this to verify the UNION column count is fixed

-- Test the view creation
CREATE OR REPLACE VIEW client_submissions_view_test AS
-- Project submissions that have been quality approved
SELECT 
    ps.id,
    ps.project_id,
    ps.milestone_id,
    ps.designer_id,
    ps.title,
    ps.description,
    ps.status,
    ps.version,
    ps.submission_type,
    ps.files,
    ps.submitted_at,
    ps.reviewed_at,
    ps.approved_at,
    ps.created_at,
    ps.updated_at,
    ps.feedback,
    ps.revision_requested,
    ps.work_type,
    'approved' as quality_status,
    ps.created_at as quality_reviewed_at,
    NULL as client_feedback_type,
    NULL as client_feedback_text,
    NULL as client_feedback_date,
    'project_submissions' as source_table,
    'test_status' as overall_status
FROM project_submissions ps
WHERE ps.id = ps.id -- Always true, just for testing

UNION ALL

-- Legacy submissions that are quality approved (status = 'approved')
SELECT 
    s.id,
    s.project_id,
    NULL as milestone_id,
    s.designer_id,
    s.title,
    s.description,
    s.status,
    1 as version,
    'legacy' as submission_type,
    NULL as files,
    s.created_at as submitted_at,
    s.updated_at as reviewed_at,
    CASE WHEN s.status = 'approved' THEN s.updated_at ELSE NULL END as approved_at,
    s.created_at,
    s.updated_at,
    s.feedback,
    s.revision_requested,
    NULL as work_type,
    'approved' as quality_status,
    s.updated_at as quality_reviewed_at,
    NULL as client_feedback_type,
    NULL as client_feedback_text,
    NULL as client_feedback_date,
    'submissions' as source_table,
    'test_status' as overall_status
FROM submissions s
WHERE s.id = s.id; -- Always true, just for testing

-- Test the view
SELECT 'Test view creation successful' as result;

-- Count columns in both parts
SELECT 'First SELECT has this many columns:' as info, 
       COUNT(*) as column_count 
FROM information_schema.columns 
WHERE table_name = 'client_submissions_view_test';

-- Drop test view
DROP VIEW IF EXISTS client_submissions_view_test;

-- Test the actual client feedback table creation
SELECT 'Testing client_feedback table existence...' as test;
SELECT COUNT(*) as count FROM client_feedback LIMIT 1;

SELECT 'All tests completed successfully!' as result;
