"use client";

import { useState } from "react";
import { filterMessage, shouldBlockMessage, getWarningMessage } from "@/lib/content-filter";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";

export default function TestContentFilterPage() {
  const [testMessage, setTestMessage] = useState("");
  const [result, setResult] = useState<any>(null);

  const testMessages = [
    "My <NAME_EMAIL>",
    "Call me at ************",
    "Contact me outside the platform",
    "Here's my WhatsApp: +1234567890",
    "Let's talk via email john [at] domain [dot] com",
    "This is a normal message without contact info",
    "My phone is ************ and <NAME_EMAIL>"
  ];

  const runTest = (message: string) => {
    const filterResult = filterMessage(message);
    const shouldBlock = shouldBlockMessage(filterResult);
    const warningMessage = getWarningMessage(filterResult.warningType, filterResult.severity);

    setResult({
      original: message,
      filterResult,
      shouldBlock,
      warningMessage
    });
  };

  return (
    <div className="max-w-4xl mx-auto p-6 space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>Content Filter Test Page</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Manual Test */}
          <div>
            <label className="block text-sm font-medium mb-2">Test Your Own Message:</label>
            <textarea
              value={testMessage}
              onChange={(e) => setTestMessage(e.target.value)}
              className="w-full p-3 border rounded-lg"
              rows={3}
              placeholder="Type a message to test..."
            />
            <Button 
              onClick={() => runTest(testMessage)}
              className="mt-2"
              disabled={!testMessage.trim()}
            >
              Test Message
            </Button>
          </div>

          {/* Preset Tests */}
          <div>
            <label className="block text-sm font-medium mb-2">Quick Tests:</label>
            <div className="grid grid-cols-1 gap-2">
              {testMessages.map((message, index) => (
                <Button
                  key={index}
                  onClick={() => runTest(message)}
                  variant="outline"
                  className="text-left justify-start"
                >
                  {message}
                </Button>
              ))}
            </div>
          </div>

          {/* Results */}
          {result && (
            <Card className="mt-6">
              <CardHeader>
                <CardTitle className="text-lg">Test Results</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <strong>Original Message:</strong>
                  <p className="bg-gray-100 p-2 rounded mt-1">{result.original}</p>
                </div>

                <div>
                  <strong>Is Blocked:</strong>
                  <span className={`ml-2 px-2 py-1 rounded text-sm ${
                    result.filterResult.isBlocked ? 'bg-red-100 text-red-700' : 'bg-green-100 text-green-700'
                  }`}>
                    {result.filterResult.isBlocked ? 'YES' : 'NO'}
                  </span>
                </div>

                {result.filterResult.isBlocked && (
                  <>
                    <div>
                      <strong>Should Block Completely:</strong>
                      <span className={`ml-2 px-2 py-1 rounded text-sm ${
                        result.shouldBlock ? 'bg-red-100 text-red-700' : 'bg-yellow-100 text-yellow-700'
                      }`}>
                        {result.shouldBlock ? 'YES - BLOCK' : 'NO - FILTER'}
                      </span>
                    </div>

                    <div>
                      <strong>Detected Content:</strong>
                      <div className="flex flex-wrap gap-1 mt-1">
                        {result.filterResult.blockedContent.map((content: string, index: number) => (
                          <span key={index} className="bg-red-100 text-red-700 px-2 py-1 rounded text-sm">
                            {content}
                          </span>
                        ))}
                      </div>
                    </div>

                    <div>
                      <strong>Violation Type:</strong>
                      <span className="ml-2 px-2 py-1 bg-blue-100 text-blue-700 rounded text-sm">
                        {result.filterResult.warningType}
                      </span>
                    </div>

                    <div>
                      <strong>Severity:</strong>
                      <span className={`ml-2 px-2 py-1 rounded text-sm ${
                        result.filterResult.severity === 'high' ? 'bg-red-100 text-red-700' :
                        result.filterResult.severity === 'medium' ? 'bg-yellow-100 text-yellow-700' :
                        'bg-blue-100 text-blue-700'
                      }`}>
                        {result.filterResult.severity.toUpperCase()}
                      </span>
                    </div>

                    <div>
                      <strong>Filtered Message:</strong>
                      <p className="bg-gray-100 p-2 rounded mt-1">{result.filterResult.filteredMessage}</p>
                    </div>

                    <div>
                      <strong>Warning Message:</strong>
                      <p className="bg-yellow-50 border border-yellow-200 p-3 rounded mt-1 text-yellow-800">
                        {result.warningMessage}
                      </p>
                    </div>
                  </>
                )}
              </CardContent>
            </Card>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
