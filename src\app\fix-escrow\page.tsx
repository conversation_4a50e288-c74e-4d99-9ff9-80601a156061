"use client";

import { useState } from "react";
import { useOptimizedAuth } from "@/hooks/useOptimizedAuth";
import { supabase } from "@/lib/supabase";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { RefreshCw, Wrench, CheckCircle, AlertTriangle } from "lucide-react";

export default function FixEscrowPage() {
  const { user, profile } = useOptimizedAuth();
  const [loading, setLoading] = useState(false);
  const [results, setResults] = useState<any>({});
  const [fixing, setFixing] = useState(false);

  const analyzeEscrowGaps = async () => {
    setLoading(true);
    const analysis: any = {};

    try {
      // 1. Find completed PayPal payments without escrow records
      const { data: paymentsWithoutEscrow, error: paymentsError } = await supabase
        .from('payments')
        .select(`
          id,
          project_id,
          milestone_id,
          client_id,
          designer_id,
          amount,
          transaction_id,
          created_at,
          projects(title, designer_id)
        `)
        .eq('payment_method', 'paypal')
        .eq('payment_type', 'deposit')
        .eq('status', 'completed');

      analysis.payments = { data: paymentsWithoutEscrow, error: paymentsError };

      if (paymentsWithoutEscrow) {
        // Check which payments have escrow records
        const paymentsWithEscrow = [];
        const paymentsWithoutEscrowRecords = [];

        for (const payment of paymentsWithoutEscrow) {
          // Check escrow_holds table
          const { data: escrowHolds } = await supabase
            .from('escrow_holds')
            .select('id')
            .eq('project_id', payment.project_id)
            .eq('milestone_id', payment.milestone_id);

          // Check paypal_escrow_holds table
          const { data: paypalEscrowHolds } = await supabase
            .from('paypal_escrow_holds')
            .select('id')
            .eq('project_id', payment.project_id)
            .eq('milestone_id', payment.milestone_id);

          if ((escrowHolds && escrowHolds.length > 0) || (paypalEscrowHolds && paypalEscrowHolds.length > 0)) {
            paymentsWithEscrow.push(payment);
          } else {
            paymentsWithoutEscrowRecords.push(payment);
          }
        }

        analysis.paymentsWithEscrow = paymentsWithEscrow;
        analysis.paymentsWithoutEscrowRecords = paymentsWithoutEscrowRecords;
      }

      // 2. Check existing escrow accounts
      const { data: escrowAccounts, error: accountsError } = await supabase
        .from('escrow_accounts')
        .select('*')
        .order('created_at', { ascending: false });

      analysis.escrowAccounts = { data: escrowAccounts, error: accountsError };

      // 3. Check existing escrow holds
      const { data: escrowHolds, error: holdsError } = await supabase
        .from('escrow_holds')
        .select('*')
        .order('created_at', { ascending: false });

      analysis.escrowHolds = { data: escrowHolds, error: holdsError };

    } catch (error) {
      console.error('Error analyzing escrow gaps:', error);
      analysis.error = error;
    }

    setResults(analysis);
    setLoading(false);
  };

  const fixMissingEscrowRecords = async () => {
    setFixing(true);
    const fixResults: any = { created: [], errors: [] };

    try {
      const paymentsToFix = results.paymentsWithoutEscrowRecords || [];

      for (const payment of paymentsToFix) {
        try {
          console.log('Processing payment:', {
            id: payment.id,
            project_id: payment.project_id,
            client_id: payment.client_id,
            designer_id: payment.designer_id,
            projects: payment.projects
          });

          // Calculate fees
          const grossAmount = payment.amount;
          const platformFee = grossAmount * 0.15; // 15%
          const processingFee = grossAmount * 0.029; // 2.9%
          const netAmount = grossAmount - platformFee - processingFee;

          // 1. Create or get escrow account
          let escrowAccountId;
          const designerId = payment.designer_id || payment.projects?.designer_id;

          if (!designerId) {
            throw new Error('No designer ID found for payment');
          }

          const { data: existingAccounts, error: accountFetchError } = await supabase
            .from('escrow_accounts')
            .select('id')
            .eq('project_id', payment.project_id)
            .eq('client_id', payment.client_id)
            .eq('designer_id', designerId);

          if (accountFetchError) {
            console.error('Error fetching escrow accounts:', accountFetchError);
            throw accountFetchError;
          }

          if (existingAccounts && existingAccounts.length > 0) {
            escrowAccountId = existingAccounts[0].id;
          } else {
            // Create new escrow account
            const accountNumber = `ESC-${payment.project_id.slice(-8)}-${Date.now()}`;
            const { data: newAccount, error: accountError } = await supabase
              .from('escrow_accounts')
              .insert({
                project_id: payment.project_id,
                client_id: payment.client_id,
                designer_id: designerId,
                account_number: accountNumber,
                status: 'active',
                total_held: grossAmount
              })
              .select('id')
              .single();

            if (accountError) throw accountError;
            escrowAccountId = newAccount.id;
          }

          // 2. Find or create a proper transaction record
          let actualTransactionId = payment.transaction_id;

          // Check if transaction_id is a UUID or PayPal order ID
          const isUUID = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(payment.transaction_id);

          if (!isUUID) {
            console.log('Transaction ID is not UUID, looking for actual transaction record...');

            // Look for transaction record with this PayPal order ID
            const { data: transactionRecords } = await supabase
              .from('transactions')
              .select('id')
              .eq('transaction_id', payment.transaction_id)
              .eq('project_id', payment.project_id);

            if (transactionRecords && transactionRecords.length > 0) {
              actualTransactionId = transactionRecords[0].id;
              console.log('Found transaction record:', actualTransactionId);
            } else {
              // Create a transaction record
              console.log('Creating new transaction record...');
              const { data: newTransaction, error: transactionError } = await supabase
                .from('transactions')
                .insert({
                  transaction_id: payment.transaction_id, // PayPal order ID
                  amount: grossAmount,
                  status: 'completed',
                  type: 'payment',
                  project_id: payment.project_id,
                  milestone_id: payment.milestone_id,
                  client_id: payment.client_id,
                  designer_id: designerId,
                  platform_fee: platformFee,
                  processing_fee: processingFee,
                  notes: `PayPal deposit payment - backfilled`,
                  payment_method: 'paypal',
                  created_at: payment.created_at
                })
                .select('id')
                .single();

              if (transactionError) {
                console.error('Error creating transaction:', transactionError);
                throw transactionError;
              }
              actualTransactionId = newTransaction.id;
              console.log('Created new transaction:', actualTransactionId);
            }
          }

          // 3. Create escrow hold
          console.log('Creating escrow hold with data:', {
            escrow_account_id: escrowAccountId,
            transaction_id: actualTransactionId,
            milestone_id: payment.milestone_id,
            project_id: payment.project_id,
            gross_amount: grossAmount,
            platform_fee: platformFee,
            processing_fee: processingFee,
            net_amount: netAmount
          });

          const { data: escrowHold, error: holdError } = await supabase
            .from('escrow_holds')
            .insert({
              escrow_account_id: escrowAccountId,
              transaction_id: actualTransactionId, // Use the UUID transaction ID
              milestone_id: payment.milestone_id,
              project_id: payment.project_id,
              gross_amount: grossAmount,
              platform_fee: platformFee,
              processing_fee: processingFee,
              net_amount: netAmount,
              hold_reason: 'paypal_deposit_payment_backfill',
              status: 'active',
              requires_manager_approval: true,
              requires_quality_approval: false,
              held_at: payment.created_at
            })
            .select()
            .single();

          if (holdError) {
            console.error('Escrow hold creation error:', holdError);
            throw holdError;
          }

          fixResults.created.push({
            payment_id: payment.id,
            project_title: payment.projects?.title || 'Unknown Project',
            amount: grossAmount,
            escrow_hold_id: escrowHold.id,
            transaction_id: actualTransactionId
          });

        } catch (error) {
          fixResults.errors.push({
            payment_id: payment.id,
            error: error instanceof Error ? error.message : 'Unknown error'
          });
        }
      }

    } catch (error) {
      console.error('Error fixing escrow records:', error);
    }

    setResults({ ...results, fixResults });
    setFixing(false);
  };

  if (!user || (profile?.role !== 'admin' && profile?.role !== 'manager')) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-900 mb-4">Access Denied</h1>
          <p className="text-gray-600">Only admins and managers can access this fix page.</p>
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-6xl mx-auto p-6 space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Fix Escrow System</h1>
          <p className="text-gray-600 mt-2">Create missing escrow records for completed PayPal payments</p>
        </div>
        <div className="flex gap-2">
          <Button onClick={analyzeEscrowGaps} disabled={loading}>
            <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
            Analyze
          </Button>
          {results.paymentsWithoutEscrowRecords?.length > 0 && (
            <Button onClick={fixMissingEscrowRecords} disabled={fixing} variant="destructive">
              <Wrench className={`h-4 w-4 mr-2 ${fixing ? 'animate-spin' : ''}`} />
              Fix Missing Records
            </Button>
          )}
        </div>
      </div>

      {loading ? (
        <div className="flex items-center justify-center py-12">
          <RefreshCw className="h-8 w-8 animate-spin text-blue-600" />
          <span className="ml-2">Analyzing escrow system...</span>
        </div>
      ) : results.payments ? (
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Analysis Results */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <AlertTriangle className="h-5 w-5 text-yellow-600" />
                Analysis Results
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="bg-blue-50 border border-blue-200 p-4 rounded">
                <h4 className="font-semibold text-blue-900">Total PayPal Payments</h4>
                <p className="text-2xl font-bold text-blue-700">{results.payments.data?.length || 0}</p>
              </div>

              <div className="bg-green-50 border border-green-200 p-4 rounded">
                <h4 className="font-semibold text-green-900">With Escrow Records</h4>
                <p className="text-2xl font-bold text-green-700">{results.paymentsWithEscrow?.length || 0}</p>
              </div>

              <div className="bg-red-50 border border-red-200 p-4 rounded">
                <h4 className="font-semibold text-red-900">Missing Escrow Records</h4>
                <p className="text-2xl font-bold text-red-700">{results.paymentsWithoutEscrowRecords?.length || 0}</p>
              </div>

              {results.paymentsWithoutEscrowRecords?.length > 0 && (
                <div className="bg-yellow-50 border border-yellow-200 p-4 rounded">
                  <h4 className="font-semibold text-yellow-900 mb-2">Payments Missing Escrow:</h4>
                  <div className="space-y-2 max-h-40 overflow-y-auto">
                    {results.paymentsWithoutEscrowRecords.map((payment: any) => (
                      <div key={payment.id} className="text-sm">
                        <strong>{payment.projects.title}</strong> - ${payment.amount}
                        <br />
                        <span className="text-gray-600">ID: {payment.id.slice(0, 8)}...</span>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Fix Results */}
          {results.fixResults && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <CheckCircle className="h-5 w-5 text-green-600" />
                  Fix Results
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="bg-green-50 border border-green-200 p-4 rounded">
                  <h4 className="font-semibold text-green-900">Successfully Created</h4>
                  <p className="text-2xl font-bold text-green-700">{results.fixResults.created?.length || 0}</p>
                </div>

                <div className="bg-red-50 border border-red-200 p-4 rounded">
                  <h4 className="font-semibold text-red-900">Errors</h4>
                  <p className="text-2xl font-bold text-red-700">{results.fixResults.errors?.length || 0}</p>
                </div>

                {results.fixResults.created?.length > 0 && (
                  <div className="bg-green-50 border border-green-200 p-4 rounded">
                    <h4 className="font-semibold text-green-900 mb-2">Created Escrow Records:</h4>
                    <div className="space-y-2 max-h-40 overflow-y-auto">
                      {results.fixResults.created.map((record: any) => (
                        <div key={record.payment_id} className="text-sm">
                          <strong>{record.project_title}</strong> - ${record.amount}
                          <br />
                          <span className="text-gray-600">Escrow ID: {record.escrow_hold_id.slice(0, 8)}...</span>
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          )}
        </div>
      ) : null}
    </div>
  );
}
