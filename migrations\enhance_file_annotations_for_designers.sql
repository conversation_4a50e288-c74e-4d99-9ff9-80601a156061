# Designer Annotation Viewing Enhancement Plan

## 🎯 **Objective**
Enable designers to view their submitted images with quality team annotations after reviews are completed.

## 📋 **Current Gap Analysis**

### ❌ **What's Missing:**
1. **Designer Project View**: No image annotation viewing in `/designer/projects/[id]`
2. **Quality Review Integration**: No connection between quality reviews and annotated files
3. **File Accessibility**: Designers can't access quality-annotated versions of their files
4. **Visual Feedback**: Only text feedback shown, not visual markup

### ✅ **What Exists:**
1. **FileAnnotationTool Component**: Fully functional annotation system
2. **Annotation API**: Working CRUD operations for annotations
3. **Quality Review System**: Basic feedback display
4. **File Storage**: Cloudflare R2 integration

## 🔧 **Implementation Plan**

### **Phase 1: Designer Project File Viewing**
**File: `src/app/designer/projects/[id]/page.tsx`**

Add a new section to show submitted files with annotations:

```tsx
// Add to existing project detail page
<div className="space-y-6">
  {/* Existing project info */}
  
  {/* NEW: Submitted Files with Annotations */}
  <Card>
    <CardHeader>
      <CardTitle>Submitted Files & Quality Feedback</CardTitle>
    </CardHeader>
    <CardContent>
      {submittedFiles.map((file) => (
        <div key={file.id} className="border rounded-lg p-4 mb-4">
          <div className="flex justify-between items-center mb-3">
            <h4 className="font-medium">{file.name}</h4>
            <Badge variant={getQualityStatusVariant(file.quality_status)}>
              {file.quality_status}
            </Badge>
          </div>
          
          {file.annotations?.length > 0 && (
            <div>
              <p className="text-sm text-gray-600 mb-3">
                Quality team has provided visual feedback on this file:
              </p>
              <FileAnnotationTool
                fileUrl={file.url}
                fileName={file.name}
                fileType={file.type}
                existingAnnotations={file.annotations}
                readOnly={true} // Designers can only view, not edit
                reviewerName="Quality Team"
              />
            </div>
          )}
        </div>
      ))}
    </CardContent>
  </Card>
</div>
```

### **Phase 2: Quality Review File Association**
**File: `src/app/api/quality/annotations/route.ts`**

Enhance the API to link annotations with quality reviews:

```typescript
// Add quality_review_id to annotation creation
const annotation = await supabase
  .from('file_annotations')
  .insert({
    file_url: fileUrl,
    file_name: fileName,
    annotation_type: annotationType,
    x_position: xPosition,
    y_position: yPosition,
    title,
    description,
    reviewer_name: user.user_metadata?.full_name,
    quality_review_id: qualityReviewId, // NEW: Link to quality review
    created_at: new Date().toISOString()
  });
```

### **Phase 3: Enhanced Designer Quality Page**
**File: `src/app/designer/quality/page.tsx`**

Add "View Annotated Files" functionality:

```tsx
{review.status === 'needs_revision' && (
  <div className="flex gap-2 mt-3">
    <Button 
      variant="outline" 
      onClick={() => viewAnnotatedFiles(review.id)}
    >
      <FileImage className="h-4 w-4 mr-2" />
      View Annotated Files
    </Button>
    <Button 
      variant="default" 
      onClick={() => navigateToProject(review.project_id)}
    >
      Update Project
    </Button>
  </div>
)}
```

## 🔧 **Save Annotations Fix**

### **Current Issue:**
```tsx
const handleSave = () => {
  if (onSaveAnnotations) {
    onSaveAnnotations(annotations); // Only calls prop, doesn't save to DB
  }
};
```

### **Enhanced Save Functionality:**
```tsx
const handleSave = async () => {
  try {
    setSaving(true);
    
    // Option 1: If using the callback pattern
    if (onSaveAnnotations) {
      await onSaveAnnotations(annotations);
    }
    
    // Option 2: Direct API save (for standalone use)
    else {
      const headers = await getAuthHeaders();
      const response = await fetch('/api/quality/annotations/bulk-save', {
        method: 'POST',
        headers,
        body: JSON.stringify({
          annotations,
          fileUrl,
          fileName,
          qualityReviewId
        })
      });
      
      if (!response.ok) {
        throw new Error('Failed to save annotations');
      }
    }
    
    // Show success feedback
    setShowSuccessMessage(true);
    setTimeout(() => setShowSuccessMessage(false), 3000);
    
  } catch (error) {
    console.error('Error saving annotations:', error);
    setShowErrorMessage(true);
    setTimeout(() => setShowErrorMessage(false), 5000);
  } finally {
    setSaving(false);
  }
};
```

## 📊 **Database Schema Updates**

### **Enhanced file_annotations table:**
```sql
ALTER TABLE file_annotations 
ADD COLUMN quality_review_id UUID REFERENCES quality_reviews(id),
ADD COLUMN project_id UUID REFERENCES projects(id),
ADD COLUMN is_visible_to_designer BOOLEAN DEFAULT true;

-- Index for performance
CREATE INDEX idx_file_annotations_quality_review 
ON file_annotations(quality_review_id);

CREATE INDEX idx_file_annotations_project 
ON file_annotations(project_id);
```

## 🎯 **Expected Outcomes**

After implementation:

### **✅ Designers Will Be Able To:**
1. **View annotated images** in their project details
2. **See visual feedback** from quality team on specific areas
3. **Understand revision requests** with precise visual context
4. **Navigate from quality reviews** to annotated files
5. **Track annotation history** across project revisions

### **✅ Quality Team Benefits:**
1. **More effective communication** through visual feedback
2. **Reduced back-and-forth** with clearer guidance
3. **Better designer education** through visual examples
4. **Audit trail** of feedback provided

### **✅ Platform Benefits:**
1. **Improved designer satisfaction** through better feedback
2. **Faster revision cycles** with clearer requirements
3. **Higher quality deliverables** through better communication
4. **Reduced support burden** with self-service feedback viewing

## 📁 **Files to Modify**

### **New Files:**
- `src/app/api/quality/annotations/bulk-save/route.ts` - Bulk save endpoint
- `src/components/designer/AnnotatedFileViewer.tsx` - Designer-specific file viewer

### **Modified Files:**
- `src/app/designer/projects/[id]/page.tsx` - Add annotation viewing
- `src/app/designer/quality/page.tsx` - Add "View Files" buttons
- `src/components/quality/FileAnnotationTool.tsx` - Enhanced save functionality
- `migrations/enhance_file_annotations.sql` - Database updates

## 🚀 **Implementation Priority**

1. **HIGH**: Fix save annotations functionality (immediate impact)
2. **HIGH**: Add designer file viewing in project details (core feature)
3. **MEDIUM**: Enhance quality page with file links (nice to have)
4. **LOW**: Advanced features like annotation history (future enhancement)

This implementation will complete the quality feedback loop and provide designers with the visual context they need to make effective revisions.
# Designer Annotation Viewing Enhancement Plan

## 🎯 **Objective**
Enable designers to view their submitted images with quality team annotations after reviews are completed.

## 📋 **Current Gap Analysis**

### ❌ **What's Missing:**
1. **Designer Project View**: No image annotation viewing in `/designer/projects/[id]`
2. **Quality Review Integration**: No connection between quality reviews and annotated files
3. **File Accessibility**: Designers can't access quality-annotated versions of their files
4. **Visual Feedback**: Only text feedback shown, not visual markup

### ✅ **What Exists:**
1. **FileAnnotationTool Component**: Fully functional annotation system
2. **Annotation API**: Working CRUD operations for annotations
3. **Quality Review System**: Basic feedback display
4. **File Storage**: Cloudflare R2 integration

## 🔧 **Implementation Plan**

### **Phase 1: Designer Project File Viewing**
**File: `src/app/designer/projects/[id]/page.tsx`**

Add a new section to show submitted files with annotations:

```tsx
// Add to existing project detail page
<div className="space-y-6">
  {/* Existing project info */}
  
  {/* NEW: Submitted Files with Annotations */}
  <Card>
    <CardHeader>
      <CardTitle>Submitted Files & Quality Feedback</CardTitle>
    </CardHeader>
    <CardContent>
      {submittedFiles.map((file) => (
        <div key={file.id} className="border rounded-lg p-4 mb-4">
          <div className="flex justify-between items-center mb-3">
            <h4 className="font-medium">{file.name}</h4>
            <Badge variant={getQualityStatusVariant(file.quality_status)}>
              {file.quality_status}
            </Badge>
          </div>
          
          {file.annotations?.length > 0 && (
            <div>
              <p className="text-sm text-gray-600 mb-3">
                Quality team has provided visual feedback on this file:
              </p>
              <FileAnnotationTool
                fileUrl={file.url}
                fileName={file.name}
                fileType={file.type}
                existingAnnotations={file.annotations}
                readOnly={true} // Designers can only view, not edit
                reviewerName="Quality Team"
              />
            </div>
          )}
        </div>
      ))}
    </CardContent>
  </Card>
</div>
```

### **Phase 2: Quality Review File Association**
**File: `src/app/api/quality/annotations/route.ts`**

Enhance the API to link annotations with quality reviews:

```typescript
// Add quality_review_id to annotation creation
const annotation = await supabase
  .from('file_annotations')
  .insert({
    file_url: fileUrl,
    file_name: fileName,
    annotation_type: annotationType,
    x_position: xPosition,
    y_position: yPosition,
    title,
    description,
    reviewer_name: user.user_metadata?.full_name,
    quality_review_id: qualityReviewId, // NEW: Link to quality review
    created_at: new Date().toISOString()
  });
```

### **Phase 3: Enhanced Designer Quality Page**
**File: `src/app/designer/quality/page.tsx`**

Add "View Annotated Files" functionality:

```tsx
{review.status === 'needs_revision' && (
  <div className="flex gap-2 mt-3">
    <Button 
      variant="outline" 
      onClick={() => viewAnnotatedFiles(review.id)}
    >
      <FileImage className="h-4 w-4 mr-2" />
      View Annotated Files
    </Button>
    <Button 
      variant="default" 
      onClick={() => navigateToProject(review.project_id)}
    >
      Update Project
    </Button>
  </div>
)}
```

## 🔧 **Save Annotations Fix**

### **Current Issue:**
```tsx
const handleSave = () => {
  if (onSaveAnnotations) {
    onSaveAnnotations(annotations); // Only calls prop, doesn't save to DB
  }
};
```

### **Enhanced Save Functionality:**
```tsx
const handleSave = async () => {
  try {
    setSaving(true);
    
    // Option 1: If using the callback pattern
    if (onSaveAnnotations) {
      await onSaveAnnotations(annotations);
    }
    
    // Option 2: Direct API save (for standalone use)
    else {
      const headers = await getAuthHeaders();
      const response = await fetch('/api/quality/annotations/bulk-save', {
        method: 'POST',
        headers,
        body: JSON.stringify({
          annotations,
          fileUrl,
          fileName,
          qualityReviewId
        })
      });
      
      if (!response.ok) {
        throw new Error('Failed to save annotations');
      }
    }
    
    // Show success feedback
    setShowSuccessMessage(true);
    setTimeout(() => setShowSuccessMessage(false), 3000);
    
  } catch (error) {
    console.error('Error saving annotations:', error);
    setShowErrorMessage(true);
    setTimeout(() => setShowErrorMessage(false), 5000);
  } finally {
    setSaving(false);
  }
};
```

## 📊 **Database Schema Updates**

### **Enhanced file_annotations table:**
```sql
ALTER TABLE file_annotations 
ADD COLUMN quality_review_id UUID REFERENCES quality_reviews(id),
ADD COLUMN project_id UUID REFERENCES projects(id),
ADD COLUMN is_visible_to_designer BOOLEAN DEFAULT true;

-- Index for performance
CREATE INDEX idx_file_annotations_quality_review 
ON file_annotations(quality_review_id);

CREATE INDEX idx_file_annotations_project 
ON file_annotations(project_id);
```

## 🎯 **Expected Outcomes**

After implementation:

### **✅ Designers Will Be Able To:**
1. **View annotated images** in their project details
2. **See visual feedback** from quality team on specific areas
3. **Understand revision requests** with precise visual context
4. **Navigate from quality reviews** to annotated files
5. **Track annotation history** across project revisions

### **✅ Quality Team Benefits:**
1. **More effective communication** through visual feedback
2. **Reduced back-and-forth** with clearer guidance
3. **Better designer education** through visual examples
4. **Audit trail** of feedback provided

### **✅ Platform Benefits:**
1. **Improved designer satisfaction** through better feedback
2. **Faster revision cycles** with clearer requirements
3. **Higher quality deliverables** through better communication
4. **Reduced support burden** with self-service feedback viewing

## 📁 **Files to Modify**

### **New Files:**
- `src/app/api/quality/annotations/bulk-save/route.ts` - Bulk save endpoint
- `src/components/designer/AnnotatedFileViewer.tsx` - Designer-specific file viewer

### **Modified Files:**
- `src/app/designer/projects/[id]/page.tsx` - Add annotation viewing
- `src/app/designer/quality/page.tsx` - Add "View Files" buttons
- `src/components/quality/FileAnnotationTool.tsx` - Enhanced save functionality
- `migrations/enhance_file_annotations.sql` - Database updates

## 🚀 **Implementation Priority**

1. **HIGH**: Fix save annotations functionality (immediate impact)
2. **HIGH**: Add designer file viewing in project details (core feature)
3. **MEDIUM**: Enhance quality page with file links (nice to have)
4. **LOW**: Advanced features like annotation history (future enhancement)

This implementation will complete the quality feedback loop and provide designers with the visual context they need to make effective revisions.
# Designer Annotation Viewing Enhancement Plan

## 🎯 **Objective**
Enable designers to view their submitted images with quality team annotations after reviews are completed.

## 📋 **Current Gap Analysis**

### ❌ **What's Missing:**
1. **Designer Project View**: No image annotation viewing in `/designer/projects/[id]`
2. **Quality Review Integration**: No connection between quality reviews and annotated files
3. **File Accessibility**: Designers can't access quality-annotated versions of their files
4. **Visual Feedback**: Only text feedback shown, not visual markup

### ✅ **What Exists:**
1. **FileAnnotationTool Component**: Fully functional annotation system
2. **Annotation API**: Working CRUD operations for annotations
3. **Quality Review System**: Basic feedback display
4. **File Storage**: Cloudflare R2 integration

## 🔧 **Implementation Plan**

### **Phase 1: Designer Project File Viewing**
**File: `src/app/designer/projects/[id]/page.tsx`**

Add a new section to show submitted files with annotations:

```tsx
// Add to existing project detail page
<div className="space-y-6">
  {/* Existing project info */}
  
  {/* NEW: Submitted Files with Annotations */}
  <Card>
    <CardHeader>
      <CardTitle>Submitted Files & Quality Feedback</CardTitle>
    </CardHeader>
    <CardContent>
      {submittedFiles.map((file) => (
        <div key={file.id} className="border rounded-lg p-4 mb-4">
          <div className="flex justify-between items-center mb-3">
            <h4 className="font-medium">{file.name}</h4>
            <Badge variant={getQualityStatusVariant(file.quality_status)}>
              {file.quality_status}
            </Badge>
          </div>
          
          {file.annotations?.length > 0 && (
            <div>
              <p className="text-sm text-gray-600 mb-3">
                Quality team has provided visual feedback on this file:
              </p>
              <FileAnnotationTool
                fileUrl={file.url}
                fileName={file.name}
                fileType={file.type}
                existingAnnotations={file.annotations}
                readOnly={true} // Designers can only view, not edit
                reviewerName="Quality Team"
              />
            </div>
          )}
        </div>
      ))}
    </CardContent>
  </Card>
</div>
```

### **Phase 2: Quality Review File Association**
**File: `src/app/api/quality/annotations/route.ts`**

Enhance the API to link annotations with quality reviews:

```typescript
// Add quality_review_id to annotation creation
const annotation = await supabase
  .from('file_annotations')
  .insert({
    file_url: fileUrl,
    file_name: fileName,
    annotation_type: annotationType,
    x_position: xPosition,
    y_position: yPosition,
    title,
    description,
    reviewer_name: user.user_metadata?.full_name,
    quality_review_id: qualityReviewId, // NEW: Link to quality review
    created_at: new Date().toISOString()
  });
```

### **Phase 3: Enhanced Designer Quality Page**
**File: `src/app/designer/quality/page.tsx`**

Add "View Annotated Files" functionality:

```tsx
{review.status === 'needs_revision' && (
  <div className="flex gap-2 mt-3">
    <Button 
      variant="outline" 
      onClick={() => viewAnnotatedFiles(review.id)}
    >
      <FileImage className="h-4 w-4 mr-2" />
      View Annotated Files
    </Button>
    <Button 
      variant="default" 
      onClick={() => navigateToProject(review.project_id)}
    >
      Update Project
    </Button>
  </div>
)}
```

## 🔧 **Save Annotations Fix**

### **Current Issue:**
```tsx
const handleSave = () => {
  if (onSaveAnnotations) {
    onSaveAnnotations(annotations); // Only calls prop, doesn't save to DB
  }
};
```

### **Enhanced Save Functionality:**
```tsx
const handleSave = async () => {
  try {
    setSaving(true);
    
    // Option 1: If using the callback pattern
    if (onSaveAnnotations) {
      await onSaveAnnotations(annotations);
    }
    
    // Option 2: Direct API save (for standalone use)
    else {
      const headers = await getAuthHeaders();
      const response = await fetch('/api/quality/annotations/bulk-save', {
        method: 'POST',
        headers,
        body: JSON.stringify({
          annotations,
          fileUrl,
          fileName,
          qualityReviewId
        })
      });
      
      if (!response.ok) {
        throw new Error('Failed to save annotations');
      }
    }
    
    // Show success feedback
    setShowSuccessMessage(true);
    setTimeout(() => setShowSuccessMessage(false), 3000);
    
  } catch (error) {
    console.error('Error saving annotations:', error);
    setShowErrorMessage(true);
    setTimeout(() => setShowErrorMessage(false), 5000);
  } finally {
    setSaving(false);
  }
};
```

## 📊 **Database Schema Updates**

### **Enhanced file_annotations table:**
```sql
ALTER TABLE file_annotations 
ADD COLUMN quality_review_id UUID REFERENCES quality_reviews(id),
ADD COLUMN project_id UUID REFERENCES projects(id),
ADD COLUMN is_visible_to_designer BOOLEAN DEFAULT true;

-- Index for performance
CREATE INDEX idx_file_annotations_quality_review 
ON file_annotations(quality_review_id);

CREATE INDEX idx_file_annotations_project 
ON file_annotations(project_id);
```

## 🎯 **Expected Outcomes**

After implementation:

### **✅ Designers Will Be Able To:**
1. **View annotated images** in their project details
2. **See visual feedback** from quality team on specific areas
3. **Understand revision requests** with precise visual context
4. **Navigate from quality reviews** to annotated files
5. **Track annotation history** across project revisions

### **✅ Quality Team Benefits:**
1. **More effective communication** through visual feedback
2. **Reduced back-and-forth** with clearer guidance
3. **Better designer education** through visual examples
4. **Audit trail** of feedback provided

### **✅ Platform Benefits:**
1. **Improved designer satisfaction** through better feedback
2. **Faster revision cycles** with clearer requirements
3. **Higher quality deliverables** through better communication
4. **Reduced support burden** with self-service feedback viewing

## 📁 **Files to Modify**

### **New Files:**
- `src/app/api/quality/annotations/bulk-save/route.ts` - Bulk save endpoint
- `src/components/designer/AnnotatedFileViewer.tsx` - Designer-specific file viewer

### **Modified Files:**
- `src/app/designer/projects/[id]/page.tsx` - Add annotation viewing
- `src/app/designer/quality/page.tsx` - Add "View Files" buttons
- `src/components/quality/FileAnnotationTool.tsx` - Enhanced save functionality
- `migrations/enhance_file_annotations.sql` - Database updates

## 🚀 **Implementation Priority**

1. **HIGH**: Fix save annotations functionality (immediate impact)
2. **HIGH**: Add designer file viewing in project details (core feature)
3. **MEDIUM**: Enhance quality page with file links (nice to have)
4. **LOW**: Advanced features like annotation history (future enhancement)

This implementation will complete the quality feedback loop and provide designers with the visual context they need to make effective revisions.
# Designer Annotation Viewing Enhancement Plan

## 🎯 **Objective**
Enable designers to view their submitted images with quality team annotations after reviews are completed.

## 📋 **Current Gap Analysis**

### ❌ **What's Missing:**
1. **Designer Project View**: No image annotation viewing in `/designer/projects/[id]`
2. **Quality Review Integration**: No connection between quality reviews and annotated files
3. **File Accessibility**: Designers can't access quality-annotated versions of their files
4. **Visual Feedback**: Only text feedback shown, not visual markup

### ✅ **What Exists:**
1. **FileAnnotationTool Component**: Fully functional annotation system
2. **Annotation API**: Working CRUD operations for annotations
3. **Quality Review System**: Basic feedback display
4. **File Storage**: Cloudflare R2 integration

## 🔧 **Implementation Plan**

### **Phase 1: Designer Project File Viewing**
**File: `src/app/designer/projects/[id]/page.tsx`**

Add a new section to show submitted files with annotations:

```tsx
// Add to existing project detail page
<div className="space-y-6">
  {/* Existing project info */}
  
  {/* NEW: Submitted Files with Annotations */}
  <Card>
    <CardHeader>
      <CardTitle>Submitted Files & Quality Feedback</CardTitle>
    </CardHeader>
    <CardContent>
      {submittedFiles.map((file) => (
        <div key={file.id} className="border rounded-lg p-4 mb-4">
          <div className="flex justify-between items-center mb-3">
            <h4 className="font-medium">{file.name}</h4>
            <Badge variant={getQualityStatusVariant(file.quality_status)}>
              {file.quality_status}
            </Badge>
          </div>
          
          {file.annotations?.length > 0 && (
            <div>
              <p className="text-sm text-gray-600 mb-3">
                Quality team has provided visual feedback on this file:
              </p>
              <FileAnnotationTool
                fileUrl={file.url}
                fileName={file.name}
                fileType={file.type}
                existingAnnotations={file.annotations}
                readOnly={true} // Designers can only view, not edit
                reviewerName="Quality Team"
              />
            </div>
          )}
        </div>
      ))}
    </CardContent>
  </Card>
</div>
```

### **Phase 2: Quality Review File Association**
**File: `src/app/api/quality/annotations/route.ts`**

Enhance the API to link annotations with quality reviews:

```typescript
// Add quality_review_id to annotation creation
const annotation = await supabase
  .from('file_annotations')
  .insert({
    file_url: fileUrl,
    file_name: fileName,
    annotation_type: annotationType,
    x_position: xPosition,
    y_position: yPosition,
    title,
    description,
    reviewer_name: user.user_metadata?.full_name,
    quality_review_id: qualityReviewId, // NEW: Link to quality review
    created_at: new Date().toISOString()
  });
```

### **Phase 3: Enhanced Designer Quality Page**
**File: `src/app/designer/quality/page.tsx`**

Add "View Annotated Files" functionality:

```tsx
{review.status === 'needs_revision' && (
  <div className="flex gap-2 mt-3">
    <Button 
      variant="outline" 
      onClick={() => viewAnnotatedFiles(review.id)}
    >
      <FileImage className="h-4 w-4 mr-2" />
      View Annotated Files
    </Button>
    <Button 
      variant="default" 
      onClick={() => navigateToProject(review.project_id)}
    >
      Update Project
    </Button>
  </div>
)}
```

## 🔧 **Save Annotations Fix**

### **Current Issue:**
```tsx
const handleSave = () => {
  if (onSaveAnnotations) {
    onSaveAnnotations(annotations); // Only calls prop, doesn't save to DB
  }
};
```

### **Enhanced Save Functionality:**
```tsx
const handleSave = async () => {
  try {
    setSaving(true);
    
    // Option 1: If using the callback pattern
    if (onSaveAnnotations) {
      await onSaveAnnotations(annotations);
    }
    
    // Option 2: Direct API save (for standalone use)
    else {
      const headers = await getAuthHeaders();
      const response = await fetch('/api/quality/annotations/bulk-save', {
        method: 'POST',
        headers,
        body: JSON.stringify({
          annotations,
          fileUrl,
          fileName,
          qualityReviewId
        })
      });
      
      if (!response.ok) {
        throw new Error('Failed to save annotations');
      }
    }
    
    // Show success feedback
    setShowSuccessMessage(true);
    setTimeout(() => setShowSuccessMessage(false), 3000);
    
  } catch (error) {
    console.error('Error saving annotations:', error);
    setShowErrorMessage(true);
    setTimeout(() => setShowErrorMessage(false), 5000);
  } finally {
    setSaving(false);
  }
};
```

## 📊 **Database Schema Updates**

### **Enhanced file_annotations table:**
```sql
ALTER TABLE file_annotations 
ADD COLUMN quality_review_id UUID REFERENCES quality_reviews(id),
ADD COLUMN project_id UUID REFERENCES projects(id),
ADD COLUMN is_visible_to_designer BOOLEAN DEFAULT true;

-- Index for performance
CREATE INDEX idx_file_annotations_quality_review 
ON file_annotations(quality_review_id);

CREATE INDEX idx_file_annotations_project 
ON file_annotations(project_id);
```

## 🎯 **Expected Outcomes**

After implementation:

### **✅ Designers Will Be Able To:**
1. **View annotated images** in their project details
2. **See visual feedback** from quality team on specific areas
3. **Understand revision requests** with precise visual context
4. **Navigate from quality reviews** to annotated files
5. **Track annotation history** across project revisions

### **✅ Quality Team Benefits:**
1. **More effective communication** through visual feedback
2. **Reduced back-and-forth** with clearer guidance
3. **Better designer education** through visual examples
4. **Audit trail** of feedback provided

### **✅ Platform Benefits:**
1. **Improved designer satisfaction** through better feedback
2. **Faster revision cycles** with clearer requirements
3. **Higher quality deliverables** through better communication
4. **Reduced support burden** with self-service feedback viewing

## 📁 **Files to Modify**

### **New Files:**
- `src/app/api/quality/annotations/bulk-save/route.ts` - Bulk save endpoint
- `src/components/designer/AnnotatedFileViewer.tsx` - Designer-specific file viewer

### **Modified Files:**
- `src/app/designer/projects/[id]/page.tsx` - Add annotation viewing
- `src/app/designer/quality/page.tsx` - Add "View Files" buttons
- `src/components/quality/FileAnnotationTool.tsx` - Enhanced save functionality
- `migrations/enhance_file_annotations.sql` - Database updates

## 🚀 **Implementation Priority**

1. **HIGH**: Fix save annotations functionality (immediate impact)
2. **HIGH**: Add designer file viewing in project details (core feature)
3. **MEDIUM**: Enhance quality page with file links (nice to have)
4. **LOW**: Advanced features like annotation history (future enhancement)

This implementation will complete the quality feedback loop and provide designers with the visual context they need to make effective revisions.
# Designer Annotation Viewing Enhancement Plan

## 🎯 **Objective**
Enable designers to view their submitted images with quality team annotations after reviews are completed.

## 📋 **Current Gap Analysis**

### ❌ **What's Missing:**
1. **Designer Project View**: No image annotation viewing in `/designer/projects/[id]`
2. **Quality Review Integration**: No connection between quality reviews and annotated files
3. **File Accessibility**: Designers can't access quality-annotated versions of their files
4. **Visual Feedback**: Only text feedback shown, not visual markup

### ✅ **What Exists:**
1. **FileAnnotationTool Component**: Fully functional annotation system
2. **Annotation API**: Working CRUD operations for annotations
3. **Quality Review System**: Basic feedback display
4. **File Storage**: Cloudflare R2 integration

## 🔧 **Implementation Plan**

### **Phase 1: Designer Project File Viewing**
**File: `src/app/designer/projects/[id]/page.tsx`**

Add a new section to show submitted files with annotations:

```tsx
// Add to existing project detail page
<div className="space-y-6">
  {/* Existing project info */}
  
  {/* NEW: Submitted Files with Annotations */}
  <Card>
    <CardHeader>
      <CardTitle>Submitted Files & Quality Feedback</CardTitle>
    </CardHeader>
    <CardContent>
      {submittedFiles.map((file) => (
        <div key={file.id} className="border rounded-lg p-4 mb-4">
          <div className="flex justify-between items-center mb-3">
            <h4 className="font-medium">{file.name}</h4>
            <Badge variant={getQualityStatusVariant(file.quality_status)}>
              {file.quality_status}
            </Badge>
          </div>
          
          {file.annotations?.length > 0 && (
            <div>
              <p className="text-sm text-gray-600 mb-3">
                Quality team has provided visual feedback on this file:
              </p>
              <FileAnnotationTool
                fileUrl={file.url}
                fileName={file.name}
                fileType={file.type}
                existingAnnotations={file.annotations}
                readOnly={true} // Designers can only view, not edit
                reviewerName="Quality Team"
              />
            </div>
          )}
        </div>
      ))}
    </CardContent>
  </Card>
</div>
```

### **Phase 2: Quality Review File Association**
**File: `src/app/api/quality/annotations/route.ts`**

Enhance the API to link annotations with quality reviews:

```typescript
// Add quality_review_id to annotation creation
const annotation = await supabase
  .from('file_annotations')
  .insert({
    file_url: fileUrl,
    file_name: fileName,
    annotation_type: annotationType,
    x_position: xPosition,
    y_position: yPosition,
    title,
    description,
    reviewer_name: user.user_metadata?.full_name,
    quality_review_id: qualityReviewId, // NEW: Link to quality review
    created_at: new Date().toISOString()
  });
```

### **Phase 3: Enhanced Designer Quality Page**
**File: `src/app/designer/quality/page.tsx`**

Add "View Annotated Files" functionality:

```tsx
{review.status === 'needs_revision' && (
  <div className="flex gap-2 mt-3">
    <Button 
      variant="outline" 
      onClick={() => viewAnnotatedFiles(review.id)}
    >
      <FileImage className="h-4 w-4 mr-2" />
      View Annotated Files
    </Button>
    <Button 
      variant="default" 
      onClick={() => navigateToProject(review.project_id)}
    >
      Update Project
    </Button>
  </div>
)}
```

## 🔧 **Save Annotations Fix**

### **Current Issue:**
```tsx
const handleSave = () => {
  if (onSaveAnnotations) {
    onSaveAnnotations(annotations); // Only calls prop, doesn't save to DB
  }
};
```

### **Enhanced Save Functionality:**
```tsx
const handleSave = async () => {
  try {
    setSaving(true);
    
    // Option 1: If using the callback pattern
    if (onSaveAnnotations) {
      await onSaveAnnotations(annotations);
    }
    
    // Option 2: Direct API save (for standalone use)
    else {
      const headers = await getAuthHeaders();
      const response = await fetch('/api/quality/annotations/bulk-save', {
        method: 'POST',
        headers,
        body: JSON.stringify({
          annotations,
          fileUrl,
          fileName,
          qualityReviewId
        })
      });
      
      if (!response.ok) {
        throw new Error('Failed to save annotations');
      }
    }
    
    // Show success feedback
    setShowSuccessMessage(true);
    setTimeout(() => setShowSuccessMessage(false), 3000);
    
  } catch (error) {
    console.error('Error saving annotations:', error);
    setShowErrorMessage(true);
    setTimeout(() => setShowErrorMessage(false), 5000);
  } finally {
    setSaving(false);
  }
};
```

## 📊 **Database Schema Updates**

### **Enhanced file_annotations table:**
```sql
ALTER TABLE file_annotations 
ADD COLUMN quality_review_id UUID REFERENCES quality_reviews(id),
ADD COLUMN project_id UUID REFERENCES projects(id),
ADD COLUMN is_visible_to_designer BOOLEAN DEFAULT true;

-- Index for performance
CREATE INDEX idx_file_annotations_quality_review 
ON file_annotations(quality_review_id);

CREATE INDEX idx_file_annotations_project 
ON file_annotations(project_id);
```

## 🎯 **Expected Outcomes**

After implementation:

### **✅ Designers Will Be Able To:**
1. **View annotated images** in their project details
2. **See visual feedback** from quality team on specific areas
3. **Understand revision requests** with precise visual context
4. **Navigate from quality reviews** to annotated files
5. **Track annotation history** across project revisions

### **✅ Quality Team Benefits:**
1. **More effective communication** through visual feedback
2. **Reduced back-and-forth** with clearer guidance
3. **Better designer education** through visual examples
4. **Audit trail** of feedback provided

### **✅ Platform Benefits:**
1. **Improved designer satisfaction** through better feedback
2. **Faster revision cycles** with clearer requirements
3. **Higher quality deliverables** through better communication
4. **Reduced support burden** with self-service feedback viewing

## 📁 **Files to Modify**

### **New Files:**
- `src/app/api/quality/annotations/bulk-save/route.ts` - Bulk save endpoint
- `src/components/designer/AnnotatedFileViewer.tsx` - Designer-specific file viewer

### **Modified Files:**
- `src/app/designer/projects/[id]/page.tsx` - Add annotation viewing
- `src/app/designer/quality/page.tsx` - Add "View Files" buttons
- `src/components/quality/FileAnnotationTool.tsx` - Enhanced save functionality
- `migrations/enhance_file_annotations.sql` - Database updates

## 🚀 **Implementation Priority**

1. **HIGH**: Fix save annotations functionality (immediate impact)
2. **HIGH**: Add designer file viewing in project details (core feature)
3. **MEDIUM**: Enhance quality page with file links (nice to have)
4. **LOW**: Advanced features like annotation history (future enhancement)

This implementation will complete the quality feedback loop and provide designers with the visual context they need to make effective revisions.
# Designer Annotation Viewing Enhancement Plan

## 🎯 **Objective**
Enable designers to view their submitted images with quality team annotations after reviews are completed.

## 📋 **Current Gap Analysis**

### ❌ **What's Missing:**
1. **Designer Project View**: No image annotation viewing in `/designer/projects/[id]`
2. **Quality Review Integration**: No connection between quality reviews and annotated files
3. **File Accessibility**: Designers can't access quality-annotated versions of their files
4. **Visual Feedback**: Only text feedback shown, not visual markup

### ✅ **What Exists:**
1. **FileAnnotationTool Component**: Fully functional annotation system
2. **Annotation API**: Working CRUD operations for annotations
3. **Quality Review System**: Basic feedback display
4. **File Storage**: Cloudflare R2 integration

## 🔧 **Implementation Plan**

### **Phase 1: Designer Project File Viewing**
**File: `src/app/designer/projects/[id]/page.tsx`**

Add a new section to show submitted files with annotations:

```tsx
// Add to existing project detail page
<div className="space-y-6">
  {/* Existing project info */}
  
  {/* NEW: Submitted Files with Annotations */}
  <Card>
    <CardHeader>
      <CardTitle>Submitted Files & Quality Feedback</CardTitle>
    </CardHeader>
    <CardContent>
      {submittedFiles.map((file) => (
        <div key={file.id} className="border rounded-lg p-4 mb-4">
          <div className="flex justify-between items-center mb-3">
            <h4 className="font-medium">{file.name}</h4>
            <Badge variant={getQualityStatusVariant(file.quality_status)}>
              {file.quality_status}
            </Badge>
          </div>
          
          {file.annotations?.length > 0 && (
            <div>
              <p className="text-sm text-gray-600 mb-3">
                Quality team has provided visual feedback on this file:
              </p>
              <FileAnnotationTool
                fileUrl={file.url}
                fileName={file.name}
                fileType={file.type}
                existingAnnotations={file.annotations}
                readOnly={true} // Designers can only view, not edit
                reviewerName="Quality Team"
              />
            </div>
          )}
        </div>
      ))}
    </CardContent>
  </Card>
</div>
```

### **Phase 2: Quality Review File Association**
**File: `src/app/api/quality/annotations/route.ts`**

Enhance the API to link annotations with quality reviews:

```typescript
// Add quality_review_id to annotation creation
const annotation = await supabase
  .from('file_annotations')
  .insert({
    file_url: fileUrl,
    file_name: fileName,
    annotation_type: annotationType,
    x_position: xPosition,
    y_position: yPosition,
    title,
    description,
    reviewer_name: user.user_metadata?.full_name,
    quality_review_id: qualityReviewId, // NEW: Link to quality review
    created_at: new Date().toISOString()
  });
```

### **Phase 3: Enhanced Designer Quality Page**
**File: `src/app/designer/quality/page.tsx`**

Add "View Annotated Files" functionality:

```tsx
{review.status === 'needs_revision' && (
  <div className="flex gap-2 mt-3">
    <Button 
      variant="outline" 
      onClick={() => viewAnnotatedFiles(review.id)}
    >
      <FileImage className="h-4 w-4 mr-2" />
      View Annotated Files
    </Button>
    <Button 
      variant="default" 
      onClick={() => navigateToProject(review.project_id)}
    >
      Update Project
    </Button>
  </div>
)}
```

## 🔧 **Save Annotations Fix**

### **Current Issue:**
```tsx
const handleSave = () => {
  if (onSaveAnnotations) {
    onSaveAnnotations(annotations); // Only calls prop, doesn't save to DB
  }
};
```

### **Enhanced Save Functionality:**
```tsx
const handleSave = async () => {
  try {
    setSaving(true);
    
    // Option 1: If using the callback pattern
    if (onSaveAnnotations) {
      await onSaveAnnotations(annotations);
    }
    
    // Option 2: Direct API save (for standalone use)
    else {
      const headers = await getAuthHeaders();
      const response = await fetch('/api/quality/annotations/bulk-save', {
        method: 'POST',
        headers,
        body: JSON.stringify({
          annotations,
          fileUrl,
          fileName,
          qualityReviewId
        })
      });
      
      if (!response.ok) {
        throw new Error('Failed to save annotations');
      }
    }
    
    // Show success feedback
    setShowSuccessMessage(true);
    setTimeout(() => setShowSuccessMessage(false), 3000);
    
  } catch (error) {
    console.error('Error saving annotations:', error);
    setShowErrorMessage(true);
    setTimeout(() => setShowErrorMessage(false), 5000);
  } finally {
    setSaving(false);
  }
};
```

## 📊 **Database Schema Updates**

### **Enhanced file_annotations table:**
```sql
ALTER TABLE file_annotations 
ADD COLUMN quality_review_id UUID REFERENCES quality_reviews(id),
ADD COLUMN project_id UUID REFERENCES projects(id),
ADD COLUMN is_visible_to_designer BOOLEAN DEFAULT true;

-- Index for performance
CREATE INDEX idx_file_annotations_quality_review 
ON file_annotations(quality_review_id);

CREATE INDEX idx_file_annotations_project 
ON file_annotations(project_id);
```

## 🎯 **Expected Outcomes**

After implementation:

### **✅ Designers Will Be Able To:**
1. **View annotated images** in their project details
2. **See visual feedback** from quality team on specific areas
3. **Understand revision requests** with precise visual context
4. **Navigate from quality reviews** to annotated files
5. **Track annotation history** across project revisions

### **✅ Quality Team Benefits:**
1. **More effective communication** through visual feedback
2. **Reduced back-and-forth** with clearer guidance
3. **Better designer education** through visual examples
4. **Audit trail** of feedback provided

### **✅ Platform Benefits:**
1. **Improved designer satisfaction** through better feedback
2. **Faster revision cycles** with clearer requirements
3. **Higher quality deliverables** through better communication
4. **Reduced support burden** with self-service feedback viewing

## 📁 **Files to Modify**

### **New Files:**
- `src/app/api/quality/annotations/bulk-save/route.ts` - Bulk save endpoint
- `src/components/designer/AnnotatedFileViewer.tsx` - Designer-specific file viewer

### **Modified Files:**
- `src/app/designer/projects/[id]/page.tsx` - Add annotation viewing
- `src/app/designer/quality/page.tsx` - Add "View Files" buttons
- `src/components/quality/FileAnnotationTool.tsx` - Enhanced save functionality
- `migrations/enhance_file_annotations.sql` - Database updates

## 🚀 **Implementation Priority**

1. **HIGH**: Fix save annotations functionality (immediate impact)
2. **HIGH**: Add designer file viewing in project details (core feature)
3. **MEDIUM**: Enhance quality page with file links (nice to have)
4. **LOW**: Advanced features like annotation history (future enhancement)

This implementation will complete the quality feedback loop and provide designers with the visual context they need to make effective revisions.
# Designer Annotation Viewing Enhancement Plan

## 🎯 **Objective**
Enable designers to view their submitted images with quality team annotations after reviews are completed.

## 📋 **Current Gap Analysis**

### ❌ **What's Missing:**
1. **Designer Project View**: No image annotation viewing in `/designer/projects/[id]`
2. **Quality Review Integration**: No connection between quality reviews and annotated files
3. **File Accessibility**: Designers can't access quality-annotated versions of their files
4. **Visual Feedback**: Only text feedback shown, not visual markup

### ✅ **What Exists:**
1. **FileAnnotationTool Component**: Fully functional annotation system
2. **Annotation API**: Working CRUD operations for annotations
3. **Quality Review System**: Basic feedback display
4. **File Storage**: Cloudflare R2 integration

## 🔧 **Implementation Plan**

### **Phase 1: Designer Project File Viewing**
**File: `src/app/designer/projects/[id]/page.tsx`**

Add a new section to show submitted files with annotations:

```tsx
// Add to existing project detail page
<div className="space-y-6">
  {/* Existing project info */}
  
  {/* NEW: Submitted Files with Annotations */}
  <Card>
    <CardHeader>
      <CardTitle>Submitted Files & Quality Feedback</CardTitle>
    </CardHeader>
    <CardContent>
      {submittedFiles.map((file) => (
        <div key={file.id} className="border rounded-lg p-4 mb-4">
          <div className="flex justify-between items-center mb-3">
            <h4 className="font-medium">{file.name}</h4>
            <Badge variant={getQualityStatusVariant(file.quality_status)}>
              {file.quality_status}
            </Badge>
          </div>
          
          {file.annotations?.length > 0 && (
            <div>
              <p className="text-sm text-gray-600 mb-3">
                Quality team has provided visual feedback on this file:
              </p>
              <FileAnnotationTool
                fileUrl={file.url}
                fileName={file.name}
                fileType={file.type}
                existingAnnotations={file.annotations}
                readOnly={true} // Designers can only view, not edit
                reviewerName="Quality Team"
              />
            </div>
          )}
        </div>
      ))}
    </CardContent>
  </Card>
</div>
```

### **Phase 2: Quality Review File Association**
**File: `src/app/api/quality/annotations/route.ts`**

Enhance the API to link annotations with quality reviews:

```typescript
// Add quality_review_id to annotation creation
const annotation = await supabase
  .from('file_annotations')
  .insert({
    file_url: fileUrl,
    file_name: fileName,
    annotation_type: annotationType,
    x_position: xPosition,
    y_position: yPosition,
    title,
    description,
    reviewer_name: user.user_metadata?.full_name,
    quality_review_id: qualityReviewId, // NEW: Link to quality review
    created_at: new Date().toISOString()
  });
```

### **Phase 3: Enhanced Designer Quality Page**
**File: `src/app/designer/quality/page.tsx`**

Add "View Annotated Files" functionality:

```tsx
{review.status === 'needs_revision' && (
  <div className="flex gap-2 mt-3">
    <Button 
      variant="outline" 
      onClick={() => viewAnnotatedFiles(review.id)}
    >
      <FileImage className="h-4 w-4 mr-2" />
      View Annotated Files
    </Button>
    <Button 
      variant="default" 
      onClick={() => navigateToProject(review.project_id)}
    >
      Update Project
    </Button>
  </div>
)}
```

## 🔧 **Save Annotations Fix**

### **Current Issue:**
```tsx
const handleSave = () => {
  if (onSaveAnnotations) {
    onSaveAnnotations(annotations); // Only calls prop, doesn't save to DB
  }
};
```

### **Enhanced Save Functionality:**
```tsx
const handleSave = async () => {
  try {
    setSaving(true);
    
    // Option 1: If using the callback pattern
    if (onSaveAnnotations) {
      await onSaveAnnotations(annotations);
    }
    
    // Option 2: Direct API save (for standalone use)
    else {
      const headers = await getAuthHeaders();
      const response = await fetch('/api/quality/annotations/bulk-save', {
        method: 'POST',
        headers,
        body: JSON.stringify({
          annotations,
          fileUrl,
          fileName,
          qualityReviewId
        })
      });
      
      if (!response.ok) {
        throw new Error('Failed to save annotations');
      }
    }
    
    // Show success feedback
    setShowSuccessMessage(true);
    setTimeout(() => setShowSuccessMessage(false), 3000);
    
  } catch (error) {
    console.error('Error saving annotations:', error);
    setShowErrorMessage(true);
    setTimeout(() => setShowErrorMessage(false), 5000);
  } finally {
    setSaving(false);
  }
};
```

## 📊 **Database Schema Updates**

### **Enhanced file_annotations table:**
```sql
ALTER TABLE file_annotations 
ADD COLUMN quality_review_id UUID REFERENCES quality_reviews(id),
ADD COLUMN project_id UUID REFERENCES projects(id),
ADD COLUMN is_visible_to_designer BOOLEAN DEFAULT true;

-- Index for performance
CREATE INDEX idx_file_annotations_quality_review 
ON file_annotations(quality_review_id);

CREATE INDEX idx_file_annotations_project 
ON file_annotations(project_id);
```

## 🎯 **Expected Outcomes**

After implementation:

### **✅ Designers Will Be Able To:**
1. **View annotated images** in their project details
2. **See visual feedback** from quality team on specific areas
3. **Understand revision requests** with precise visual context
4. **Navigate from quality reviews** to annotated files
5. **Track annotation history** across project revisions

### **✅ Quality Team Benefits:**
1. **More effective communication** through visual feedback
2. **Reduced back-and-forth** with clearer guidance
3. **Better designer education** through visual examples
4. **Audit trail** of feedback provided

### **✅ Platform Benefits:**
1. **Improved designer satisfaction** through better feedback
2. **Faster revision cycles** with clearer requirements
3. **Higher quality deliverables** through better communication
4. **Reduced support burden** with self-service feedback viewing

## 📁 **Files to Modify**

### **New Files:**
- `src/app/api/quality/annotations/bulk-save/route.ts` - Bulk save endpoint
- `src/components/designer/AnnotatedFileViewer.tsx` - Designer-specific file viewer

### **Modified Files:**
- `src/app/designer/projects/[id]/page.tsx` - Add annotation viewing
- `src/app/designer/quality/page.tsx` - Add "View Files" buttons
- `src/components/quality/FileAnnotationTool.tsx` - Enhanced save functionality
- `migrations/enhance_file_annotations.sql` - Database updates

## 🚀 **Implementation Priority**

1. **HIGH**: Fix save annotations functionality (immediate impact)
2. **HIGH**: Add designer file viewing in project details (core feature)
3. **MEDIUM**: Enhance quality page with file links (nice to have)
4. **LOW**: Advanced features like annotation history (future enhancement)

This implementation will complete the quality feedback loop and provide designers with the visual context they need to make effective revisions.
# Designer Annotation Viewing Enhancement Plan

## 🎯 **Objective**
Enable designers to view their submitted images with quality team annotations after reviews are completed.

## 📋 **Current Gap Analysis**

### ❌ **What's Missing:**
1. **Designer Project View**: No image annotation viewing in `/designer/projects/[id]`
2. **Quality Review Integration**: No connection between quality reviews and annotated files
3. **File Accessibility**: Designers can't access quality-annotated versions of their files
4. **Visual Feedback**: Only text feedback shown, not visual markup

### ✅ **What Exists:**
1. **FileAnnotationTool Component**: Fully functional annotation system
2. **Annotation API**: Working CRUD operations for annotations
3. **Quality Review System**: Basic feedback display
4. **File Storage**: Cloudflare R2 integration

## 🔧 **Implementation Plan**

### **Phase 1: Designer Project File Viewing**
**File: `src/app/designer/projects/[id]/page.tsx`**

Add a new section to show submitted files with annotations:

```tsx
// Add to existing project detail page
<div className="space-y-6">
  {/* Existing project info */}
  
  {/* NEW: Submitted Files with Annotations */}
  <Card>
    <CardHeader>
      <CardTitle>Submitted Files & Quality Feedback</CardTitle>
    </CardHeader>
    <CardContent>
      {submittedFiles.map((file) => (
        <div key={file.id} className="border rounded-lg p-4 mb-4">
          <div className="flex justify-between items-center mb-3">
            <h4 className="font-medium">{file.name}</h4>
            <Badge variant={getQualityStatusVariant(file.quality_status)}>
              {file.quality_status}
            </Badge>
          </div>
          
          {file.annotations?.length > 0 && (
            <div>
              <p className="text-sm text-gray-600 mb-3">
                Quality team has provided visual feedback on this file:
              </p>
              <FileAnnotationTool
                fileUrl={file.url}
                fileName={file.name}
                fileType={file.type}
                existingAnnotations={file.annotations}
                readOnly={true} // Designers can only view, not edit
                reviewerName="Quality Team"
              />
            </div>
          )}
        </div>
      ))}
    </CardContent>
  </Card>
</div>
```

### **Phase 2: Quality Review File Association**
**File: `src/app/api/quality/annotations/route.ts`**

Enhance the API to link annotations with quality reviews:

```typescript
// Add quality_review_id to annotation creation
const annotation = await supabase
  .from('file_annotations')
  .insert({
    file_url: fileUrl,
    file_name: fileName,
    annotation_type: annotationType,
    x_position: xPosition,
    y_position: yPosition,
    title,
    description,
    reviewer_name: user.user_metadata?.full_name,
    quality_review_id: qualityReviewId, // NEW: Link to quality review
    created_at: new Date().toISOString()
  });
```

### **Phase 3: Enhanced Designer Quality Page**
**File: `src/app/designer/quality/page.tsx`**

Add "View Annotated Files" functionality:

```tsx
{review.status === 'needs_revision' && (
  <div className="flex gap-2 mt-3">
    <Button 
      variant="outline" 
      onClick={() => viewAnnotatedFiles(review.id)}
    >
      <FileImage className="h-4 w-4 mr-2" />
      View Annotated Files
    </Button>
    <Button 
      variant="default" 
      onClick={() => navigateToProject(review.project_id)}
    >
      Update Project
    </Button>
  </div>
)}
```

## 🔧 **Save Annotations Fix**

### **Current Issue:**
```tsx
const handleSave = () => {
  if (onSaveAnnotations) {
    onSaveAnnotations(annotations); // Only calls prop, doesn't save to DB
  }
};
```

### **Enhanced Save Functionality:**
```tsx
const handleSave = async () => {
  try {
    setSaving(true);
    
    // Option 1: If using the callback pattern
    if (onSaveAnnotations) {
      await onSaveAnnotations(annotations);
    }
    
    // Option 2: Direct API save (for standalone use)
    else {
      const headers = await getAuthHeaders();
      const response = await fetch('/api/quality/annotations/bulk-save', {
        method: 'POST',
        headers,
        body: JSON.stringify({
          annotations,
          fileUrl,
          fileName,
          qualityReviewId
        })
      });
      
      if (!response.ok) {
        throw new Error('Failed to save annotations');
      }
    }
    
    // Show success feedback
    setShowSuccessMessage(true);
    setTimeout(() => setShowSuccessMessage(false), 3000);
    
  } catch (error) {
    console.error('Error saving annotations:', error);
    setShowErrorMessage(true);
    setTimeout(() => setShowErrorMessage(false), 5000);
  } finally {
    setSaving(false);
  }
};
```

## 📊 **Database Schema Updates**

### **Enhanced file_annotations table:**
```sql
ALTER TABLE file_annotations 
ADD COLUMN quality_review_id UUID REFERENCES quality_reviews(id),
ADD COLUMN project_id UUID REFERENCES projects(id),
ADD COLUMN is_visible_to_designer BOOLEAN DEFAULT true;

-- Index for performance
CREATE INDEX idx_file_annotations_quality_review 
ON file_annotations(quality_review_id);

CREATE INDEX idx_file_annotations_project 
ON file_annotations(project_id);
```

## 🎯 **Expected Outcomes**

After implementation:

### **✅ Designers Will Be Able To:**
1. **View annotated images** in their project details
2. **See visual feedback** from quality team on specific areas
3. **Understand revision requests** with precise visual context
4. **Navigate from quality reviews** to annotated files
5. **Track annotation history** across project revisions

### **✅ Quality Team Benefits:**
1. **More effective communication** through visual feedback
2. **Reduced back-and-forth** with clearer guidance
3. **Better designer education** through visual examples
4. **Audit trail** of feedback provided

### **✅ Platform Benefits:**
1. **Improved designer satisfaction** through better feedback
2. **Faster revision cycles** with clearer requirements
3. **Higher quality deliverables** through better communication
4. **Reduced support burden** with self-service feedback viewing

## 📁 **Files to Modify**

### **New Files:**
- `src/app/api/quality/annotations/bulk-save/route.ts` - Bulk save endpoint
- `src/components/designer/AnnotatedFileViewer.tsx` - Designer-specific file viewer

### **Modified Files:**
- `src/app/designer/projects/[id]/page.tsx` - Add annotation viewing
- `src/app/designer/quality/page.tsx` - Add "View Files" buttons
- `src/components/quality/FileAnnotationTool.tsx` - Enhanced save functionality
- `migrations/enhance_file_annotations.sql` - Database updates

## 🚀 **Implementation Priority**

1. **HIGH**: Fix save annotations functionality (immediate impact)
2. **HIGH**: Add designer file viewing in project details (core feature)
3. **MEDIUM**: Enhance quality page with file links (nice to have)
4. **LOW**: Advanced features like annotation history (future enhancement)

This implementation will complete the quality feedback loop and provide designers with the visual context they need to make effective revisions.
# Designer Annotation Viewing Enhancement Plan

## 🎯 **Objective**
Enable designers to view their submitted images with quality team annotations after reviews are completed.

## 📋 **Current Gap Analysis**

### ❌ **What's Missing:**
1. **Designer Project View**: No image annotation viewing in `/designer/projects/[id]`
2. **Quality Review Integration**: No connection between quality reviews and annotated files
3. **File Accessibility**: Designers can't access quality-annotated versions of their files
4. **Visual Feedback**: Only text feedback shown, not visual markup

### ✅ **What Exists:**
1. **FileAnnotationTool Component**: Fully functional annotation system
2. **Annotation API**: Working CRUD operations for annotations
3. **Quality Review System**: Basic feedback display
4. **File Storage**: Cloudflare R2 integration

## 🔧 **Implementation Plan**

### **Phase 1: Designer Project File Viewing**
**File: `src/app/designer/projects/[id]/page.tsx`**

Add a new section to show submitted files with annotations:

```tsx
// Add to existing project detail page
<div className="space-y-6">
  {/* Existing project info */}
  
  {/* NEW: Submitted Files with Annotations */}
  <Card>
    <CardHeader>
      <CardTitle>Submitted Files & Quality Feedback</CardTitle>
    </CardHeader>
    <CardContent>
      {submittedFiles.map((file) => (
        <div key={file.id} className="border rounded-lg p-4 mb-4">
          <div className="flex justify-between items-center mb-3">
            <h4 className="font-medium">{file.name}</h4>
            <Badge variant={getQualityStatusVariant(file.quality_status)}>
              {file.quality_status}
            </Badge>
          </div>
          
          {file.annotations?.length > 0 && (
            <div>
              <p className="text-sm text-gray-600 mb-3">
                Quality team has provided visual feedback on this file:
              </p>
              <FileAnnotationTool
                fileUrl={file.url}
                fileName={file.name}
                fileType={file.type}
                existingAnnotations={file.annotations}
                readOnly={true} // Designers can only view, not edit
                reviewerName="Quality Team"
              />
            </div>
          )}
        </div>
      ))}
    </CardContent>
  </Card>
</div>
```

### **Phase 2: Quality Review File Association**
**File: `src/app/api/quality/annotations/route.ts`**

Enhance the API to link annotations with quality reviews:

```typescript
// Add quality_review_id to annotation creation
const annotation = await supabase
  .from('file_annotations')
  .insert({
    file_url: fileUrl,
    file_name: fileName,
    annotation_type: annotationType,
    x_position: xPosition,
    y_position: yPosition,
    title,
    description,
    reviewer_name: user.user_metadata?.full_name,
    quality_review_id: qualityReviewId, // NEW: Link to quality review
    created_at: new Date().toISOString()
  });
```

### **Phase 3: Enhanced Designer Quality Page**
**File: `src/app/designer/quality/page.tsx`**

Add "View Annotated Files" functionality:

```tsx
{review.status === 'needs_revision' && (
  <div className="flex gap-2 mt-3">
    <Button 
      variant="outline" 
      onClick={() => viewAnnotatedFiles(review.id)}
    >
      <FileImage className="h-4 w-4 mr-2" />
      View Annotated Files
    </Button>
    <Button 
      variant="default" 
      onClick={() => navigateToProject(review.project_id)}
    >
      Update Project
    </Button>
  </div>
)}
```

## 🔧 **Save Annotations Fix**

### **Current Issue:**
```tsx
const handleSave = () => {
  if (onSaveAnnotations) {
    onSaveAnnotations(annotations); // Only calls prop, doesn't save to DB
  }
};
```

### **Enhanced Save Functionality:**
```tsx
const handleSave = async () => {
  try {
    setSaving(true);
    
    // Option 1: If using the callback pattern
    if (onSaveAnnotations) {
      await onSaveAnnotations(annotations);
    }
    
    // Option 2: Direct API save (for standalone use)
    else {
      const headers = await getAuthHeaders();
      const response = await fetch('/api/quality/annotations/bulk-save', {
        method: 'POST',
        headers,
        body: JSON.stringify({
          annotations,
          fileUrl,
          fileName,
          qualityReviewId
        })
      });
      
      if (!response.ok) {
        throw new Error('Failed to save annotations');
      }
    }
    
    // Show success feedback
    setShowSuccessMessage(true);
    setTimeout(() => setShowSuccessMessage(false), 3000);
    
  } catch (error) {
    console.error('Error saving annotations:', error);
    setShowErrorMessage(true);
    setTimeout(() => setShowErrorMessage(false), 5000);
  } finally {
    setSaving(false);
  }
};
```

## 📊 **Database Schema Updates**

### **Enhanced file_annotations table:**
```sql
ALTER TABLE file_annotations 
ADD COLUMN quality_review_id UUID REFERENCES quality_reviews(id),
ADD COLUMN project_id UUID REFERENCES projects(id),
ADD COLUMN is_visible_to_designer BOOLEAN DEFAULT true;

-- Index for performance
CREATE INDEX idx_file_annotations_quality_review 
ON file_annotations(quality_review_id);

CREATE INDEX idx_file_annotations_project 
ON file_annotations(project_id);
```

## 🎯 **Expected Outcomes**

After implementation:

### **✅ Designers Will Be Able To:**
1. **View annotated images** in their project details
2. **See visual feedback** from quality team on specific areas
3. **Understand revision requests** with precise visual context
4. **Navigate from quality reviews** to annotated files
5. **Track annotation history** across project revisions

### **✅ Quality Team Benefits:**
1. **More effective communication** through visual feedback
2. **Reduced back-and-forth** with clearer guidance
3. **Better designer education** through visual examples
4. **Audit trail** of feedback provided

### **✅ Platform Benefits:**
1. **Improved designer satisfaction** through better feedback
2. **Faster revision cycles** with clearer requirements
3. **Higher quality deliverables** through better communication
4. **Reduced support burden** with self-service feedback viewing

## 📁 **Files to Modify**

### **New Files:**
- `src/app/api/quality/annotations/bulk-save/route.ts` - Bulk save endpoint
- `src/components/designer/AnnotatedFileViewer.tsx` - Designer-specific file viewer

### **Modified Files:**
- `src/app/designer/projects/[id]/page.tsx` - Add annotation viewing
- `src/app/designer/quality/page.tsx` - Add "View Files" buttons
- `src/components/quality/FileAnnotationTool.tsx` - Enhanced save functionality
- `migrations/enhance_file_annotations.sql` - Database updates

## 🚀 **Implementation Priority**

1. **HIGH**: Fix save annotations functionality (immediate impact)
2. **HIGH**: Add designer file viewing in project details (core feature)
3. **MEDIUM**: Enhance quality page with file links (nice to have)
4. **LOW**: Advanced features like annotation history (future enhancement)

This implementation will complete the quality feedback loop and provide designers with the visual context they need to make effective revisions.
# Designer Annotation Viewing Enhancement Plan

## 🎯 **Objective**
Enable designers to view their submitted images with quality team annotations after reviews are completed.

## 📋 **Current Gap Analysis**

### ❌ **What's Missing:**
1. **Designer Project View**: No image annotation viewing in `/designer/projects/[id]`
2. **Quality Review Integration**: No connection between quality reviews and annotated files
3. **File Accessibility**: Designers can't access quality-annotated versions of their files
4. **Visual Feedback**: Only text feedback shown, not visual markup

### ✅ **What Exists:**
1. **FileAnnotationTool Component**: Fully functional annotation system
2. **Annotation API**: Working CRUD operations for annotations
3. **Quality Review System**: Basic feedback display
4. **File Storage**: Cloudflare R2 integration

## 🔧 **Implementation Plan**

### **Phase 1: Designer Project File Viewing**
**File: `src/app/designer/projects/[id]/page.tsx`**

Add a new section to show submitted files with annotations:

```tsx
// Add to existing project detail page
<div className="space-y-6">
  {/* Existing project info */}
  
  {/* NEW: Submitted Files with Annotations */}
  <Card>
    <CardHeader>
      <CardTitle>Submitted Files & Quality Feedback</CardTitle>
    </CardHeader>
    <CardContent>
      {submittedFiles.map((file) => (
        <div key={file.id} className="border rounded-lg p-4 mb-4">
          <div className="flex justify-between items-center mb-3">
            <h4 className="font-medium">{file.name}</h4>
            <Badge variant={getQualityStatusVariant(file.quality_status)}>
              {file.quality_status}
            </Badge>
          </div>
          
          {file.annotations?.length > 0 && (
            <div>
              <p className="text-sm text-gray-600 mb-3">
                Quality team has provided visual feedback on this file:
              </p>
              <FileAnnotationTool
                fileUrl={file.url}
                fileName={file.name}
                fileType={file.type}
                existingAnnotations={file.annotations}
                readOnly={true} // Designers can only view, not edit
                reviewerName="Quality Team"
              />
            </div>
          )}
        </div>
      ))}
    </CardContent>
  </Card>
</div>
```

### **Phase 2: Quality Review File Association**
**File: `src/app/api/quality/annotations/route.ts`**

Enhance the API to link annotations with quality reviews:

```typescript
// Add quality_review_id to annotation creation
const annotation = await supabase
  .from('file_annotations')
  .insert({
    file_url: fileUrl,
    file_name: fileName,
    annotation_type: annotationType,
    x_position: xPosition,
    y_position: yPosition,
    title,
    description,
    reviewer_name: user.user_metadata?.full_name,
    quality_review_id: qualityReviewId, // NEW: Link to quality review
    created_at: new Date().toISOString()
  });
```

### **Phase 3: Enhanced Designer Quality Page**
**File: `src/app/designer/quality/page.tsx`**

Add "View Annotated Files" functionality:

```tsx
{review.status === 'needs_revision' && (
  <div className="flex gap-2 mt-3">
    <Button 
      variant="outline" 
      onClick={() => viewAnnotatedFiles(review.id)}
    >
      <FileImage className="h-4 w-4 mr-2" />
      View Annotated Files
    </Button>
    <Button 
      variant="default" 
      onClick={() => navigateToProject(review.project_id)}
    >
      Update Project
    </Button>
  </div>
)}
```

## 🔧 **Save Annotations Fix**

### **Current Issue:**
```tsx
const handleSave = () => {
  if (onSaveAnnotations) {
    onSaveAnnotations(annotations); // Only calls prop, doesn't save to DB
  }
};
```

### **Enhanced Save Functionality:**
```tsx
const handleSave = async () => {
  try {
    setSaving(true);
    
    // Option 1: If using the callback pattern
    if (onSaveAnnotations) {
      await onSaveAnnotations(annotations);
    }
    
    // Option 2: Direct API save (for standalone use)
    else {
      const headers = await getAuthHeaders();
      const response = await fetch('/api/quality/annotations/bulk-save', {
        method: 'POST',
        headers,
        body: JSON.stringify({
          annotations,
          fileUrl,
          fileName,
          qualityReviewId
        })
      });
      
      if (!response.ok) {
        throw new Error('Failed to save annotations');
      }
    }
    
    // Show success feedback
    setShowSuccessMessage(true);
    setTimeout(() => setShowSuccessMessage(false), 3000);
    
  } catch (error) {
    console.error('Error saving annotations:', error);
    setShowErrorMessage(true);
    setTimeout(() => setShowErrorMessage(false), 5000);
  } finally {
    setSaving(false);
  }
};
```

## 📊 **Database Schema Updates**

### **Enhanced file_annotations table:**
```sql
ALTER TABLE file_annotations 
ADD COLUMN quality_review_id UUID REFERENCES quality_reviews(id),
ADD COLUMN project_id UUID REFERENCES projects(id),
ADD COLUMN is_visible_to_designer BOOLEAN DEFAULT true;

-- Index for performance
CREATE INDEX idx_file_annotations_quality_review 
ON file_annotations(quality_review_id);

CREATE INDEX idx_file_annotations_project 
ON file_annotations(project_id);
```

## 🎯 **Expected Outcomes**

After implementation:

### **✅ Designers Will Be Able To:**
1. **View annotated images** in their project details
2. **See visual feedback** from quality team on specific areas
3. **Understand revision requests** with precise visual context
4. **Navigate from quality reviews** to annotated files
5. **Track annotation history** across project revisions

### **✅ Quality Team Benefits:**
1. **More effective communication** through visual feedback
2. **Reduced back-and-forth** with clearer guidance
3. **Better designer education** through visual examples
4. **Audit trail** of feedback provided

### **✅ Platform Benefits:**
1. **Improved designer satisfaction** through better feedback
2. **Faster revision cycles** with clearer requirements
3. **Higher quality deliverables** through better communication
4. **Reduced support burden** with self-service feedback viewing

## 📁 **Files to Modify**

### **New Files:**
- `src/app/api/quality/annotations/bulk-save/route.ts` - Bulk save endpoint
- `src/components/designer/AnnotatedFileViewer.tsx` - Designer-specific file viewer

### **Modified Files:**
- `src/app/designer/projects/[id]/page.tsx` - Add annotation viewing
- `src/app/designer/quality/page.tsx` - Add "View Files" buttons
- `src/components/quality/FileAnnotationTool.tsx` - Enhanced save functionality
- `migrations/enhance_file_annotations.sql` - Database updates

## 🚀 **Implementation Priority**

1. **HIGH**: Fix save annotations functionality (immediate impact)
2. **HIGH**: Add designer file viewing in project details (core feature)
3. **MEDIUM**: Enhance quality page with file links (nice to have)
4. **LOW**: Advanced features like annotation history (future enhancement)

This implementation will complete the quality feedback loop and provide designers with the visual context they need to make effective revisions.
# Designer Annotation Viewing Enhancement Plan

## 🎯 **Objective**
Enable designers to view their submitted images with quality team annotations after reviews are completed.

## 📋 **Current Gap Analysis**

### ❌ **What's Missing:**
1. **Designer Project View**: No image annotation viewing in `/designer/projects/[id]`
2. **Quality Review Integration**: No connection between quality reviews and annotated files
3. **File Accessibility**: Designers can't access quality-annotated versions of their files
4. **Visual Feedback**: Only text feedback shown, not visual markup

### ✅ **What Exists:**
1. **FileAnnotationTool Component**: Fully functional annotation system
2. **Annotation API**: Working CRUD operations for annotations
3. **Quality Review System**: Basic feedback display
4. **File Storage**: Cloudflare R2 integration

## 🔧 **Implementation Plan**

### **Phase 1: Designer Project File Viewing**
**File: `src/app/designer/projects/[id]/page.tsx`**

Add a new section to show submitted files with annotations:

```tsx
// Add to existing project detail page
<div className="space-y-6">
  {/* Existing project info */}
  
  {/* NEW: Submitted Files with Annotations */}
  <Card>
    <CardHeader>
      <CardTitle>Submitted Files & Quality Feedback</CardTitle>
    </CardHeader>
    <CardContent>
      {submittedFiles.map((file) => (
        <div key={file.id} className="border rounded-lg p-4 mb-4">
          <div className="flex justify-between items-center mb-3">
            <h4 className="font-medium">{file.name}</h4>
            <Badge variant={getQualityStatusVariant(file.quality_status)}>
              {file.quality_status}
            </Badge>
          </div>
          
          {file.annotations?.length > 0 && (
            <div>
              <p className="text-sm text-gray-600 mb-3">
                Quality team has provided visual feedback on this file:
              </p>
              <FileAnnotationTool
                fileUrl={file.url}
                fileName={file.name}
                fileType={file.type}
                existingAnnotations={file.annotations}
                readOnly={true} // Designers can only view, not edit
                reviewerName="Quality Team"
              />
            </div>
          )}
        </div>
      ))}
    </CardContent>
  </Card>
</div>
```

### **Phase 2: Quality Review File Association**
**File: `src/app/api/quality/annotations/route.ts`**

Enhance the API to link annotations with quality reviews:

```typescript
// Add quality_review_id to annotation creation
const annotation = await supabase
  .from('file_annotations')
  .insert({
    file_url: fileUrl,
    file_name: fileName,
    annotation_type: annotationType,
    x_position: xPosition,
    y_position: yPosition,
    title,
    description,
    reviewer_name: user.user_metadata?.full_name,
    quality_review_id: qualityReviewId, // NEW: Link to quality review
    created_at: new Date().toISOString()
  });
```

### **Phase 3: Enhanced Designer Quality Page**
**File: `src/app/designer/quality/page.tsx`**

Add "View Annotated Files" functionality:

```tsx
{review.status === 'needs_revision' && (
  <div className="flex gap-2 mt-3">
    <Button 
      variant="outline" 
      onClick={() => viewAnnotatedFiles(review.id)}
    >
      <FileImage className="h-4 w-4 mr-2" />
      View Annotated Files
    </Button>
    <Button 
      variant="default" 
      onClick={() => navigateToProject(review.project_id)}
    >
      Update Project
    </Button>
  </div>
)}
```

## 🔧 **Save Annotations Fix**

### **Current Issue:**
```tsx
const handleSave = () => {
  if (onSaveAnnotations) {
    onSaveAnnotations(annotations); // Only calls prop, doesn't save to DB
  }
};
```

### **Enhanced Save Functionality:**
```tsx
const handleSave = async () => {
  try {
    setSaving(true);
    
    // Option 1: If using the callback pattern
    if (onSaveAnnotations) {
      await onSaveAnnotations(annotations);
    }
    
    // Option 2: Direct API save (for standalone use)
    else {
      const headers = await getAuthHeaders();
      const response = await fetch('/api/quality/annotations/bulk-save', {
        method: 'POST',
        headers,
        body: JSON.stringify({
          annotations,
          fileUrl,
          fileName,
          qualityReviewId
        })
      });
      
      if (!response.ok) {
        throw new Error('Failed to save annotations');
      }
    }
    
    // Show success feedback
    setShowSuccessMessage(true);
    setTimeout(() => setShowSuccessMessage(false), 3000);
    
  } catch (error) {
    console.error('Error saving annotations:', error);
    setShowErrorMessage(true);
    setTimeout(() => setShowErrorMessage(false), 5000);
  } finally {
    setSaving(false);
  }
};
```

## 📊 **Database Schema Updates**

### **Enhanced file_annotations table:**
```sql
ALTER TABLE file_annotations 
ADD COLUMN quality_review_id UUID REFERENCES quality_reviews(id),
ADD COLUMN project_id UUID REFERENCES projects(id),
ADD COLUMN is_visible_to_designer BOOLEAN DEFAULT true;

-- Index for performance
CREATE INDEX idx_file_annotations_quality_review 
ON file_annotations(quality_review_id);

CREATE INDEX idx_file_annotations_project 
ON file_annotations(project_id);
```

## 🎯 **Expected Outcomes**

After implementation:

### **✅ Designers Will Be Able To:**
1. **View annotated images** in their project details
2. **See visual feedback** from quality team on specific areas
3. **Understand revision requests** with precise visual context
4. **Navigate from quality reviews** to annotated files
5. **Track annotation history** across project revisions

### **✅ Quality Team Benefits:**
1. **More effective communication** through visual feedback
2. **Reduced back-and-forth** with clearer guidance
3. **Better designer education** through visual examples
4. **Audit trail** of feedback provided

### **✅ Platform Benefits:**
1. **Improved designer satisfaction** through better feedback
2. **Faster revision cycles** with clearer requirements
3. **Higher quality deliverables** through better communication
4. **Reduced support burden** with self-service feedback viewing

## 📁 **Files to Modify**

### **New Files:**
- `src/app/api/quality/annotations/bulk-save/route.ts` - Bulk save endpoint
- `src/components/designer/AnnotatedFileViewer.tsx` - Designer-specific file viewer

### **Modified Files:**
- `src/app/designer/projects/[id]/page.tsx` - Add annotation viewing
- `src/app/designer/quality/page.tsx` - Add "View Files" buttons
- `src/components/quality/FileAnnotationTool.tsx` - Enhanced save functionality
- `migrations/enhance_file_annotations.sql` - Database updates

## 🚀 **Implementation Priority**

1. **HIGH**: Fix save annotations functionality (immediate impact)
2. **HIGH**: Add designer file viewing in project details (core feature)
3. **MEDIUM**: Enhance quality page with file links (nice to have)
4. **LOW**: Advanced features like annotation history (future enhancement)

This implementation will complete the quality feedback loop and provide designers with the visual context they need to make effective revisions.
# Designer Annotation Viewing Enhancement Plan

## 🎯 **Objective**
Enable designers to view their submitted images with quality team annotations after reviews are completed.

## 📋 **Current Gap Analysis**

### ❌ **What's Missing:**
1. **Designer Project View**: No image annotation viewing in `/designer/projects/[id]`
2. **Quality Review Integration**: No connection between quality reviews and annotated files
3. **File Accessibility**: Designers can't access quality-annotated versions of their files
4. **Visual Feedback**: Only text feedback shown, not visual markup

### ✅ **What Exists:**
1. **FileAnnotationTool Component**: Fully functional annotation system
2. **Annotation API**: Working CRUD operations for annotations
3. **Quality Review System**: Basic feedback display
4. **File Storage**: Cloudflare R2 integration

## 🔧 **Implementation Plan**

### **Phase 1: Designer Project File Viewing**
**File: `src/app/designer/projects/[id]/page.tsx`**

Add a new section to show submitted files with annotations:

```tsx
// Add to existing project detail page
<div className="space-y-6">
  {/* Existing project info */}
  
  {/* NEW: Submitted Files with Annotations */}
  <Card>
    <CardHeader>
      <CardTitle>Submitted Files & Quality Feedback</CardTitle>
    </CardHeader>
    <CardContent>
      {submittedFiles.map((file) => (
        <div key={file.id} className="border rounded-lg p-4 mb-4">
          <div className="flex justify-between items-center mb-3">
            <h4 className="font-medium">{file.name}</h4>
            <Badge variant={getQualityStatusVariant(file.quality_status)}>
              {file.quality_status}
            </Badge>
          </div>
          
          {file.annotations?.length > 0 && (
            <div>
              <p className="text-sm text-gray-600 mb-3">
                Quality team has provided visual feedback on this file:
              </p>
              <FileAnnotationTool
                fileUrl={file.url}
                fileName={file.name}
                fileType={file.type}
                existingAnnotations={file.annotations}
                readOnly={true} // Designers can only view, not edit
                reviewerName="Quality Team"
              />
            </div>
          )}
        </div>
      ))}
    </CardContent>
  </Card>
</div>
```

### **Phase 2: Quality Review File Association**
**File: `src/app/api/quality/annotations/route.ts`**

Enhance the API to link annotations with quality reviews:

```typescript
// Add quality_review_id to annotation creation
const annotation = await supabase
  .from('file_annotations')
  .insert({
    file_url: fileUrl,
    file_name: fileName,
    annotation_type: annotationType,
    x_position: xPosition,
    y_position: yPosition,
    title,
    description,
    reviewer_name: user.user_metadata?.full_name,
    quality_review_id: qualityReviewId, // NEW: Link to quality review
    created_at: new Date().toISOString()
  });
```

### **Phase 3: Enhanced Designer Quality Page**
**File: `src/app/designer/quality/page.tsx`**

Add "View Annotated Files" functionality:

```tsx
{review.status === 'needs_revision' && (
  <div className="flex gap-2 mt-3">
    <Button 
      variant="outline" 
      onClick={() => viewAnnotatedFiles(review.id)}
    >
      <FileImage className="h-4 w-4 mr-2" />
      View Annotated Files
    </Button>
    <Button 
      variant="default" 
      onClick={() => navigateToProject(review.project_id)}
    >
      Update Project
    </Button>
  </div>
)}
```

## 🔧 **Save Annotations Fix**

### **Current Issue:**
```tsx
const handleSave = () => {
  if (onSaveAnnotations) {
    onSaveAnnotations(annotations); // Only calls prop, doesn't save to DB
  }
};
```

### **Enhanced Save Functionality:**
```tsx
const handleSave = async () => {
  try {
    setSaving(true);
    
    // Option 1: If using the callback pattern
    if (onSaveAnnotations) {
      await onSaveAnnotations(annotations);
    }
    
    // Option 2: Direct API save (for standalone use)
    else {
      const headers = await getAuthHeaders();
      const response = await fetch('/api/quality/annotations/bulk-save', {
        method: 'POST',
        headers,
        body: JSON.stringify({
          annotations,
          fileUrl,
          fileName,
          qualityReviewId
        })
      });
      
      if (!response.ok) {
        throw new Error('Failed to save annotations');
      }
    }
    
    // Show success feedback
    setShowSuccessMessage(true);
    setTimeout(() => setShowSuccessMessage(false), 3000);
    
  } catch (error) {
    console.error('Error saving annotations:', error);
    setShowErrorMessage(true);
    setTimeout(() => setShowErrorMessage(false), 5000);
  } finally {
    setSaving(false);
  }
};
```

## 📊 **Database Schema Updates**

### **Enhanced file_annotations table:**
```sql
ALTER TABLE file_annotations 
ADD COLUMN quality_review_id UUID REFERENCES quality_reviews(id),
ADD COLUMN project_id UUID REFERENCES projects(id),
ADD COLUMN is_visible_to_designer BOOLEAN DEFAULT true;

-- Index for performance
CREATE INDEX idx_file_annotations_quality_review 
ON file_annotations(quality_review_id);

CREATE INDEX idx_file_annotations_project 
ON file_annotations(project_id);
```

## 🎯 **Expected Outcomes**

After implementation:

### **✅ Designers Will Be Able To:**
1. **View annotated images** in their project details
2. **See visual feedback** from quality team on specific areas
3. **Understand revision requests** with precise visual context
4. **Navigate from quality reviews** to annotated files
5. **Track annotation history** across project revisions

### **✅ Quality Team Benefits:**
1. **More effective communication** through visual feedback
2. **Reduced back-and-forth** with clearer guidance
3. **Better designer education** through visual examples
4. **Audit trail** of feedback provided

### **✅ Platform Benefits:**
1. **Improved designer satisfaction** through better feedback
2. **Faster revision cycles** with clearer requirements
3. **Higher quality deliverables** through better communication
4. **Reduced support burden** with self-service feedback viewing

## 📁 **Files to Modify**

### **New Files:**
- `src/app/api/quality/annotations/bulk-save/route.ts` - Bulk save endpoint
- `src/components/designer/AnnotatedFileViewer.tsx` - Designer-specific file viewer

### **Modified Files:**
- `src/app/designer/projects/[id]/page.tsx` - Add annotation viewing
- `src/app/designer/quality/page.tsx` - Add "View Files" buttons
- `src/components/quality/FileAnnotationTool.tsx` - Enhanced save functionality
- `migrations/enhance_file_annotations.sql` - Database updates

## 🚀 **Implementation Priority**

1. **HIGH**: Fix save annotations functionality (immediate impact)
2. **HIGH**: Add designer file viewing in project details (core feature)
3. **MEDIUM**: Enhance quality page with file links (nice to have)
4. **LOW**: Advanced features like annotation history (future enhancement)

This implementation will complete the quality feedback loop and provide designers with the visual context they need to make effective revisions.
# Designer Annotation Viewing Enhancement Plan

## 🎯 **Objective**
Enable designers to view their submitted images with quality team annotations after reviews are completed.

## 📋 **Current Gap Analysis**

### ❌ **What's Missing:**
1. **Designer Project View**: No image annotation viewing in `/designer/projects/[id]`
2. **Quality Review Integration**: No connection between quality reviews and annotated files
3. **File Accessibility**: Designers can't access quality-annotated versions of their files
4. **Visual Feedback**: Only text feedback shown, not visual markup

### ✅ **What Exists:**
1. **FileAnnotationTool Component**: Fully functional annotation system
2. **Annotation API**: Working CRUD operations for annotations
3. **Quality Review System**: Basic feedback display
4. **File Storage**: Cloudflare R2 integration

## 🔧 **Implementation Plan**

### **Phase 1: Designer Project File Viewing**
**File: `src/app/designer/projects/[id]/page.tsx`**

Add a new section to show submitted files with annotations:

```tsx
// Add to existing project detail page
<div className="space-y-6">
  {/* Existing project info */}
  
  {/* NEW: Submitted Files with Annotations */}
  <Card>
    <CardHeader>
      <CardTitle>Submitted Files & Quality Feedback</CardTitle>
    </CardHeader>
    <CardContent>
      {submittedFiles.map((file) => (
        <div key={file.id} className="border rounded-lg p-4 mb-4">
          <div className="flex justify-between items-center mb-3">
            <h4 className="font-medium">{file.name}</h4>
            <Badge variant={getQualityStatusVariant(file.quality_status)}>
              {file.quality_status}
            </Badge>
          </div>
          
          {file.annotations?.length > 0 && (
            <div>
              <p className="text-sm text-gray-600 mb-3">
                Quality team has provided visual feedback on this file:
              </p>
              <FileAnnotationTool
                fileUrl={file.url}
                fileName={file.name}
                fileType={file.type}
                existingAnnotations={file.annotations}
                readOnly={true} // Designers can only view, not edit
                reviewerName="Quality Team"
              />
            </div>
          )}
        </div>
      ))}
    </CardContent>
  </Card>
</div>
```

### **Phase 2: Quality Review File Association**
**File: `src/app/api/quality/annotations/route.ts`**

Enhance the API to link annotations with quality reviews:

```typescript
// Add quality_review_id to annotation creation
const annotation = await supabase
  .from('file_annotations')
  .insert({
    file_url: fileUrl,
    file_name: fileName,
    annotation_type: annotationType,
    x_position: xPosition,
    y_position: yPosition,
    title,
    description,
    reviewer_name: user.user_metadata?.full_name,
    quality_review_id: qualityReviewId, // NEW: Link to quality review
    created_at: new Date().toISOString()
  });
```

### **Phase 3: Enhanced Designer Quality Page**
**File: `src/app/designer/quality/page.tsx`**

Add "View Annotated Files" functionality:

```tsx
{review.status === 'needs_revision' && (
  <div className="flex gap-2 mt-3">
    <Button 
      variant="outline" 
      onClick={() => viewAnnotatedFiles(review.id)}
    >
      <FileImage className="h-4 w-4 mr-2" />
      View Annotated Files
    </Button>
    <Button 
      variant="default" 
      onClick={() => navigateToProject(review.project_id)}
    >
      Update Project
    </Button>
  </div>
)}
```

## 🔧 **Save Annotations Fix**

### **Current Issue:**
```tsx
const handleSave = () => {
  if (onSaveAnnotations) {
    onSaveAnnotations(annotations); // Only calls prop, doesn't save to DB
  }
};
```

### **Enhanced Save Functionality:**
```tsx
const handleSave = async () => {
  try {
    setSaving(true);
    
    // Option 1: If using the callback pattern
    if (onSaveAnnotations) {
      await onSaveAnnotations(annotations);
    }
    
    // Option 2: Direct API save (for standalone use)
    else {
      const headers = await getAuthHeaders();
      const response = await fetch('/api/quality/annotations/bulk-save', {
        method: 'POST',
        headers,
        body: JSON.stringify({
          annotations,
          fileUrl,
          fileName,
          qualityReviewId
        })
      });
      
      if (!response.ok) {
        throw new Error('Failed to save annotations');
      }
    }
    
    // Show success feedback
    setShowSuccessMessage(true);
    setTimeout(() => setShowSuccessMessage(false), 3000);
    
  } catch (error) {
    console.error('Error saving annotations:', error);
    setShowErrorMessage(true);
    setTimeout(() => setShowErrorMessage(false), 5000);
  } finally {
    setSaving(false);
  }
};
```

## 📊 **Database Schema Updates**

### **Enhanced file_annotations table:**
```sql
ALTER TABLE file_annotations 
ADD COLUMN quality_review_id UUID REFERENCES quality_reviews(id),
ADD COLUMN project_id UUID REFERENCES projects(id),
ADD COLUMN is_visible_to_designer BOOLEAN DEFAULT true;

-- Index for performance
CREATE INDEX idx_file_annotations_quality_review 
ON file_annotations(quality_review_id);

CREATE INDEX idx_file_annotations_project 
ON file_annotations(project_id);
```

## 🎯 **Expected Outcomes**

After implementation:

### **✅ Designers Will Be Able To:**
1. **View annotated images** in their project details
2. **See visual feedback** from quality team on specific areas
3. **Understand revision requests** with precise visual context
4. **Navigate from quality reviews** to annotated files
5. **Track annotation history** across project revisions

### **✅ Quality Team Benefits:**
1. **More effective communication** through visual feedback
2. **Reduced back-and-forth** with clearer guidance
3. **Better designer education** through visual examples
4. **Audit trail** of feedback provided

### **✅ Platform Benefits:**
1. **Improved designer satisfaction** through better feedback
2. **Faster revision cycles** with clearer requirements
3. **Higher quality deliverables** through better communication
4. **Reduced support burden** with self-service feedback viewing

## 📁 **Files to Modify**

### **New Files:**
- `src/app/api/quality/annotations/bulk-save/route.ts` - Bulk save endpoint
- `src/components/designer/AnnotatedFileViewer.tsx` - Designer-specific file viewer

### **Modified Files:**
- `src/app/designer/projects/[id]/page.tsx` - Add annotation viewing
- `src/app/designer/quality/page.tsx` - Add "View Files" buttons
- `src/components/quality/FileAnnotationTool.tsx` - Enhanced save functionality
- `migrations/enhance_file_annotations.sql` - Database updates

## 🚀 **Implementation Priority**

1. **HIGH**: Fix save annotations functionality (immediate impact)
2. **HIGH**: Add designer file viewing in project details (core feature)
3. **MEDIUM**: Enhance quality page with file links (nice to have)
4. **LOW**: Advanced features like annotation history (future enhancement)

This implementation will complete the quality feedback loop and provide designers with the visual context they need to make effective revisions.
# Designer Annotation Viewing Enhancement Plan

## 🎯 **Objective**
Enable designers to view their submitted images with quality team annotations after reviews are completed.

## 📋 **Current Gap Analysis**

### ❌ **What's Missing:**
1. **Designer Project View**: No image annotation viewing in `/designer/projects/[id]`
2. **Quality Review Integration**: No connection between quality reviews and annotated files
3. **File Accessibility**: Designers can't access quality-annotated versions of their files
4. **Visual Feedback**: Only text feedback shown, not visual markup

### ✅ **What Exists:**
1. **FileAnnotationTool Component**: Fully functional annotation system
2. **Annotation API**: Working CRUD operations for annotations
3. **Quality Review System**: Basic feedback display
4. **File Storage**: Cloudflare R2 integration

## 🔧 **Implementation Plan**

### **Phase 1: Designer Project File Viewing**
**File: `src/app/designer/projects/[id]/page.tsx`**

Add a new section to show submitted files with annotations:

```tsx
// Add to existing project detail page
<div className="space-y-6">
  {/* Existing project info */}
  
  {/* NEW: Submitted Files with Annotations */}
  <Card>
    <CardHeader>
      <CardTitle>Submitted Files & Quality Feedback</CardTitle>
    </CardHeader>
    <CardContent>
      {submittedFiles.map((file) => (
        <div key={file.id} className="border rounded-lg p-4 mb-4">
          <div className="flex justify-between items-center mb-3">
            <h4 className="font-medium">{file.name}</h4>
            <Badge variant={getQualityStatusVariant(file.quality_status)}>
              {file.quality_status}
            </Badge>
          </div>
          
          {file.annotations?.length > 0 && (
            <div>
              <p className="text-sm text-gray-600 mb-3">
                Quality team has provided visual feedback on this file:
              </p>
              <FileAnnotationTool
                fileUrl={file.url}
                fileName={file.name}
                fileType={file.type}
                existingAnnotations={file.annotations}
                readOnly={true} // Designers can only view, not edit
                reviewerName="Quality Team"
              />
            </div>
          )}
        </div>
      ))}
    </CardContent>
  </Card>
</div>
```

### **Phase 2: Quality Review File Association**
**File: `src/app/api/quality/annotations/route.ts`**

Enhance the API to link annotations with quality reviews:

```typescript
// Add quality_review_id to annotation creation
const annotation = await supabase
  .from('file_annotations')
  .insert({
    file_url: fileUrl,
    file_name: fileName,
    annotation_type: annotationType,
    x_position: xPosition,
    y_position: yPosition,
    title,
    description,
    reviewer_name: user.user_metadata?.full_name,
    quality_review_id: qualityReviewId, // NEW: Link to quality review
    created_at: new Date().toISOString()
  });
```

### **Phase 3: Enhanced Designer Quality Page**
**File: `src/app/designer/quality/page.tsx`**

Add "View Annotated Files" functionality:

```tsx
{review.status === 'needs_revision' && (
  <div className="flex gap-2 mt-3">
    <Button 
      variant="outline" 
      onClick={() => viewAnnotatedFiles(review.id)}
    >
      <FileImage className="h-4 w-4 mr-2" />
      View Annotated Files
    </Button>
    <Button 
      variant="default" 
      onClick={() => navigateToProject(review.project_id)}
    >
      Update Project
    </Button>
  </div>
)}
```

## 🔧 **Save Annotations Fix**

### **Current Issue:**
```tsx
const handleSave = () => {
  if (onSaveAnnotations) {
    onSaveAnnotations(annotations); // Only calls prop, doesn't save to DB
  }
};
```

### **Enhanced Save Functionality:**
```tsx
const handleSave = async () => {
  try {
    setSaving(true);
    
    // Option 1: If using the callback pattern
    if (onSaveAnnotations) {
      await onSaveAnnotations(annotations);
    }
    
    // Option 2: Direct API save (for standalone use)
    else {
      const headers = await getAuthHeaders();
      const response = await fetch('/api/quality/annotations/bulk-save', {
        method: 'POST',
        headers,
        body: JSON.stringify({
          annotations,
          fileUrl,
          fileName,
          qualityReviewId
        })
      });
      
      if (!response.ok) {
        throw new Error('Failed to save annotations');
      }
    }
    
    // Show success feedback
    setShowSuccessMessage(true);
    setTimeout(() => setShowSuccessMessage(false), 3000);
    
  } catch (error) {
    console.error('Error saving annotations:', error);
    setShowErrorMessage(true);
    setTimeout(() => setShowErrorMessage(false), 5000);
  } finally {
    setSaving(false);
  }
};
```

## 📊 **Database Schema Updates**

### **Enhanced file_annotations table:**
```sql
ALTER TABLE file_annotations 
ADD COLUMN quality_review_id UUID REFERENCES quality_reviews(id),
ADD COLUMN project_id UUID REFERENCES projects(id),
ADD COLUMN is_visible_to_designer BOOLEAN DEFAULT true;

-- Index for performance
CREATE INDEX idx_file_annotations_quality_review 
ON file_annotations(quality_review_id);

CREATE INDEX idx_file_annotations_project 
ON file_annotations(project_id);
```

## 🎯 **Expected Outcomes**

After implementation:

### **✅ Designers Will Be Able To:**
1. **View annotated images** in their project details
2. **See visual feedback** from quality team on specific areas
3. **Understand revision requests** with precise visual context
4. **Navigate from quality reviews** to annotated files
5. **Track annotation history** across project revisions

### **✅ Quality Team Benefits:**
1. **More effective communication** through visual feedback
2. **Reduced back-and-forth** with clearer guidance
3. **Better designer education** through visual examples
4. **Audit trail** of feedback provided

### **✅ Platform Benefits:**
1. **Improved designer satisfaction** through better feedback
2. **Faster revision cycles** with clearer requirements
3. **Higher quality deliverables** through better communication
4. **Reduced support burden** with self-service feedback viewing

## 📁 **Files to Modify**

### **New Files:**
- `src/app/api/quality/annotations/bulk-save/route.ts` - Bulk save endpoint
- `src/components/designer/AnnotatedFileViewer.tsx` - Designer-specific file viewer

### **Modified Files:**
- `src/app/designer/projects/[id]/page.tsx` - Add annotation viewing
- `src/app/designer/quality/page.tsx` - Add "View Files" buttons
- `src/components/quality/FileAnnotationTool.tsx` - Enhanced save functionality
- `migrations/enhance_file_annotations.sql` - Database updates

## 🚀 **Implementation Priority**

1. **HIGH**: Fix save annotations functionality (immediate impact)
2. **HIGH**: Add designer file viewing in project details (core feature)
3. **MEDIUM**: Enhance quality page with file links (nice to have)
4. **LOW**: Advanced features like annotation history (future enhancement)

This implementation will complete the quality feedback loop and provide designers with the visual context they need to make effective revisions.
# Designer Annotation Viewing Enhancement Plan

## 🎯 **Objective**
Enable designers to view their submitted images with quality team annotations after reviews are completed.

## 📋 **Current Gap Analysis**

### ❌ **What's Missing:**
1. **Designer Project View**: No image annotation viewing in `/designer/projects/[id]`
2. **Quality Review Integration**: No connection between quality reviews and annotated files
3. **File Accessibility**: Designers can't access quality-annotated versions of their files
4. **Visual Feedback**: Only text feedback shown, not visual markup

### ✅ **What Exists:**
1. **FileAnnotationTool Component**: Fully functional annotation system
2. **Annotation API**: Working CRUD operations for annotations
3. **Quality Review System**: Basic feedback display
4. **File Storage**: Cloudflare R2 integration

## 🔧 **Implementation Plan**

### **Phase 1: Designer Project File Viewing**
**File: `src/app/designer/projects/[id]/page.tsx`**

Add a new section to show submitted files with annotations:

```tsx
// Add to existing project detail page
<div className="space-y-6">
  {/* Existing project info */}
  
  {/* NEW: Submitted Files with Annotations */}
  <Card>
    <CardHeader>
      <CardTitle>Submitted Files & Quality Feedback</CardTitle>
    </CardHeader>
    <CardContent>
      {submittedFiles.map((file) => (
        <div key={file.id} className="border rounded-lg p-4 mb-4">
          <div className="flex justify-between items-center mb-3">
            <h4 className="font-medium">{file.name}</h4>
            <Badge variant={getQualityStatusVariant(file.quality_status)}>
              {file.quality_status}
            </Badge>
          </div>
          
          {file.annotations?.length > 0 && (
            <div>
              <p className="text-sm text-gray-600 mb-3">
                Quality team has provided visual feedback on this file:
              </p>
              <FileAnnotationTool
                fileUrl={file.url}
                fileName={file.name}
                fileType={file.type}
                existingAnnotations={file.annotations}
                readOnly={true} // Designers can only view, not edit
                reviewerName="Quality Team"
              />
            </div>
          )}
        </div>
      ))}
    </CardContent>
  </Card>
</div>
```

### **Phase 2: Quality Review File Association**
**File: `src/app/api/quality/annotations/route.ts`**

Enhance the API to link annotations with quality reviews:

```typescript
// Add quality_review_id to annotation creation
const annotation = await supabase
  .from('file_annotations')
  .insert({
    file_url: fileUrl,
    file_name: fileName,
    annotation_type: annotationType,
    x_position: xPosition,
    y_position: yPosition,
    title,
    description,
    reviewer_name: user.user_metadata?.full_name,
    quality_review_id: qualityReviewId, // NEW: Link to quality review
    created_at: new Date().toISOString()
  });
```

### **Phase 3: Enhanced Designer Quality Page**
**File: `src/app/designer/quality/page.tsx`**

Add "View Annotated Files" functionality:

```tsx
{review.status === 'needs_revision' && (
  <div className="flex gap-2 mt-3">
    <Button 
      variant="outline" 
      onClick={() => viewAnnotatedFiles(review.id)}
    >
      <FileImage className="h-4 w-4 mr-2" />
      View Annotated Files
    </Button>
    <Button 
      variant="default" 
      onClick={() => navigateToProject(review.project_id)}
    >
      Update Project
    </Button>
  </div>
)}
```

## 🔧 **Save Annotations Fix**

### **Current Issue:**
```tsx
const handleSave = () => {
  if (onSaveAnnotations) {
    onSaveAnnotations(annotations); // Only calls prop, doesn't save to DB
  }
};
```

### **Enhanced Save Functionality:**
```tsx
const handleSave = async () => {
  try {
    setSaving(true);
    
    // Option 1: If using the callback pattern
    if (onSaveAnnotations) {
      await onSaveAnnotations(annotations);
    }
    
    // Option 2: Direct API save (for standalone use)
    else {
      const headers = await getAuthHeaders();
      const response = await fetch('/api/quality/annotations/bulk-save', {
        method: 'POST',
        headers,
        body: JSON.stringify({
          annotations,
          fileUrl,
          fileName,
          qualityReviewId
        })
      });
      
      if (!response.ok) {
        throw new Error('Failed to save annotations');
      }
    }
    
    // Show success feedback
    setShowSuccessMessage(true);
    setTimeout(() => setShowSuccessMessage(false), 3000);
    
  } catch (error) {
    console.error('Error saving annotations:', error);
    setShowErrorMessage(true);
    setTimeout(() => setShowErrorMessage(false), 5000);
  } finally {
    setSaving(false);
  }
};
```

## 📊 **Database Schema Updates**

### **Enhanced file_annotations table:**
```sql
ALTER TABLE file_annotations 
ADD COLUMN quality_review_id UUID REFERENCES quality_reviews(id),
ADD COLUMN project_id UUID REFERENCES projects(id),
ADD COLUMN is_visible_to_designer BOOLEAN DEFAULT true;

-- Index for performance
CREATE INDEX idx_file_annotations_quality_review 
ON file_annotations(quality_review_id);

CREATE INDEX idx_file_annotations_project 
ON file_annotations(project_id);
```

## 🎯 **Expected Outcomes**

After implementation:

### **✅ Designers Will Be Able To:**
1. **View annotated images** in their project details
2. **See visual feedback** from quality team on specific areas
3. **Understand revision requests** with precise visual context
4. **Navigate from quality reviews** to annotated files
5. **Track annotation history** across project revisions

### **✅ Quality Team Benefits:**
1. **More effective communication** through visual feedback
2. **Reduced back-and-forth** with clearer guidance
3. **Better designer education** through visual examples
4. **Audit trail** of feedback provided

### **✅ Platform Benefits:**
1. **Improved designer satisfaction** through better feedback
2. **Faster revision cycles** with clearer requirements
3. **Higher quality deliverables** through better communication
4. **Reduced support burden** with self-service feedback viewing

## 📁 **Files to Modify**

### **New Files:**
- `src/app/api/quality/annotations/bulk-save/route.ts` - Bulk save endpoint
- `src/components/designer/AnnotatedFileViewer.tsx` - Designer-specific file viewer

### **Modified Files:**
- `src/app/designer/projects/[id]/page.tsx` - Add annotation viewing
- `src/app/designer/quality/page.tsx` - Add "View Files" buttons
- `src/components/quality/FileAnnotationTool.tsx` - Enhanced save functionality
- `migrations/enhance_file_annotations.sql` - Database updates

## 🚀 **Implementation Priority**

1. **HIGH**: Fix save annotations functionality (immediate impact)
2. **HIGH**: Add designer file viewing in project details (core feature)
3. **MEDIUM**: Enhance quality page with file links (nice to have)
4. **LOW**: Advanced features like annotation history (future enhancement)

This implementation will complete the quality feedback loop and provide designers with the visual context they need to make effective revisions.
# Designer Annotation Viewing Enhancement Plan

## 🎯 **Objective**
Enable designers to view their submitted images with quality team annotations after reviews are completed.

## 📋 **Current Gap Analysis**

### ❌ **What's Missing:**
1. **Designer Project View**: No image annotation viewing in `/designer/projects/[id]`
2. **Quality Review Integration**: No connection between quality reviews and annotated files
3. **File Accessibility**: Designers can't access quality-annotated versions of their files
4. **Visual Feedback**: Only text feedback shown, not visual markup

### ✅ **What Exists:**
1. **FileAnnotationTool Component**: Fully functional annotation system
2. **Annotation API**: Working CRUD operations for annotations
3. **Quality Review System**: Basic feedback display
4. **File Storage**: Cloudflare R2 integration

## 🔧 **Implementation Plan**

### **Phase 1: Designer Project File Viewing**
**File: `src/app/designer/projects/[id]/page.tsx`**

Add a new section to show submitted files with annotations:

```tsx
// Add to existing project detail page
<div className="space-y-6">
  {/* Existing project info */}
  
  {/* NEW: Submitted Files with Annotations */}
  <Card>
    <CardHeader>
      <CardTitle>Submitted Files & Quality Feedback</CardTitle>
    </CardHeader>
    <CardContent>
      {submittedFiles.map((file) => (
        <div key={file.id} className="border rounded-lg p-4 mb-4">
          <div className="flex justify-between items-center mb-3">
            <h4 className="font-medium">{file.name}</h4>
            <Badge variant={getQualityStatusVariant(file.quality_status)}>
              {file.quality_status}
            </Badge>
          </div>
          
          {file.annotations?.length > 0 && (
            <div>
              <p className="text-sm text-gray-600 mb-3">
                Quality team has provided visual feedback on this file:
              </p>
              <FileAnnotationTool
                fileUrl={file.url}
                fileName={file.name}
                fileType={file.type}
                existingAnnotations={file.annotations}
                readOnly={true} // Designers can only view, not edit
                reviewerName="Quality Team"
              />
            </div>
          )}
        </div>
      ))}
    </CardContent>
  </Card>
</div>
```

### **Phase 2: Quality Review File Association**
**File: `src/app/api/quality/annotations/route.ts`**

Enhance the API to link annotations with quality reviews:

```typescript
// Add quality_review_id to annotation creation
const annotation = await supabase
  .from('file_annotations')
  .insert({
    file_url: fileUrl,
    file_name: fileName,
    annotation_type: annotationType,
    x_position: xPosition,
    y_position: yPosition,
    title,
    description,
    reviewer_name: user.user_metadata?.full_name,
    quality_review_id: qualityReviewId, // NEW: Link to quality review
    created_at: new Date().toISOString()
  });
```

### **Phase 3: Enhanced Designer Quality Page**
**File: `src/app/designer/quality/page.tsx`**

Add "View Annotated Files" functionality:

```tsx
{review.status === 'needs_revision' && (
  <div className="flex gap-2 mt-3">
    <Button 
      variant="outline" 
      onClick={() => viewAnnotatedFiles(review.id)}
    >
      <FileImage className="h-4 w-4 mr-2" />
      View Annotated Files
    </Button>
    <Button 
      variant="default" 
      onClick={() => navigateToProject(review.project_id)}
    >
      Update Project
    </Button>
  </div>
)}
```

## 🔧 **Save Annotations Fix**

### **Current Issue:**
```tsx
const handleSave = () => {
  if (onSaveAnnotations) {
    onSaveAnnotations(annotations); // Only calls prop, doesn't save to DB
  }
};
```

### **Enhanced Save Functionality:**
```tsx
const handleSave = async () => {
  try {
    setSaving(true);
    
    // Option 1: If using the callback pattern
    if (onSaveAnnotations) {
      await onSaveAnnotations(annotations);
    }
    
    // Option 2: Direct API save (for standalone use)
    else {
      const headers = await getAuthHeaders();
      const response = await fetch('/api/quality/annotations/bulk-save', {
        method: 'POST',
        headers,
        body: JSON.stringify({
          annotations,
          fileUrl,
          fileName,
          qualityReviewId
        })
      });
      
      if (!response.ok) {
        throw new Error('Failed to save annotations');
      }
    }
    
    // Show success feedback
    setShowSuccessMessage(true);
    setTimeout(() => setShowSuccessMessage(false), 3000);
    
  } catch (error) {
    console.error('Error saving annotations:', error);
    setShowErrorMessage(true);
    setTimeout(() => setShowErrorMessage(false), 5000);
  } finally {
    setSaving(false);
  }
};
```

## 📊 **Database Schema Updates**

### **Enhanced file_annotations table:**
```sql
ALTER TABLE file_annotations 
ADD COLUMN quality_review_id UUID REFERENCES quality_reviews(id),
ADD COLUMN project_id UUID REFERENCES projects(id),
ADD COLUMN is_visible_to_designer BOOLEAN DEFAULT true;

-- Index for performance
CREATE INDEX idx_file_annotations_quality_review 
ON file_annotations(quality_review_id);

CREATE INDEX idx_file_annotations_project 
ON file_annotations(project_id);
```

## 🎯 **Expected Outcomes**

After implementation:

### **✅ Designers Will Be Able To:**
1. **View annotated images** in their project details
2. **See visual feedback** from quality team on specific areas
3. **Understand revision requests** with precise visual context
4. **Navigate from quality reviews** to annotated files
5. **Track annotation history** across project revisions

### **✅ Quality Team Benefits:**
1. **More effective communication** through visual feedback
2. **Reduced back-and-forth** with clearer guidance
3. **Better designer education** through visual examples
4. **Audit trail** of feedback provided

### **✅ Platform Benefits:**
1. **Improved designer satisfaction** through better feedback
2. **Faster revision cycles** with clearer requirements
3. **Higher quality deliverables** through better communication
4. **Reduced support burden** with self-service feedback viewing

## 📁 **Files to Modify**

### **New Files:**
- `src/app/api/quality/annotations/bulk-save/route.ts` - Bulk save endpoint
- `src/components/designer/AnnotatedFileViewer.tsx` - Designer-specific file viewer

### **Modified Files:**
- `src/app/designer/projects/[id]/page.tsx` - Add annotation viewing
- `src/app/designer/quality/page.tsx` - Add "View Files" buttons
- `src/components/quality/FileAnnotationTool.tsx` - Enhanced save functionality
- `migrations/enhance_file_annotations.sql` - Database updates

## 🚀 **Implementation Priority**

1. **HIGH**: Fix save annotations functionality (immediate impact)
2. **HIGH**: Add designer file viewing in project details (core feature)
3. **MEDIUM**: Enhance quality page with file links (nice to have)
4. **LOW**: Advanced features like annotation history (future enhancement)

This implementation will complete the quality feedback loop and provide designers with the visual context they need to make effective revisions.
# Designer Annotation Viewing Enhancement Plan

## 🎯 **Objective**
Enable designers to view their submitted images with quality team annotations after reviews are completed.

## 📋 **Current Gap Analysis**

### ❌ **What's Missing:**
1. **Designer Project View**: No image annotation viewing in `/designer/projects/[id]`
2. **Quality Review Integration**: No connection between quality reviews and annotated files
3. **File Accessibility**: Designers can't access quality-annotated versions of their files
4. **Visual Feedback**: Only text feedback shown, not visual markup

### ✅ **What Exists:**
1. **FileAnnotationTool Component**: Fully functional annotation system
2. **Annotation API**: Working CRUD operations for annotations
3. **Quality Review System**: Basic feedback display
4. **File Storage**: Cloudflare R2 integration

## 🔧 **Implementation Plan**

### **Phase 1: Designer Project File Viewing**
**File: `src/app/designer/projects/[id]/page.tsx`**

Add a new section to show submitted files with annotations:

```tsx
// Add to existing project detail page
<div className="space-y-6">
  {/* Existing project info */}
  
  {/* NEW: Submitted Files with Annotations */}
  <Card>
    <CardHeader>
      <CardTitle>Submitted Files & Quality Feedback</CardTitle>
    </CardHeader>
    <CardContent>
      {submittedFiles.map((file) => (
        <div key={file.id} className="border rounded-lg p-4 mb-4">
          <div className="flex justify-between items-center mb-3">
            <h4 className="font-medium">{file.name}</h4>
            <Badge variant={getQualityStatusVariant(file.quality_status)}>
              {file.quality_status}
            </Badge>
          </div>
          
          {file.annotations?.length > 0 && (
            <div>
              <p className="text-sm text-gray-600 mb-3">
                Quality team has provided visual feedback on this file:
              </p>
              <FileAnnotationTool
                fileUrl={file.url}
                fileName={file.name}
                fileType={file.type}
                existingAnnotations={file.annotations}
                readOnly={true} // Designers can only view, not edit
                reviewerName="Quality Team"
              />
            </div>
          )}
        </div>
      ))}
    </CardContent>
  </Card>
</div>
```

### **Phase 2: Quality Review File Association**
**File: `src/app/api/quality/annotations/route.ts`**

Enhance the API to link annotations with quality reviews:

```typescript
// Add quality_review_id to annotation creation
const annotation = await supabase
  .from('file_annotations')
  .insert({
    file_url: fileUrl,
    file_name: fileName,
    annotation_type: annotationType,
    x_position: xPosition,
    y_position: yPosition,
    title,
    description,
    reviewer_name: user.user_metadata?.full_name,
    quality_review_id: qualityReviewId, // NEW: Link to quality review
    created_at: new Date().toISOString()
  });
```

### **Phase 3: Enhanced Designer Quality Page**
**File: `src/app/designer/quality/page.tsx`**

Add "View Annotated Files" functionality:

```tsx
{review.status === 'needs_revision' && (
  <div className="flex gap-2 mt-3">
    <Button 
      variant="outline" 
      onClick={() => viewAnnotatedFiles(review.id)}
    >
      <FileImage className="h-4 w-4 mr-2" />
      View Annotated Files
    </Button>
    <Button 
      variant="default" 
      onClick={() => navigateToProject(review.project_id)}
    >
      Update Project
    </Button>
  </div>
)}
```

## 🔧 **Save Annotations Fix**

### **Current Issue:**
```tsx
const handleSave = () => {
  if (onSaveAnnotations) {
    onSaveAnnotations(annotations); // Only calls prop, doesn't save to DB
  }
};
```

### **Enhanced Save Functionality:**
```tsx
const handleSave = async () => {
  try {
    setSaving(true);
    
    // Option 1: If using the callback pattern
    if (onSaveAnnotations) {
      await onSaveAnnotations(annotations);
    }
    
    // Option 2: Direct API save (for standalone use)
    else {
      const headers = await getAuthHeaders();
      const response = await fetch('/api/quality/annotations/bulk-save', {
        method: 'POST',
        headers,
        body: JSON.stringify({
          annotations,
          fileUrl,
          fileName,
          qualityReviewId
        })
      });
      
      if (!response.ok) {
        throw new Error('Failed to save annotations');
      }
    }
    
    // Show success feedback
    setShowSuccessMessage(true);
    setTimeout(() => setShowSuccessMessage(false), 3000);
    
  } catch (error) {
    console.error('Error saving annotations:', error);
    setShowErrorMessage(true);
    setTimeout(() => setShowErrorMessage(false), 5000);
  } finally {
    setSaving(false);
  }
};
```

## 📊 **Database Schema Updates**

### **Enhanced file_annotations table:**
```sql
ALTER TABLE file_annotations 
ADD COLUMN quality_review_id UUID REFERENCES quality_reviews(id),
ADD COLUMN project_id UUID REFERENCES projects(id),
ADD COLUMN is_visible_to_designer BOOLEAN DEFAULT true;

-- Index for performance
CREATE INDEX idx_file_annotations_quality_review 
ON file_annotations(quality_review_id);

CREATE INDEX idx_file_annotations_project 
ON file_annotations(project_id);
```

## 🎯 **Expected Outcomes**

After implementation:

### **✅ Designers Will Be Able To:**
1. **View annotated images** in their project details
2. **See visual feedback** from quality team on specific areas
3. **Understand revision requests** with precise visual context
4. **Navigate from quality reviews** to annotated files
5. **Track annotation history** across project revisions

### **✅ Quality Team Benefits:**
1. **More effective communication** through visual feedback
2. **Reduced back-and-forth** with clearer guidance
3. **Better designer education** through visual examples
4. **Audit trail** of feedback provided

### **✅ Platform Benefits:**
1. **Improved designer satisfaction** through better feedback
2. **Faster revision cycles** with clearer requirements
3. **Higher quality deliverables** through better communication
4. **Reduced support burden** with self-service feedback viewing

## 📁 **Files to Modify**

### **New Files:**
- `src/app/api/quality/annotations/bulk-save/route.ts` - Bulk save endpoint
- `src/components/designer/AnnotatedFileViewer.tsx` - Designer-specific file viewer

### **Modified Files:**
- `src/app/designer/projects/[id]/page.tsx` - Add annotation viewing
- `src/app/designer/quality/page.tsx` - Add "View Files" buttons
- `src/components/quality/FileAnnotationTool.tsx` - Enhanced save functionality
- `migrations/enhance_file_annotations.sql` - Database updates

## 🚀 **Implementation Priority**

1. **HIGH**: Fix save annotations functionality (immediate impact)
2. **HIGH**: Add designer file viewing in project details (core feature)
3. **MEDIUM**: Enhance quality page with file links (nice to have)
4. **LOW**: Advanced features like annotation history (future enhancement)

This implementation will complete the quality feedback loop and provide designers with the visual context they need to make effective revisions.
# Designer Annotation Viewing Enhancement Plan

## 🎯 **Objective**
Enable designers to view their submitted images with quality team annotations after reviews are completed.

## 📋 **Current Gap Analysis**

### ❌ **What's Missing:**
1. **Designer Project View**: No image annotation viewing in `/designer/projects/[id]`
2. **Quality Review Integration**: No connection between quality reviews and annotated files
3. **File Accessibility**: Designers can't access quality-annotated versions of their files
4. **Visual Feedback**: Only text feedback shown, not visual markup

### ✅ **What Exists:**
1. **FileAnnotationTool Component**: Fully functional annotation system
2. **Annotation API**: Working CRUD operations for annotations
3. **Quality Review System**: Basic feedback display
4. **File Storage**: Cloudflare R2 integration

## 🔧 **Implementation Plan**

### **Phase 1: Designer Project File Viewing**
**File: `src/app/designer/projects/[id]/page.tsx`**

Add a new section to show submitted files with annotations:

```tsx
// Add to existing project detail page
<div className="space-y-6">
  {/* Existing project info */}
  
  {/* NEW: Submitted Files with Annotations */}
  <Card>
    <CardHeader>
      <CardTitle>Submitted Files & Quality Feedback</CardTitle>
    </CardHeader>
    <CardContent>
      {submittedFiles.map((file) => (
        <div key={file.id} className="border rounded-lg p-4 mb-4">
          <div className="flex justify-between items-center mb-3">
            <h4 className="font-medium">{file.name}</h4>
            <Badge variant={getQualityStatusVariant(file.quality_status)}>
              {file.quality_status}
            </Badge>
          </div>
          
          {file.annotations?.length > 0 && (
            <div>
              <p className="text-sm text-gray-600 mb-3">
                Quality team has provided visual feedback on this file:
              </p>
              <FileAnnotationTool
                fileUrl={file.url}
                fileName={file.name}
                fileType={file.type}
                existingAnnotations={file.annotations}
                readOnly={true} // Designers can only view, not edit
                reviewerName="Quality Team"
              />
            </div>
          )}
        </div>
      ))}
    </CardContent>
  </Card>
</div>
```

### **Phase 2: Quality Review File Association**
**File: `src/app/api/quality/annotations/route.ts`**

Enhance the API to link annotations with quality reviews:

```typescript
// Add quality_review_id to annotation creation
const annotation = await supabase
  .from('file_annotations')
  .insert({
    file_url: fileUrl,
    file_name: fileName,
    annotation_type: annotationType,
    x_position: xPosition,
    y_position: yPosition,
    title,
    description,
    reviewer_name: user.user_metadata?.full_name,
    quality_review_id: qualityReviewId, // NEW: Link to quality review
    created_at: new Date().toISOString()
  });
```

### **Phase 3: Enhanced Designer Quality Page**
**File: `src/app/designer/quality/page.tsx`**

Add "View Annotated Files" functionality:

```tsx
{review.status === 'needs_revision' && (
  <div className="flex gap-2 mt-3">
    <Button 
      variant="outline" 
      onClick={() => viewAnnotatedFiles(review.id)}
    >
      <FileImage className="h-4 w-4 mr-2" />
      View Annotated Files
    </Button>
    <Button 
      variant="default" 
      onClick={() => navigateToProject(review.project_id)}
    >
      Update Project
    </Button>
  </div>
)}
```

## 🔧 **Save Annotations Fix**

### **Current Issue:**
```tsx
const handleSave = () => {
  if (onSaveAnnotations) {
    onSaveAnnotations(annotations); // Only calls prop, doesn't save to DB
  }
};
```

### **Enhanced Save Functionality:**
```tsx
const handleSave = async () => {
  try {
    setSaving(true);
    
    // Option 1: If using the callback pattern
    if (onSaveAnnotations) {
      await onSaveAnnotations(annotations);
    }
    
    // Option 2: Direct API save (for standalone use)
    else {
      const headers = await getAuthHeaders();
      const response = await fetch('/api/quality/annotations/bulk-save', {
        method: 'POST',
        headers,
        body: JSON.stringify({
          annotations,
          fileUrl,
          fileName,
          qualityReviewId
        })
      });
      
      if (!response.ok) {
        throw new Error('Failed to save annotations');
      }
    }
    
    // Show success feedback
    setShowSuccessMessage(true);
    setTimeout(() => setShowSuccessMessage(false), 3000);
    
  } catch (error) {
    console.error('Error saving annotations:', error);
    setShowErrorMessage(true);
    setTimeout(() => setShowErrorMessage(false), 5000);
  } finally {
    setSaving(false);
  }
};
```

## 📊 **Database Schema Updates**

### **Enhanced file_annotations table:**
```sql
ALTER TABLE file_annotations 
ADD COLUMN quality_review_id UUID REFERENCES quality_reviews(id),
ADD COLUMN project_id UUID REFERENCES projects(id),
ADD COLUMN is_visible_to_designer BOOLEAN DEFAULT true;

-- Index for performance
CREATE INDEX idx_file_annotations_quality_review 
ON file_annotations(quality_review_id);

CREATE INDEX idx_file_annotations_project 
ON file_annotations(project_id);
```

## 🎯 **Expected Outcomes**

After implementation:

### **✅ Designers Will Be Able To:**
1. **View annotated images** in their project details
2. **See visual feedback** from quality team on specific areas
3. **Understand revision requests** with precise visual context
4. **Navigate from quality reviews** to annotated files
5. **Track annotation history** across project revisions

### **✅ Quality Team Benefits:**
1. **More effective communication** through visual feedback
2. **Reduced back-and-forth** with clearer guidance
3. **Better designer education** through visual examples
4. **Audit trail** of feedback provided

### **✅ Platform Benefits:**
1. **Improved designer satisfaction** through better feedback
2. **Faster revision cycles** with clearer requirements
3. **Higher quality deliverables** through better communication
4. **Reduced support burden** with self-service feedback viewing

## 📁 **Files to Modify**

### **New Files:**
- `src/app/api/quality/annotations/bulk-save/route.ts` - Bulk save endpoint
- `src/components/designer/AnnotatedFileViewer.tsx` - Designer-specific file viewer

### **Modified Files:**
- `src/app/designer/projects/[id]/page.tsx` - Add annotation viewing
- `src/app/designer/quality/page.tsx` - Add "View Files" buttons
- `src/components/quality/FileAnnotationTool.tsx` - Enhanced save functionality
- `migrations/enhance_file_annotations.sql` - Database updates

## 🚀 **Implementation Priority**

1. **HIGH**: Fix save annotations functionality (immediate impact)
2. **HIGH**: Add designer file viewing in project details (core feature)
3. **MEDIUM**: Enhance quality page with file links (nice to have)
4. **LOW**: Advanced features like annotation history (future enhancement)

This implementation will complete the quality feedback loop and provide designers with the visual context they need to make effective revisions.
# Designer Annotation Viewing Enhancement Plan

## 🎯 **Objective**
Enable designers to view their submitted images with quality team annotations after reviews are completed.

## 📋 **Current Gap Analysis**

### ❌ **What's Missing:**
1. **Designer Project View**: No image annotation viewing in `/designer/projects/[id]`
2. **Quality Review Integration**: No connection between quality reviews and annotated files
3. **File Accessibility**: Designers can't access quality-annotated versions of their files
4. **Visual Feedback**: Only text feedback shown, not visual markup

### ✅ **What Exists:**
1. **FileAnnotationTool Component**: Fully functional annotation system
2. **Annotation API**: Working CRUD operations for annotations
3. **Quality Review System**: Basic feedback display
4. **File Storage**: Cloudflare R2 integration

## 🔧 **Implementation Plan**

### **Phase 1: Designer Project File Viewing**
**File: `src/app/designer/projects/[id]/page.tsx`**

Add a new section to show submitted files with annotations:

```tsx
// Add to existing project detail page
<div className="space-y-6">
  {/* Existing project info */}
  
  {/* NEW: Submitted Files with Annotations */}
  <Card>
    <CardHeader>
      <CardTitle>Submitted Files & Quality Feedback</CardTitle>
    </CardHeader>
    <CardContent>
      {submittedFiles.map((file) => (
        <div key={file.id} className="border rounded-lg p-4 mb-4">
          <div className="flex justify-between items-center mb-3">
            <h4 className="font-medium">{file.name}</h4>
            <Badge variant={getQualityStatusVariant(file.quality_status)}>
              {file.quality_status}
            </Badge>
          </div>
          
          {file.annotations?.length > 0 && (
            <div>
              <p className="text-sm text-gray-600 mb-3">
                Quality team has provided visual feedback on this file:
              </p>
              <FileAnnotationTool
                fileUrl={file.url}
                fileName={file.name}
                fileType={file.type}
                existingAnnotations={file.annotations}
                readOnly={true} // Designers can only view, not edit
                reviewerName="Quality Team"
              />
            </div>
          )}
        </div>
      ))}
    </CardContent>
  </Card>
</div>
```

### **Phase 2: Quality Review File Association**
**File: `src/app/api/quality/annotations/route.ts`**

Enhance the API to link annotations with quality reviews:

```typescript
// Add quality_review_id to annotation creation
const annotation = await supabase
  .from('file_annotations')
  .insert({
    file_url: fileUrl,
    file_name: fileName,
    annotation_type: annotationType,
    x_position: xPosition,
    y_position: yPosition,
    title,
    description,
    reviewer_name: user.user_metadata?.full_name,
    quality_review_id: qualityReviewId, // NEW: Link to quality review
    created_at: new Date().toISOString()
  });
```

### **Phase 3: Enhanced Designer Quality Page**
**File: `src/app/designer/quality/page.tsx`**

Add "View Annotated Files" functionality:

```tsx
{review.status === 'needs_revision' && (
  <div className="flex gap-2 mt-3">
    <Button 
      variant="outline" 
      onClick={() => viewAnnotatedFiles(review.id)}
    >
      <FileImage className="h-4 w-4 mr-2" />
      View Annotated Files
    </Button>
    <Button 
      variant="default" 
      onClick={() => navigateToProject(review.project_id)}
    >
      Update Project
    </Button>
  </div>
)}
```

## 🔧 **Save Annotations Fix**

### **Current Issue:**
```tsx
const handleSave = () => {
  if (onSaveAnnotations) {
    onSaveAnnotations(annotations); // Only calls prop, doesn't save to DB
  }
};
```

### **Enhanced Save Functionality:**
```tsx
const handleSave = async () => {
  try {
    setSaving(true);
    
    // Option 1: If using the callback pattern
    if (onSaveAnnotations) {
      await onSaveAnnotations(annotations);
    }
    
    // Option 2: Direct API save (for standalone use)
    else {
      const headers = await getAuthHeaders();
      const response = await fetch('/api/quality/annotations/bulk-save', {
        method: 'POST',
        headers,
        body: JSON.stringify({
          annotations,
          fileUrl,
          fileName,
          qualityReviewId
        })
      });
      
      if (!response.ok) {
        throw new Error('Failed to save annotations');
      }
    }
    
    // Show success feedback
    setShowSuccessMessage(true);
    setTimeout(() => setShowSuccessMessage(false), 3000);
    
  } catch (error) {
    console.error('Error saving annotations:', error);
    setShowErrorMessage(true);
    setTimeout(() => setShowErrorMessage(false), 5000);
  } finally {
    setSaving(false);
  }
};
```

## 📊 **Database Schema Updates**

### **Enhanced file_annotations table:**
```sql
ALTER TABLE file_annotations 
ADD COLUMN quality_review_id UUID REFERENCES quality_reviews(id),
ADD COLUMN project_id UUID REFERENCES projects(id),
ADD COLUMN is_visible_to_designer BOOLEAN DEFAULT true;

-- Index for performance
CREATE INDEX idx_file_annotations_quality_review 
ON file_annotations(quality_review_id);

CREATE INDEX idx_file_annotations_project 
ON file_annotations(project_id);
```

## 🎯 **Expected Outcomes**

After implementation:

### **✅ Designers Will Be Able To:**
1. **View annotated images** in their project details
2. **See visual feedback** from quality team on specific areas
3. **Understand revision requests** with precise visual context
4. **Navigate from quality reviews** to annotated files
5. **Track annotation history** across project revisions

### **✅ Quality Team Benefits:**
1. **More effective communication** through visual feedback
2. **Reduced back-and-forth** with clearer guidance
3. **Better designer education** through visual examples
4. **Audit trail** of feedback provided

### **✅ Platform Benefits:**
1. **Improved designer satisfaction** through better feedback
2. **Faster revision cycles** with clearer requirements
3. **Higher quality deliverables** through better communication
4. **Reduced support burden** with self-service feedback viewing

## 📁 **Files to Modify**

### **New Files:**
- `src/app/api/quality/annotations/bulk-save/route.ts` - Bulk save endpoint
- `src/components/designer/AnnotatedFileViewer.tsx` - Designer-specific file viewer

### **Modified Files:**
- `src/app/designer/projects/[id]/page.tsx` - Add annotation viewing
- `src/app/designer/quality/page.tsx` - Add "View Files" buttons
- `src/components/quality/FileAnnotationTool.tsx` - Enhanced save functionality
- `migrations/enhance_file_annotations.sql` - Database updates

## 🚀 **Implementation Priority**

1. **HIGH**: Fix save annotations functionality (immediate impact)
2. **HIGH**: Add designer file viewing in project details (core feature)
3. **MEDIUM**: Enhance quality page with file links (nice to have)
4. **LOW**: Advanced features like annotation history (future enhancement)

This implementation will complete the quality feedback loop and provide designers with the visual context they need to make effective revisions.
# Designer Annotation Viewing Enhancement Plan

## 🎯 **Objective**
Enable designers to view their submitted images with quality team annotations after reviews are completed.

## 📋 **Current Gap Analysis**

### ❌ **What's Missing:**
1. **Designer Project View**: No image annotation viewing in `/designer/projects/[id]`
2. **Quality Review Integration**: No connection between quality reviews and annotated files
3. **File Accessibility**: Designers can't access quality-annotated versions of their files
4. **Visual Feedback**: Only text feedback shown, not visual markup

### ✅ **What Exists:**
1. **FileAnnotationTool Component**: Fully functional annotation system
2. **Annotation API**: Working CRUD operations for annotations
3. **Quality Review System**: Basic feedback display
4. **File Storage**: Cloudflare R2 integration

## 🔧 **Implementation Plan**

### **Phase 1: Designer Project File Viewing**
**File: `src/app/designer/projects/[id]/page.tsx`**

Add a new section to show submitted files with annotations:

```tsx
// Add to existing project detail page
<div className="space-y-6">
  {/* Existing project info */}
  
  {/* NEW: Submitted Files with Annotations */}
  <Card>
    <CardHeader>
      <CardTitle>Submitted Files & Quality Feedback</CardTitle>
    </CardHeader>
    <CardContent>
      {submittedFiles.map((file) => (
        <div key={file.id} className="border rounded-lg p-4 mb-4">
          <div className="flex justify-between items-center mb-3">
            <h4 className="font-medium">{file.name}</h4>
            <Badge variant={getQualityStatusVariant(file.quality_status)}>
              {file.quality_status}
            </Badge>
          </div>
          
          {file.annotations?.length > 0 && (
            <div>
              <p className="text-sm text-gray-600 mb-3">
                Quality team has provided visual feedback on this file:
              </p>
              <FileAnnotationTool
                fileUrl={file.url}
                fileName={file.name}
                fileType={file.type}
                existingAnnotations={file.annotations}
                readOnly={true} // Designers can only view, not edit
                reviewerName="Quality Team"
              />
            </div>
          )}
        </div>
      ))}
    </CardContent>
  </Card>
</div>
```

### **Phase 2: Quality Review File Association**
**File: `src/app/api/quality/annotations/route.ts`**

Enhance the API to link annotations with quality reviews:

```typescript
// Add quality_review_id to annotation creation
const annotation = await supabase
  .from('file_annotations')
  .insert({
    file_url: fileUrl,
    file_name: fileName,
    annotation_type: annotationType,
    x_position: xPosition,
    y_position: yPosition,
    title,
    description,
    reviewer_name: user.user_metadata?.full_name,
    quality_review_id: qualityReviewId, // NEW: Link to quality review
    created_at: new Date().toISOString()
  });
```

### **Phase 3: Enhanced Designer Quality Page**
**File: `src/app/designer/quality/page.tsx`**

Add "View Annotated Files" functionality:

```tsx
{review.status === 'needs_revision' && (
  <div className="flex gap-2 mt-3">
    <Button 
      variant="outline" 
      onClick={() => viewAnnotatedFiles(review.id)}
    >
      <FileImage className="h-4 w-4 mr-2" />
      View Annotated Files
    </Button>
    <Button 
      variant="default" 
      onClick={() => navigateToProject(review.project_id)}
    >
      Update Project
    </Button>
  </div>
)}
```

## 🔧 **Save Annotations Fix**

### **Current Issue:**
```tsx
const handleSave = () => {
  if (onSaveAnnotations) {
    onSaveAnnotations(annotations); // Only calls prop, doesn't save to DB
  }
};
```

### **Enhanced Save Functionality:**
```tsx
const handleSave = async () => {
  try {
    setSaving(true);
    
    // Option 1: If using the callback pattern
    if (onSaveAnnotations) {
      await onSaveAnnotations(annotations);
    }
    
    // Option 2: Direct API save (for standalone use)
    else {
      const headers = await getAuthHeaders();
      const response = await fetch('/api/quality/annotations/bulk-save', {
        method: 'POST',
        headers,
        body: JSON.stringify({
          annotations,
          fileUrl,
          fileName,
          qualityReviewId
        })
      });
      
      if (!response.ok) {
        throw new Error('Failed to save annotations');
      }
    }
    
    // Show success feedback
    setShowSuccessMessage(true);
    setTimeout(() => setShowSuccessMessage(false), 3000);
    
  } catch (error) {
    console.error('Error saving annotations:', error);
    setShowErrorMessage(true);
    setTimeout(() => setShowErrorMessage(false), 5000);
  } finally {
    setSaving(false);
  }
};
```

## 📊 **Database Schema Updates**

### **Enhanced file_annotations table:**
```sql
ALTER TABLE file_annotations 
ADD COLUMN quality_review_id UUID REFERENCES quality_reviews(id),
ADD COLUMN project_id UUID REFERENCES projects(id),
ADD COLUMN is_visible_to_designer BOOLEAN DEFAULT true;

-- Index for performance
CREATE INDEX idx_file_annotations_quality_review 
ON file_annotations(quality_review_id);

CREATE INDEX idx_file_annotations_project 
ON file_annotations(project_id);
```

## 🎯 **Expected Outcomes**

After implementation:

### **✅ Designers Will Be Able To:**
1. **View annotated images** in their project details
2. **See visual feedback** from quality team on specific areas
3. **Understand revision requests** with precise visual context
4. **Navigate from quality reviews** to annotated files
5. **Track annotation history** across project revisions

### **✅ Quality Team Benefits:**
1. **More effective communication** through visual feedback
2. **Reduced back-and-forth** with clearer guidance
3. **Better designer education** through visual examples
4. **Audit trail** of feedback provided

### **✅ Platform Benefits:**
1. **Improved designer satisfaction** through better feedback
2. **Faster revision cycles** with clearer requirements
3. **Higher quality deliverables** through better communication
4. **Reduced support burden** with self-service feedback viewing

## 📁 **Files to Modify**

### **New Files:**
- `src/app/api/quality/annotations/bulk-save/route.ts` - Bulk save endpoint
- `src/components/designer/AnnotatedFileViewer.tsx` - Designer-specific file viewer

### **Modified Files:**
- `src/app/designer/projects/[id]/page.tsx` - Add annotation viewing
- `src/app/designer/quality/page.tsx` - Add "View Files" buttons
- `src/components/quality/FileAnnotationTool.tsx` - Enhanced save functionality
- `migrations/enhance_file_annotations.sql` - Database updates

## 🚀 **Implementation Priority**

1. **HIGH**: Fix save annotations functionality (immediate impact)
2. **HIGH**: Add designer file viewing in project details (core feature)
3. **MEDIUM**: Enhance quality page with file links (nice to have)
4. **LOW**: Advanced features like annotation history (future enhancement)

This implementation will complete the quality feedback loop and provide designers with the visual context they need to make effective revisions.
# Designer Annotation Viewing Enhancement Plan

## 🎯 **Objective**
Enable designers to view their submitted images with quality team annotations after reviews are completed.

## 📋 **Current Gap Analysis**

### ❌ **What's Missing:**
1. **Designer Project View**: No image annotation viewing in `/designer/projects/[id]`
2. **Quality Review Integration**: No connection between quality reviews and annotated files
3. **File Accessibility**: Designers can't access quality-annotated versions of their files
4. **Visual Feedback**: Only text feedback shown, not visual markup

### ✅ **What Exists:**
1. **FileAnnotationTool Component**: Fully functional annotation system
2. **Annotation API**: Working CRUD operations for annotations
3. **Quality Review System**: Basic feedback display
4. **File Storage**: Cloudflare R2 integration

## 🔧 **Implementation Plan**

### **Phase 1: Designer Project File Viewing**
**File: `src/app/designer/projects/[id]/page.tsx`**

Add a new section to show submitted files with annotations:

```tsx
// Add to existing project detail page
<div className="space-y-6">
  {/* Existing project info */}
  
  {/* NEW: Submitted Files with Annotations */}
  <Card>
    <CardHeader>
      <CardTitle>Submitted Files & Quality Feedback</CardTitle>
    </CardHeader>
    <CardContent>
      {submittedFiles.map((file) => (
        <div key={file.id} className="border rounded-lg p-4 mb-4">
          <div className="flex justify-between items-center mb-3">
            <h4 className="font-medium">{file.name}</h4>
            <Badge variant={getQualityStatusVariant(file.quality_status)}>
              {file.quality_status}
            </Badge>
          </div>
          
          {file.annotations?.length > 0 && (
            <div>
              <p className="text-sm text-gray-600 mb-3">
                Quality team has provided visual feedback on this file:
              </p>
              <FileAnnotationTool
                fileUrl={file.url}
                fileName={file.name}
                fileType={file.type}
                existingAnnotations={file.annotations}
                readOnly={true} // Designers can only view, not edit
                reviewerName="Quality Team"
              />
            </div>
          )}
        </div>
      ))}
    </CardContent>
  </Card>
</div>
```

### **Phase 2: Quality Review File Association**
**File: `src/app/api/quality/annotations/route.ts`**

Enhance the API to link annotations with quality reviews:

```typescript
// Add quality_review_id to annotation creation
const annotation = await supabase
  .from('file_annotations')
  .insert({
    file_url: fileUrl,
    file_name: fileName,
    annotation_type: annotationType,
    x_position: xPosition,
    y_position: yPosition,
    title,
    description,
    reviewer_name: user.user_metadata?.full_name,
    quality_review_id: qualityReviewId, // NEW: Link to quality review
    created_at: new Date().toISOString()
  });
```

### **Phase 3: Enhanced Designer Quality Page**
**File: `src/app/designer/quality/page.tsx`**

Add "View Annotated Files" functionality:

```tsx
{review.status === 'needs_revision' && (
  <div className="flex gap-2 mt-3">
    <Button 
      variant="outline" 
      onClick={() => viewAnnotatedFiles(review.id)}
    >
      <FileImage className="h-4 w-4 mr-2" />
      View Annotated Files
    </Button>
    <Button 
      variant="default" 
      onClick={() => navigateToProject(review.project_id)}
    >
      Update Project
    </Button>
  </div>
)}
```

## 🔧 **Save Annotations Fix**

### **Current Issue:**
```tsx
const handleSave = () => {
  if (onSaveAnnotations) {
    onSaveAnnotations(annotations); // Only calls prop, doesn't save to DB
  }
};
```

### **Enhanced Save Functionality:**
```tsx
const handleSave = async () => {
  try {
    setSaving(true);
    
    // Option 1: If using the callback pattern
    if (onSaveAnnotations) {
      await onSaveAnnotations(annotations);
    }
    
    // Option 2: Direct API save (for standalone use)
    else {
      const headers = await getAuthHeaders();
      const response = await fetch('/api/quality/annotations/bulk-save', {
        method: 'POST',
        headers,
        body: JSON.stringify({
          annotations,
          fileUrl,
          fileName,
          qualityReviewId
        })
      });
      
      if (!response.ok) {
        throw new Error('Failed to save annotations');
      }
    }
    
    // Show success feedback
    setShowSuccessMessage(true);
    setTimeout(() => setShowSuccessMessage(false), 3000);
    
  } catch (error) {
    console.error('Error saving annotations:', error);
    setShowErrorMessage(true);
    setTimeout(() => setShowErrorMessage(false), 5000);
  } finally {
    setSaving(false);
  }
};
```

## 📊 **Database Schema Updates**

### **Enhanced file_annotations table:**
```sql
ALTER TABLE file_annotations 
ADD COLUMN quality_review_id UUID REFERENCES quality_reviews(id),
ADD COLUMN project_id UUID REFERENCES projects(id),
ADD COLUMN is_visible_to_designer BOOLEAN DEFAULT true;

-- Index for performance
CREATE INDEX idx_file_annotations_quality_review 
ON file_annotations(quality_review_id);

CREATE INDEX idx_file_annotations_project 
ON file_annotations(project_id);
```

## 🎯 **Expected Outcomes**

After implementation:

### **✅ Designers Will Be Able To:**
1. **View annotated images** in their project details
2. **See visual feedback** from quality team on specific areas
3. **Understand revision requests** with precise visual context
4. **Navigate from quality reviews** to annotated files
5. **Track annotation history** across project revisions

### **✅ Quality Team Benefits:**
1. **More effective communication** through visual feedback
2. **Reduced back-and-forth** with clearer guidance
3. **Better designer education** through visual examples
4. **Audit trail** of feedback provided

### **✅ Platform Benefits:**
1. **Improved designer satisfaction** through better feedback
2. **Faster revision cycles** with clearer requirements
3. **Higher quality deliverables** through better communication
4. **Reduced support burden** with self-service feedback viewing

## 📁 **Files to Modify**

### **New Files:**
- `src/app/api/quality/annotations/bulk-save/route.ts` - Bulk save endpoint
- `src/components/designer/AnnotatedFileViewer.tsx` - Designer-specific file viewer

### **Modified Files:**
- `src/app/designer/projects/[id]/page.tsx` - Add annotation viewing
- `src/app/designer/quality/page.tsx` - Add "View Files" buttons
- `src/components/quality/FileAnnotationTool.tsx` - Enhanced save functionality
- `migrations/enhance_file_annotations.sql` - Database updates

## 🚀 **Implementation Priority**

1. **HIGH**: Fix save annotations functionality (immediate impact)
2. **HIGH**: Add designer file viewing in project details (core feature)
3. **MEDIUM**: Enhance quality page with file links (nice to have)
4. **LOW**: Advanced features like annotation history (future enhancement)

This implementation will complete the quality feedback loop and provide designers with the visual context they need to make effective revisions.
# Designer Annotation Viewing Enhancement Plan

## 🎯 **Objective**
Enable designers to view their submitted images with quality team annotations after reviews are completed.

## 📋 **Current Gap Analysis**

### ❌ **What's Missing:**
1. **Designer Project View**: No image annotation viewing in `/designer/projects/[id]`
2. **Quality Review Integration**: No connection between quality reviews and annotated files
3. **File Accessibility**: Designers can't access quality-annotated versions of their files
4. **Visual Feedback**: Only text feedback shown, not visual markup

### ✅ **What Exists:**
1. **FileAnnotationTool Component**: Fully functional annotation system
2. **Annotation API**: Working CRUD operations for annotations
3. **Quality Review System**: Basic feedback display
4. **File Storage**: Cloudflare R2 integration

## 🔧 **Implementation Plan**

### **Phase 1: Designer Project File Viewing**
**File: `src/app/designer/projects/[id]/page.tsx`**

Add a new section to show submitted files with annotations:

```tsx
// Add to existing project detail page
<div className="space-y-6">
  {/* Existing project info */}
  
  {/* NEW: Submitted Files with Annotations */}
  <Card>
    <CardHeader>
      <CardTitle>Submitted Files & Quality Feedback</CardTitle>
    </CardHeader>
    <CardContent>
      {submittedFiles.map((file) => (
        <div key={file.id} className="border rounded-lg p-4 mb-4">
          <div className="flex justify-between items-center mb-3">
            <h4 className="font-medium">{file.name}</h4>
            <Badge variant={getQualityStatusVariant(file.quality_status)}>
              {file.quality_status}
            </Badge>
          </div>
          
          {file.annotations?.length > 0 && (
            <div>
              <p className="text-sm text-gray-600 mb-3">
                Quality team has provided visual feedback on this file:
              </p>
              <FileAnnotationTool
                fileUrl={file.url}
                fileName={file.name}
                fileType={file.type}
                existingAnnotations={file.annotations}
                readOnly={true} // Designers can only view, not edit
                reviewerName="Quality Team"
              />
            </div>
          )}
        </div>
      ))}
    </CardContent>
  </Card>
</div>
```

### **Phase 2: Quality Review File Association**
**File: `src/app/api/quality/annotations/route.ts`**

Enhance the API to link annotations with quality reviews:

```typescript
// Add quality_review_id to annotation creation
const annotation = await supabase
  .from('file_annotations')
  .insert({
    file_url: fileUrl,
    file_name: fileName,
    annotation_type: annotationType,
    x_position: xPosition,
    y_position: yPosition,
    title,
    description,
    reviewer_name: user.user_metadata?.full_name,
    quality_review_id: qualityReviewId, // NEW: Link to quality review
    created_at: new Date().toISOString()
  });
```

### **Phase 3: Enhanced Designer Quality Page**
**File: `src/app/designer/quality/page.tsx`**

Add "View Annotated Files" functionality:

```tsx
{review.status === 'needs_revision' && (
  <div className="flex gap-2 mt-3">
    <Button 
      variant="outline" 
      onClick={() => viewAnnotatedFiles(review.id)}
    >
      <FileImage className="h-4 w-4 mr-2" />
      View Annotated Files
    </Button>
    <Button 
      variant="default" 
      onClick={() => navigateToProject(review.project_id)}
    >
      Update Project
    </Button>
  </div>
)}
```

## 🔧 **Save Annotations Fix**

### **Current Issue:**
```tsx
const handleSave = () => {
  if (onSaveAnnotations) {
    onSaveAnnotations(annotations); // Only calls prop, doesn't save to DB
  }
};
```

### **Enhanced Save Functionality:**
```tsx
const handleSave = async () => {
  try {
    setSaving(true);
    
    // Option 1: If using the callback pattern
    if (onSaveAnnotations) {
      await onSaveAnnotations(annotations);
    }
    
    // Option 2: Direct API save (for standalone use)
    else {
      const headers = await getAuthHeaders();
      const response = await fetch('/api/quality/annotations/bulk-save', {
        method: 'POST',
        headers,
        body: JSON.stringify({
          annotations,
          fileUrl,
          fileName,
          qualityReviewId
        })
      });
      
      if (!response.ok) {
        throw new Error('Failed to save annotations');
      }
    }
    
    // Show success feedback
    setShowSuccessMessage(true);
    setTimeout(() => setShowSuccessMessage(false), 3000);
    
  } catch (error) {
    console.error('Error saving annotations:', error);
    setShowErrorMessage(true);
    setTimeout(() => setShowErrorMessage(false), 5000);
  } finally {
    setSaving(false);
  }
};
```

## 📊 **Database Schema Updates**

### **Enhanced file_annotations table:**
```sql
ALTER TABLE file_annotations 
ADD COLUMN quality_review_id UUID REFERENCES quality_reviews(id),
ADD COLUMN project_id UUID REFERENCES projects(id),
ADD COLUMN is_visible_to_designer BOOLEAN DEFAULT true;

-- Index for performance
CREATE INDEX idx_file_annotations_quality_review 
ON file_annotations(quality_review_id);

CREATE INDEX idx_file_annotations_project 
ON file_annotations(project_id);
```

## 🎯 **Expected Outcomes**

After implementation:

### **✅ Designers Will Be Able To:**
1. **View annotated images** in their project details
2. **See visual feedback** from quality team on specific areas
3. **Understand revision requests** with precise visual context
4. **Navigate from quality reviews** to annotated files
5. **Track annotation history** across project revisions

### **✅ Quality Team Benefits:**
1. **More effective communication** through visual feedback
2. **Reduced back-and-forth** with clearer guidance
3. **Better designer education** through visual examples
4. **Audit trail** of feedback provided

### **✅ Platform Benefits:**
1. **Improved designer satisfaction** through better feedback
2. **Faster revision cycles** with clearer requirements
3. **Higher quality deliverables** through better communication
4. **Reduced support burden** with self-service feedback viewing

## 📁 **Files to Modify**

### **New Files:**
- `src/app/api/quality/annotations/bulk-save/route.ts` - Bulk save endpoint
- `src/components/designer/AnnotatedFileViewer.tsx` - Designer-specific file viewer

### **Modified Files:**
- `src/app/designer/projects/[id]/page.tsx` - Add annotation viewing
- `src/app/designer/quality/page.tsx` - Add "View Files" buttons
- `src/components/quality/FileAnnotationTool.tsx` - Enhanced save functionality
- `migrations/enhance_file_annotations.sql` - Database updates

## 🚀 **Implementation Priority**

1. **HIGH**: Fix save annotations functionality (immediate impact)
2. **HIGH**: Add designer file viewing in project details (core feature)
3. **MEDIUM**: Enhance quality page with file links (nice to have)
4. **LOW**: Advanced features like annotation history (future enhancement)

This implementation will complete the quality feedback loop and provide designers with the visual context they need to make effective revisions.
# Designer Annotation Viewing Enhancement Plan

## 🎯 **Objective**
Enable designers to view their submitted images with quality team annotations after reviews are completed.

## 📋 **Current Gap Analysis**

### ❌ **What's Missing:**
1. **Designer Project View**: No image annotation viewing in `/designer/projects/[id]`
2. **Quality Review Integration**: No connection between quality reviews and annotated files
3. **File Accessibility**: Designers can't access quality-annotated versions of their files
4. **Visual Feedback**: Only text feedback shown, not visual markup

### ✅ **What Exists:**
1. **FileAnnotationTool Component**: Fully functional annotation system
2. **Annotation API**: Working CRUD operations for annotations
3. **Quality Review System**: Basic feedback display
4. **File Storage**: Cloudflare R2 integration

## 🔧 **Implementation Plan**

### **Phase 1: Designer Project File Viewing**
**File: `src/app/designer/projects/[id]/page.tsx`**

Add a new section to show submitted files with annotations:

```tsx
// Add to existing project detail page
<div className="space-y-6">
  {/* Existing project info */}
  
  {/* NEW: Submitted Files with Annotations */}
  <Card>
    <CardHeader>
      <CardTitle>Submitted Files & Quality Feedback</CardTitle>
    </CardHeader>
    <CardContent>
      {submittedFiles.map((file) => (
        <div key={file.id} className="border rounded-lg p-4 mb-4">
          <div className="flex justify-between items-center mb-3">
            <h4 className="font-medium">{file.name}</h4>
            <Badge variant={getQualityStatusVariant(file.quality_status)}>
              {file.quality_status}
            </Badge>
          </div>
          
          {file.annotations?.length > 0 && (
            <div>
              <p className="text-sm text-gray-600 mb-3">
                Quality team has provided visual feedback on this file:
              </p>
              <FileAnnotationTool
                fileUrl={file.url}
                fileName={file.name}
                fileType={file.type}
                existingAnnotations={file.annotations}
                readOnly={true} // Designers can only view, not edit
                reviewerName="Quality Team"
              />
            </div>
          )}
        </div>
      ))}
    </CardContent>
  </Card>
</div>
```

### **Phase 2: Quality Review File Association**
**File: `src/app/api/quality/annotations/route.ts`**

Enhance the API to link annotations with quality reviews:

```typescript
// Add quality_review_id to annotation creation
const annotation = await supabase
  .from('file_annotations')
  .insert({
    file_url: fileUrl,
    file_name: fileName,
    annotation_type: annotationType,
    x_position: xPosition,
    y_position: yPosition,
    title,
    description,
    reviewer_name: user.user_metadata?.full_name,
    quality_review_id: qualityReviewId, // NEW: Link to quality review
    created_at: new Date().toISOString()
  });
```

### **Phase 3: Enhanced Designer Quality Page**
**File: `src/app/designer/quality/page.tsx`**

Add "View Annotated Files" functionality:

```tsx
{review.status === 'needs_revision' && (
  <div className="flex gap-2 mt-3">
    <Button 
      variant="outline" 
      onClick={() => viewAnnotatedFiles(review.id)}
    >
      <FileImage className="h-4 w-4 mr-2" />
      View Annotated Files
    </Button>
    <Button 
      variant="default" 
      onClick={() => navigateToProject(review.project_id)}
    >
      Update Project
    </Button>
  </div>
)}
```

## 🔧 **Save Annotations Fix**

### **Current Issue:**
```tsx
const handleSave = () => {
  if (onSaveAnnotations) {
    onSaveAnnotations(annotations); // Only calls prop, doesn't save to DB
  }
};
```

### **Enhanced Save Functionality:**
```tsx
const handleSave = async () => {
  try {
    setSaving(true);
    
    // Option 1: If using the callback pattern
    if (onSaveAnnotations) {
      await onSaveAnnotations(annotations);
    }
    
    // Option 2: Direct API save (for standalone use)
    else {
      const headers = await getAuthHeaders();
      const response = await fetch('/api/quality/annotations/bulk-save', {
        method: 'POST',
        headers,
        body: JSON.stringify({
          annotations,
          fileUrl,
          fileName,
          qualityReviewId
        })
      });
      
      if (!response.ok) {
        throw new Error('Failed to save annotations');
      }
    }
    
    // Show success feedback
    setShowSuccessMessage(true);
    setTimeout(() => setShowSuccessMessage(false), 3000);
    
  } catch (error) {
    console.error('Error saving annotations:', error);
    setShowErrorMessage(true);
    setTimeout(() => setShowErrorMessage(false), 5000);
  } finally {
    setSaving(false);
  }
};
```

## 📊 **Database Schema Updates**

### **Enhanced file_annotations table:**
```sql
ALTER TABLE file_annotations 
ADD COLUMN quality_review_id UUID REFERENCES quality_reviews(id),
ADD COLUMN project_id UUID REFERENCES projects(id),
ADD COLUMN is_visible_to_designer BOOLEAN DEFAULT true;

-- Index for performance
CREATE INDEX idx_file_annotations_quality_review 
ON file_annotations(quality_review_id);

CREATE INDEX idx_file_annotations_project 
ON file_annotations(project_id);
```

## 🎯 **Expected Outcomes**

After implementation:

### **✅ Designers Will Be Able To:**
1. **View annotated images** in their project details
2. **See visual feedback** from quality team on specific areas
3. **Understand revision requests** with precise visual context
4. **Navigate from quality reviews** to annotated files
5. **Track annotation history** across project revisions

### **✅ Quality Team Benefits:**
1. **More effective communication** through visual feedback
2. **Reduced back-and-forth** with clearer guidance
3. **Better designer education** through visual examples
4. **Audit trail** of feedback provided

### **✅ Platform Benefits:**
1. **Improved designer satisfaction** through better feedback
2. **Faster revision cycles** with clearer requirements
3. **Higher quality deliverables** through better communication
4. **Reduced support burden** with self-service feedback viewing

## 📁 **Files to Modify**

### **New Files:**
- `src/app/api/quality/annotations/bulk-save/route.ts` - Bulk save endpoint
- `src/components/designer/AnnotatedFileViewer.tsx` - Designer-specific file viewer

### **Modified Files:**
- `src/app/designer/projects/[id]/page.tsx` - Add annotation viewing
- `src/app/designer/quality/page.tsx` - Add "View Files" buttons
- `src/components/quality/FileAnnotationTool.tsx` - Enhanced save functionality
- `migrations/enhance_file_annotations.sql` - Database updates

## 🚀 **Implementation Priority**

1. **HIGH**: Fix save annotations functionality (immediate impact)
2. **HIGH**: Add designer file viewing in project details (core feature)
3. **MEDIUM**: Enhance quality page with file links (nice to have)
4. **LOW**: Advanced features like annotation history (future enhancement)

This implementation will complete the quality feedback loop and provide designers with the visual context they need to make effective revisions.
# Designer Annotation Viewing Enhancement Plan

## 🎯 **Objective**
Enable designers to view their submitted images with quality team annotations after reviews are completed.

## 📋 **Current Gap Analysis**

### ❌ **What's Missing:**
1. **Designer Project View**: No image annotation viewing in `/designer/projects/[id]`
2. **Quality Review Integration**: No connection between quality reviews and annotated files
3. **File Accessibility**: Designers can't access quality-annotated versions of their files
4. **Visual Feedback**: Only text feedback shown, not visual markup

### ✅ **What Exists:**
1. **FileAnnotationTool Component**: Fully functional annotation system
2. **Annotation API**: Working CRUD operations for annotations
3. **Quality Review System**: Basic feedback display
4. **File Storage**: Cloudflare R2 integration

## 🔧 **Implementation Plan**

### **Phase 1: Designer Project File Viewing**
**File: `src/app/designer/projects/[id]/page.tsx`**

Add a new section to show submitted files with annotations:

```tsx
// Add to existing project detail page
<div className="space-y-6">
  {/* Existing project info */}
  
  {/* NEW: Submitted Files with Annotations */}
  <Card>
    <CardHeader>
      <CardTitle>Submitted Files & Quality Feedback</CardTitle>
    </CardHeader>
    <CardContent>
      {submittedFiles.map((file) => (
        <div key={file.id} className="border rounded-lg p-4 mb-4">
          <div className="flex justify-between items-center mb-3">
            <h4 className="font-medium">{file.name}</h4>
            <Badge variant={getQualityStatusVariant(file.quality_status)}>
              {file.quality_status}
            </Badge>
          </div>
          
          {file.annotations?.length > 0 && (
            <div>
              <p className="text-sm text-gray-600 mb-3">
                Quality team has provided visual feedback on this file:
              </p>
              <FileAnnotationTool
                fileUrl={file.url}
                fileName={file.name}
                fileType={file.type}
                existingAnnotations={file.annotations}
                readOnly={true} // Designers can only view, not edit
                reviewerName="Quality Team"
              />
            </div>
          )}
        </div>
      ))}
    </CardContent>
  </Card>
</div>
```

### **Phase 2: Quality Review File Association**
**File: `src/app/api/quality/annotations/route.ts`**

Enhance the API to link annotations with quality reviews:

```typescript
// Add quality_review_id to annotation creation
const annotation = await supabase
  .from('file_annotations')
  .insert({
    file_url: fileUrl,
    file_name: fileName,
    annotation_type: annotationType,
    x_position: xPosition,
    y_position: yPosition,
    title,
    description,
    reviewer_name: user.user_metadata?.full_name,
    quality_review_id: qualityReviewId, // NEW: Link to quality review
    created_at: new Date().toISOString()
  });
```

### **Phase 3: Enhanced Designer Quality Page**
**File: `src/app/designer/quality/page.tsx`**

Add "View Annotated Files" functionality:

```tsx
{review.status === 'needs_revision' && (
  <div className="flex gap-2 mt-3">
    <Button 
      variant="outline" 
      onClick={() => viewAnnotatedFiles(review.id)}
    >
      <FileImage className="h-4 w-4 mr-2" />
      View Annotated Files
    </Button>
    <Button 
      variant="default" 
      onClick={() => navigateToProject(review.project_id)}
    >
      Update Project
    </Button>
  </div>
)}
```

## 🔧 **Save Annotations Fix**

### **Current Issue:**
```tsx
const handleSave = () => {
  if (onSaveAnnotations) {
    onSaveAnnotations(annotations); // Only calls prop, doesn't save to DB
  }
};
```

### **Enhanced Save Functionality:**
```tsx
const handleSave = async () => {
  try {
    setSaving(true);
    
    // Option 1: If using the callback pattern
    if (onSaveAnnotations) {
      await onSaveAnnotations(annotations);
    }
    
    // Option 2: Direct API save (for standalone use)
    else {
      const headers = await getAuthHeaders();
      const response = await fetch('/api/quality/annotations/bulk-save', {
        method: 'POST',
        headers,
        body: JSON.stringify({
          annotations,
          fileUrl,
          fileName,
          qualityReviewId
        })
      });
      
      if (!response.ok) {
        throw new Error('Failed to save annotations');
      }
    }
    
    // Show success feedback
    setShowSuccessMessage(true);
    setTimeout(() => setShowSuccessMessage(false), 3000);
    
  } catch (error) {
    console.error('Error saving annotations:', error);
    setShowErrorMessage(true);
    setTimeout(() => setShowErrorMessage(false), 5000);
  } finally {
    setSaving(false);
  }
};
```

## 📊 **Database Schema Updates**

### **Enhanced file_annotations table:**
```sql
ALTER TABLE file_annotations 
ADD COLUMN quality_review_id UUID REFERENCES quality_reviews(id),
ADD COLUMN project_id UUID REFERENCES projects(id),
ADD COLUMN is_visible_to_designer BOOLEAN DEFAULT true;

-- Index for performance
CREATE INDEX idx_file_annotations_quality_review 
ON file_annotations(quality_review_id);

CREATE INDEX idx_file_annotations_project 
ON file_annotations(project_id);
```

## 🎯 **Expected Outcomes**

After implementation:

### **✅ Designers Will Be Able To:**
1. **View annotated images** in their project details
2. **See visual feedback** from quality team on specific areas
3. **Understand revision requests** with precise visual context
4. **Navigate from quality reviews** to annotated files
5. **Track annotation history** across project revisions

### **✅ Quality Team Benefits:**
1. **More effective communication** through visual feedback
2. **Reduced back-and-forth** with clearer guidance
3. **Better designer education** through visual examples
4. **Audit trail** of feedback provided

### **✅ Platform Benefits:**
1. **Improved designer satisfaction** through better feedback
2. **Faster revision cycles** with clearer requirements
3. **Higher quality deliverables** through better communication
4. **Reduced support burden** with self-service feedback viewing

## 📁 **Files to Modify**

### **New Files:**
- `src/app/api/quality/annotations/bulk-save/route.ts` - Bulk save endpoint
- `src/components/designer/AnnotatedFileViewer.tsx` - Designer-specific file viewer

### **Modified Files:**
- `src/app/designer/projects/[id]/page.tsx` - Add annotation viewing
- `src/app/designer/quality/page.tsx` - Add "View Files" buttons
- `src/components/quality/FileAnnotationTool.tsx` - Enhanced save functionality
- `migrations/enhance_file_annotations.sql` - Database updates

## 🚀 **Implementation Priority**

1. **HIGH**: Fix save annotations functionality (immediate impact)
2. **HIGH**: Add designer file viewing in project details (core feature)
3. **MEDIUM**: Enhance quality page with file links (nice to have)
4. **LOW**: Advanced features like annotation history (future enhancement)

This implementation will complete the quality feedback loop and provide designers with the visual context they need to make effective revisions.
# Designer Annotation Viewing Enhancement Plan

## 🎯 **Objective**
Enable designers to view their submitted images with quality team annotations after reviews are completed.

## 📋 **Current Gap Analysis**

### ❌ **What's Missing:**
1. **Designer Project View**: No image annotation viewing in `/designer/projects/[id]`
2. **Quality Review Integration**: No connection between quality reviews and annotated files
3. **File Accessibility**: Designers can't access quality-annotated versions of their files
4. **Visual Feedback**: Only text feedback shown, not visual markup

### ✅ **What Exists:**
1. **FileAnnotationTool Component**: Fully functional annotation system
2. **Annotation API**: Working CRUD operations for annotations
3. **Quality Review System**: Basic feedback display
4. **File Storage**: Cloudflare R2 integration

## 🔧 **Implementation Plan**

### **Phase 1: Designer Project File Viewing**
**File: `src/app/designer/projects/[id]/page.tsx`**

Add a new section to show submitted files with annotations:

```tsx
// Add to existing project detail page
<div className="space-y-6">
  {/* Existing project info */}
  
  {/* NEW: Submitted Files with Annotations */}
  <Card>
    <CardHeader>
      <CardTitle>Submitted Files & Quality Feedback</CardTitle>
    </CardHeader>
    <CardContent>
      {submittedFiles.map((file) => (
        <div key={file.id} className="border rounded-lg p-4 mb-4">
          <div className="flex justify-between items-center mb-3">
            <h4 className="font-medium">{file.name}</h4>
            <Badge variant={getQualityStatusVariant(file.quality_status)}>
              {file.quality_status}
            </Badge>
          </div>
          
          {file.annotations?.length > 0 && (
            <div>
              <p className="text-sm text-gray-600 mb-3">
                Quality team has provided visual feedback on this file:
              </p>
              <FileAnnotationTool
                fileUrl={file.url}
                fileName={file.name}
                fileType={file.type}
                existingAnnotations={file.annotations}
                readOnly={true} // Designers can only view, not edit
                reviewerName="Quality Team"
              />
            </div>
          )}
        </div>
      ))}
    </CardContent>
  </Card>
</div>
```

### **Phase 2: Quality Review File Association**
**File: `src/app/api/quality/annotations/route.ts`**

Enhance the API to link annotations with quality reviews:

```typescript
// Add quality_review_id to annotation creation
const annotation = await supabase
  .from('file_annotations')
  .insert({
    file_url: fileUrl,
    file_name: fileName,
    annotation_type: annotationType,
    x_position: xPosition,
    y_position: yPosition,
    title,
    description,
    reviewer_name: user.user_metadata?.full_name,
    quality_review_id: qualityReviewId, // NEW: Link to quality review
    created_at: new Date().toISOString()
  });
```

### **Phase 3: Enhanced Designer Quality Page**
**File: `src/app/designer/quality/page.tsx`**

Add "View Annotated Files" functionality:

```tsx
{review.status === 'needs_revision' && (
  <div className="flex gap-2 mt-3">
    <Button 
      variant="outline" 
      onClick={() => viewAnnotatedFiles(review.id)}
    >
      <FileImage className="h-4 w-4 mr-2" />
      View Annotated Files
    </Button>
    <Button 
      variant="default" 
      onClick={() => navigateToProject(review.project_id)}
    >
      Update Project
    </Button>
  </div>
)}
```

## 🔧 **Save Annotations Fix**

### **Current Issue:**
```tsx
const handleSave = () => {
  if (onSaveAnnotations) {
    onSaveAnnotations(annotations); // Only calls prop, doesn't save to DB
  }
};
```

### **Enhanced Save Functionality:**
```tsx
const handleSave = async () => {
  try {
    setSaving(true);
    
    // Option 1: If using the callback pattern
    if (onSaveAnnotations) {
      await onSaveAnnotations(annotations);
    }
    
    // Option 2: Direct API save (for standalone use)
    else {
      const headers = await getAuthHeaders();
      const response = await fetch('/api/quality/annotations/bulk-save', {
        method: 'POST',
        headers,
        body: JSON.stringify({
          annotations,
          fileUrl,
          fileName,
          qualityReviewId
        })
      });
      
      if (!response.ok) {
        throw new Error('Failed to save annotations');
      }
    }
    
    // Show success feedback
    setShowSuccessMessage(true);
    setTimeout(() => setShowSuccessMessage(false), 3000);
    
  } catch (error) {
    console.error('Error saving annotations:', error);
    setShowErrorMessage(true);
    setTimeout(() => setShowErrorMessage(false), 5000);
  } finally {
    setSaving(false);
  }
};
```

## 📊 **Database Schema Updates**

### **Enhanced file_annotations table:**
```sql
ALTER TABLE file_annotations 
ADD COLUMN quality_review_id UUID REFERENCES quality_reviews(id),
ADD COLUMN project_id UUID REFERENCES projects(id),
ADD COLUMN is_visible_to_designer BOOLEAN DEFAULT true;

-- Index for performance
CREATE INDEX idx_file_annotations_quality_review 
ON file_annotations(quality_review_id);

CREATE INDEX idx_file_annotations_project 
ON file_annotations(project_id);
```

## 🎯 **Expected Outcomes**

After implementation:

### **✅ Designers Will Be Able To:**
1. **View annotated images** in their project details
2. **See visual feedback** from quality team on specific areas
3. **Understand revision requests** with precise visual context
4. **Navigate from quality reviews** to annotated files
5. **Track annotation history** across project revisions

### **✅ Quality Team Benefits:**
1. **More effective communication** through visual feedback
2. **Reduced back-and-forth** with clearer guidance
3. **Better designer education** through visual examples
4. **Audit trail** of feedback provided

### **✅ Platform Benefits:**
1. **Improved designer satisfaction** through better feedback
2. **Faster revision cycles** with clearer requirements
3. **Higher quality deliverables** through better communication
4. **Reduced support burden** with self-service feedback viewing

## 📁 **Files to Modify**

### **New Files:**
- `src/app/api/quality/annotations/bulk-save/route.ts` - Bulk save endpoint
- `src/components/designer/AnnotatedFileViewer.tsx` - Designer-specific file viewer

### **Modified Files:**
- `src/app/designer/projects/[id]/page.tsx` - Add annotation viewing
- `src/app/designer/quality/page.tsx` - Add "View Files" buttons
- `src/components/quality/FileAnnotationTool.tsx` - Enhanced save functionality
- `migrations/enhance_file_annotations.sql` - Database updates

## 🚀 **Implementation Priority**

1. **HIGH**: Fix save annotations functionality (immediate impact)
2. **HIGH**: Add designer file viewing in project details (core feature)
3. **MEDIUM**: Enhance quality page with file links (nice to have)
4. **LOW**: Advanced features like annotation history (future enhancement)

This implementation will complete the quality feedback loop and provide designers with the visual context they need to make effective revisions.
# Designer Annotation Viewing Enhancement Plan

## 🎯 **Objective**
Enable designers to view their submitted images with quality team annotations after reviews are completed.

## 📋 **Current Gap Analysis**

### ❌ **What's Missing:**
1. **Designer Project View**: No image annotation viewing in `/designer/projects/[id]`
2. **Quality Review Integration**: No connection between quality reviews and annotated files
3. **File Accessibility**: Designers can't access quality-annotated versions of their files
4. **Visual Feedback**: Only text feedback shown, not visual markup

### ✅ **What Exists:**
1. **FileAnnotationTool Component**: Fully functional annotation system
2. **Annotation API**: Working CRUD operations for annotations
3. **Quality Review System**: Basic feedback display
4. **File Storage**: Cloudflare R2 integration

## 🔧 **Implementation Plan**

### **Phase 1: Designer Project File Viewing**
**File: `src/app/designer/projects/[id]/page.tsx`**

Add a new section to show submitted files with annotations:

```tsx
// Add to existing project detail page
<div className="space-y-6">
  {/* Existing project info */}
  
  {/* NEW: Submitted Files with Annotations */}
  <Card>
    <CardHeader>
      <CardTitle>Submitted Files & Quality Feedback</CardTitle>
    </CardHeader>
    <CardContent>
      {submittedFiles.map((file) => (
        <div key={file.id} className="border rounded-lg p-4 mb-4">
          <div className="flex justify-between items-center mb-3">
            <h4 className="font-medium">{file.name}</h4>
            <Badge variant={getQualityStatusVariant(file.quality_status)}>
              {file.quality_status}
            </Badge>
          </div>
          
          {file.annotations?.length > 0 && (
            <div>
              <p className="text-sm text-gray-600 mb-3">
                Quality team has provided visual feedback on this file:
              </p>
              <FileAnnotationTool
                fileUrl={file.url}
                fileName={file.name}
                fileType={file.type}
                existingAnnotations={file.annotations}
                readOnly={true} // Designers can only view, not edit
                reviewerName="Quality Team"
              />
            </div>
          )}
        </div>
      ))}
    </CardContent>
  </Card>
</div>
```

### **Phase 2: Quality Review File Association**
**File: `src/app/api/quality/annotations/route.ts`**

Enhance the API to link annotations with quality reviews:

```typescript
// Add quality_review_id to annotation creation
const annotation = await supabase
  .from('file_annotations')
  .insert({
    file_url: fileUrl,
    file_name: fileName,
    annotation_type: annotationType,
    x_position: xPosition,
    y_position: yPosition,
    title,
    description,
    reviewer_name: user.user_metadata?.full_name,
    quality_review_id: qualityReviewId, // NEW: Link to quality review
    created_at: new Date().toISOString()
  });
```

### **Phase 3: Enhanced Designer Quality Page**
**File: `src/app/designer/quality/page.tsx`**

Add "View Annotated Files" functionality:

```tsx
{review.status === 'needs_revision' && (
  <div className="flex gap-2 mt-3">
    <Button 
      variant="outline" 
      onClick={() => viewAnnotatedFiles(review.id)}
    >
      <FileImage className="h-4 w-4 mr-2" />
      View Annotated Files
    </Button>
    <Button 
      variant="default" 
      onClick={() => navigateToProject(review.project_id)}
    >
      Update Project
    </Button>
  </div>
)}
```

## 🔧 **Save Annotations Fix**

### **Current Issue:**
```tsx
const handleSave = () => {
  if (onSaveAnnotations) {
    onSaveAnnotations(annotations); // Only calls prop, doesn't save to DB
  }
};
```

### **Enhanced Save Functionality:**
```tsx
const handleSave = async () => {
  try {
    setSaving(true);
    
    // Option 1: If using the callback pattern
    if (onSaveAnnotations) {
      await onSaveAnnotations(annotations);
    }
    
    // Option 2: Direct API save (for standalone use)
    else {
      const headers = await getAuthHeaders();
      const response = await fetch('/api/quality/annotations/bulk-save', {
        method: 'POST',
        headers,
        body: JSON.stringify({
          annotations,
          fileUrl,
          fileName,
          qualityReviewId
        })
      });
      
      if (!response.ok) {
        throw new Error('Failed to save annotations');
      }
    }
    
    // Show success feedback
    setShowSuccessMessage(true);
    setTimeout(() => setShowSuccessMessage(false), 3000);
    
  } catch (error) {
    console.error('Error saving annotations:', error);
    setShowErrorMessage(true);
    setTimeout(() => setShowErrorMessage(false), 5000);
  } finally {
    setSaving(false);
  }
};
```

## 📊 **Database Schema Updates**

### **Enhanced file_annotations table:**
```sql
ALTER TABLE file_annotations 
ADD COLUMN quality_review_id UUID REFERENCES quality_reviews(id),
ADD COLUMN project_id UUID REFERENCES projects(id),
ADD COLUMN is_visible_to_designer BOOLEAN DEFAULT true;

-- Index for performance
CREATE INDEX idx_file_annotations_quality_review 
ON file_annotations(quality_review_id);

CREATE INDEX idx_file_annotations_project 
ON file_annotations(project_id);
```

## 🎯 **Expected Outcomes**

After implementation:

### **✅ Designers Will Be Able To:**
1. **View annotated images** in their project details
2. **See visual feedback** from quality team on specific areas
3. **Understand revision requests** with precise visual context
4. **Navigate from quality reviews** to annotated files
5. **Track annotation history** across project revisions

### **✅ Quality Team Benefits:**
1. **More effective communication** through visual feedback
2. **Reduced back-and-forth** with clearer guidance
3. **Better designer education** through visual examples
4. **Audit trail** of feedback provided

### **✅ Platform Benefits:**
1. **Improved designer satisfaction** through better feedback
2. **Faster revision cycles** with clearer requirements
3. **Higher quality deliverables** through better communication
4. **Reduced support burden** with self-service feedback viewing

## 📁 **Files to Modify**

### **New Files:**
- `src/app/api/quality/annotations/bulk-save/route.ts` - Bulk save endpoint
- `src/components/designer/AnnotatedFileViewer.tsx` - Designer-specific file viewer

### **Modified Files:**
- `src/app/designer/projects/[id]/page.tsx` - Add annotation viewing
- `src/app/designer/quality/page.tsx` - Add "View Files" buttons
- `src/components/quality/FileAnnotationTool.tsx` - Enhanced save functionality
- `migrations/enhance_file_annotations.sql` - Database updates

## 🚀 **Implementation Priority**

1. **HIGH**: Fix save annotations functionality (immediate impact)
2. **HIGH**: Add designer file viewing in project details (core feature)
3. **MEDIUM**: Enhance quality page with file links (nice to have)
4. **LOW**: Advanced features like annotation history (future enhancement)

This implementation will complete the quality feedback loop and provide designers with the visual context they need to make effective revisions.
# Designer Annotation Viewing Enhancement Plan

## 🎯 **Objective**
Enable designers to view their submitted images with quality team annotations after reviews are completed.

## 📋 **Current Gap Analysis**

### ❌ **What's Missing:**
1. **Designer Project View**: No image annotation viewing in `/designer/projects/[id]`
2. **Quality Review Integration**: No connection between quality reviews and annotated files
3. **File Accessibility**: Designers can't access quality-annotated versions of their files
4. **Visual Feedback**: Only text feedback shown, not visual markup

### ✅ **What Exists:**
1. **FileAnnotationTool Component**: Fully functional annotation system
2. **Annotation API**: Working CRUD operations for annotations
3. **Quality Review System**: Basic feedback display
4. **File Storage**: Cloudflare R2 integration

## 🔧 **Implementation Plan**

### **Phase 1: Designer Project File Viewing**
**File: `src/app/designer/projects/[id]/page.tsx`**

Add a new section to show submitted files with annotations:

```tsx
// Add to existing project detail page
<div className="space-y-6">
  {/* Existing project info */}
  
  {/* NEW: Submitted Files with Annotations */}
  <Card>
    <CardHeader>
      <CardTitle>Submitted Files & Quality Feedback</CardTitle>
    </CardHeader>
    <CardContent>
      {submittedFiles.map((file) => (
        <div key={file.id} className="border rounded-lg p-4 mb-4">
          <div className="flex justify-between items-center mb-3">
            <h4 className="font-medium">{file.name}</h4>
            <Badge variant={getQualityStatusVariant(file.quality_status)}>
              {file.quality_status}
            </Badge>
          </div>
          
          {file.annotations?.length > 0 && (
            <div>
              <p className="text-sm text-gray-600 mb-3">
                Quality team has provided visual feedback on this file:
              </p>
              <FileAnnotationTool
                fileUrl={file.url}
                fileName={file.name}
                fileType={file.type}
                existingAnnotations={file.annotations}
                readOnly={true} // Designers can only view, not edit
                reviewerName="Quality Team"
              />
            </div>
          )}
        </div>
      ))}
    </CardContent>
  </Card>
</div>
```

### **Phase 2: Quality Review File Association**
**File: `src/app/api/quality/annotations/route.ts`**

Enhance the API to link annotations with quality reviews:

```typescript
// Add quality_review_id to annotation creation
const annotation = await supabase
  .from('file_annotations')
  .insert({
    file_url: fileUrl,
    file_name: fileName,
    annotation_type: annotationType,
    x_position: xPosition,
    y_position: yPosition,
    title,
    description,
    reviewer_name: user.user_metadata?.full_name,
    quality_review_id: qualityReviewId, // NEW: Link to quality review
    created_at: new Date().toISOString()
  });
```

### **Phase 3: Enhanced Designer Quality Page**
**File: `src/app/designer/quality/page.tsx`**

Add "View Annotated Files" functionality:

```tsx
{review.status === 'needs_revision' && (
  <div className="flex gap-2 mt-3">
    <Button 
      variant="outline" 
      onClick={() => viewAnnotatedFiles(review.id)}
    >
      <FileImage className="h-4 w-4 mr-2" />
      View Annotated Files
    </Button>
    <Button 
      variant="default" 
      onClick={() => navigateToProject(review.project_id)}
    >
      Update Project
    </Button>
  </div>
)}
```

## 🔧 **Save Annotations Fix**

### **Current Issue:**
```tsx
const handleSave = () => {
  if (onSaveAnnotations) {
    onSaveAnnotations(annotations); // Only calls prop, doesn't save to DB
  }
};
```

### **Enhanced Save Functionality:**
```tsx
const handleSave = async () => {
  try {
    setSaving(true);
    
    // Option 1: If using the callback pattern
    if (onSaveAnnotations) {
      await onSaveAnnotations(annotations);
    }
    
    // Option 2: Direct API save (for standalone use)
    else {
      const headers = await getAuthHeaders();
      const response = await fetch('/api/quality/annotations/bulk-save', {
        method: 'POST',
        headers,
        body: JSON.stringify({
          annotations,
          fileUrl,
          fileName,
          qualityReviewId
        })
      });
      
      if (!response.ok) {
        throw new Error('Failed to save annotations');
      }
    }
    
    // Show success feedback
    setShowSuccessMessage(true);
    setTimeout(() => setShowSuccessMessage(false), 3000);
    
  } catch (error) {
    console.error('Error saving annotations:', error);
    setShowErrorMessage(true);
    setTimeout(() => setShowErrorMessage(false), 5000);
  } finally {
    setSaving(false);
  }
};
```

## 📊 **Database Schema Updates**

### **Enhanced file_annotations table:**
```sql
ALTER TABLE file_annotations 
ADD COLUMN quality_review_id UUID REFERENCES quality_reviews(id),
ADD COLUMN project_id UUID REFERENCES projects(id),
ADD COLUMN is_visible_to_designer BOOLEAN DEFAULT true;

-- Index for performance
CREATE INDEX idx_file_annotations_quality_review 
ON file_annotations(quality_review_id);

CREATE INDEX idx_file_annotations_project 
ON file_annotations(project_id);
```

## 🎯 **Expected Outcomes**

After implementation:

### **✅ Designers Will Be Able To:**
1. **View annotated images** in their project details
2. **See visual feedback** from quality team on specific areas
3. **Understand revision requests** with precise visual context
4. **Navigate from quality reviews** to annotated files
5. **Track annotation history** across project revisions

### **✅ Quality Team Benefits:**
1. **More effective communication** through visual feedback
2. **Reduced back-and-forth** with clearer guidance
3. **Better designer education** through visual examples
4. **Audit trail** of feedback provided

### **✅ Platform Benefits:**
1. **Improved designer satisfaction** through better feedback
2. **Faster revision cycles** with clearer requirements
3. **Higher quality deliverables** through better communication
4. **Reduced support burden** with self-service feedback viewing

## 📁 **Files to Modify**

### **New Files:**
- `src/app/api/quality/annotations/bulk-save/route.ts` - Bulk save endpoint
- `src/components/designer/AnnotatedFileViewer.tsx` - Designer-specific file viewer

### **Modified Files:**
- `src/app/designer/projects/[id]/page.tsx` - Add annotation viewing
- `src/app/designer/quality/page.tsx` - Add "View Files" buttons
- `src/components/quality/FileAnnotationTool.tsx` - Enhanced save functionality
- `migrations/enhance_file_annotations.sql` - Database updates

## 🚀 **Implementation Priority**

1. **HIGH**: Fix save annotations functionality (immediate impact)
2. **HIGH**: Add designer file viewing in project details (core feature)
3. **MEDIUM**: Enhance quality page with file links (nice to have)
4. **LOW**: Advanced features like annotation history (future enhancement)

This implementation will complete the quality feedback loop and provide designers with the visual context they need to make effective revisions.
# Designer Annotation Viewing Enhancement Plan

## 🎯 **Objective**
Enable designers to view their submitted images with quality team annotations after reviews are completed.

## 📋 **Current Gap Analysis**

### ❌ **What's Missing:**
1. **Designer Project View**: No image annotation viewing in `/designer/projects/[id]`
2. **Quality Review Integration**: No connection between quality reviews and annotated files
3. **File Accessibility**: Designers can't access quality-annotated versions of their files
4. **Visual Feedback**: Only text feedback shown, not visual markup

### ✅ **What Exists:**
1. **FileAnnotationTool Component**: Fully functional annotation system
2. **Annotation API**: Working CRUD operations for annotations
3. **Quality Review System**: Basic feedback display
4. **File Storage**: Cloudflare R2 integration

## 🔧 **Implementation Plan**

### **Phase 1: Designer Project File Viewing**
**File: `src/app/designer/projects/[id]/page.tsx`**

Add a new section to show submitted files with annotations:

```tsx
// Add to existing project detail page
<div className="space-y-6">
  {/* Existing project info */}
  
  {/* NEW: Submitted Files with Annotations */}
  <Card>
    <CardHeader>
      <CardTitle>Submitted Files & Quality Feedback</CardTitle>
    </CardHeader>
    <CardContent>
      {submittedFiles.map((file) => (
        <div key={file.id} className="border rounded-lg p-4 mb-4">
          <div className="flex justify-between items-center mb-3">
            <h4 className="font-medium">{file.name}</h4>
            <Badge variant={getQualityStatusVariant(file.quality_status)}>
              {file.quality_status}
            </Badge>
          </div>
          
          {file.annotations?.length > 0 && (
            <div>
              <p className="text-sm text-gray-600 mb-3">
                Quality team has provided visual feedback on this file:
              </p>
              <FileAnnotationTool
                fileUrl={file.url}
                fileName={file.name}
                fileType={file.type}
                existingAnnotations={file.annotations}
                readOnly={true} // Designers can only view, not edit
                reviewerName="Quality Team"
              />
            </div>
          )}
        </div>
      ))}
    </CardContent>
  </Card>
</div>
```

### **Phase 2: Quality Review File Association**
**File: `src/app/api/quality/annotations/route.ts`**

Enhance the API to link annotations with quality reviews:

```typescript
// Add quality_review_id to annotation creation
const annotation = await supabase
  .from('file_annotations')
  .insert({
    file_url: fileUrl,
    file_name: fileName,
    annotation_type: annotationType,
    x_position: xPosition,
    y_position: yPosition,
    title,
    description,
    reviewer_name: user.user_metadata?.full_name,
    quality_review_id: qualityReviewId, // NEW: Link to quality review
    created_at: new Date().toISOString()
  });
```

### **Phase 3: Enhanced Designer Quality Page**
**File: `src/app/designer/quality/page.tsx`**

Add "View Annotated Files" functionality:

```tsx
{review.status === 'needs_revision' && (
  <div className="flex gap-2 mt-3">
    <Button 
      variant="outline" 
      onClick={() => viewAnnotatedFiles(review.id)}
    >
      <FileImage className="h-4 w-4 mr-2" />
      View Annotated Files
    </Button>
    <Button 
      variant="default" 
      onClick={() => navigateToProject(review.project_id)}
    >
      Update Project
    </Button>
  </div>
)}
```

## 🔧 **Save Annotations Fix**

### **Current Issue:**
```tsx
const handleSave = () => {
  if (onSaveAnnotations) {
    onSaveAnnotations(annotations); // Only calls prop, doesn't save to DB
  }
};
```

### **Enhanced Save Functionality:**
```tsx
const handleSave = async () => {
  try {
    setSaving(true);
    
    // Option 1: If using the callback pattern
    if (onSaveAnnotations) {
      await onSaveAnnotations(annotations);
    }
    
    // Option 2: Direct API save (for standalone use)
    else {
      const headers = await getAuthHeaders();
      const response = await fetch('/api/quality/annotations/bulk-save', {
        method: 'POST',
        headers,
        body: JSON.stringify({
          annotations,
          fileUrl,
          fileName,
          qualityReviewId
        })
      });
      
      if (!response.ok) {
        throw new Error('Failed to save annotations');
      }
    }
    
    // Show success feedback
    setShowSuccessMessage(true);
    setTimeout(() => setShowSuccessMessage(false), 3000);
    
  } catch (error) {
    console.error('Error saving annotations:', error);
    setShowErrorMessage(true);
    setTimeout(() => setShowErrorMessage(false), 5000);
  } finally {
    setSaving(false);
  }
};
```

## 📊 **Database Schema Updates**

### **Enhanced file_annotations table:**
```sql
ALTER TABLE file_annotations 
ADD COLUMN quality_review_id UUID REFERENCES quality_reviews(id),
ADD COLUMN project_id UUID REFERENCES projects(id),
ADD COLUMN is_visible_to_designer BOOLEAN DEFAULT true;

-- Index for performance
CREATE INDEX idx_file_annotations_quality_review 
ON file_annotations(quality_review_id);

CREATE INDEX idx_file_annotations_project 
ON file_annotations(project_id);
```

## 🎯 **Expected Outcomes**

After implementation:

### **✅ Designers Will Be Able To:**
1. **View annotated images** in their project details
2. **See visual feedback** from quality team on specific areas
3. **Understand revision requests** with precise visual context
4. **Navigate from quality reviews** to annotated files
5. **Track annotation history** across project revisions

### **✅ Quality Team Benefits:**
1. **More effective communication** through visual feedback
2. **Reduced back-and-forth** with clearer guidance
3. **Better designer education** through visual examples
4. **Audit trail** of feedback provided

### **✅ Platform Benefits:**
1. **Improved designer satisfaction** through better feedback
2. **Faster revision cycles** with clearer requirements
3. **Higher quality deliverables** through better communication
4. **Reduced support burden** with self-service feedback viewing

## 📁 **Files to Modify**

### **New Files:**
- `src/app/api/quality/annotations/bulk-save/route.ts` - Bulk save endpoint
- `src/components/designer/AnnotatedFileViewer.tsx` - Designer-specific file viewer

### **Modified Files:**
- `src/app/designer/projects/[id]/page.tsx` - Add annotation viewing
- `src/app/designer/quality/page.tsx` - Add "View Files" buttons
- `src/components/quality/FileAnnotationTool.tsx` - Enhanced save functionality
- `migrations/enhance_file_annotations.sql` - Database updates

## 🚀 **Implementation Priority**

1. **HIGH**: Fix save annotations functionality (immediate impact)
2. **HIGH**: Add designer file viewing in project details (core feature)
3. **MEDIUM**: Enhance quality page with file links (nice to have)
4. **LOW**: Advanced features like annotation history (future enhancement)

This implementation will complete the quality feedback loop and provide designers with the visual context they need to make effective revisions.
# Designer Annotation Viewing Enhancement Plan

## 🎯 **Objective**
Enable designers to view their submitted images with quality team annotations after reviews are completed.

## 📋 **Current Gap Analysis**

### ❌ **What's Missing:**
1. **Designer Project View**: No image annotation viewing in `/designer/projects/[id]`
2. **Quality Review Integration**: No connection between quality reviews and annotated files
3. **File Accessibility**: Designers can't access quality-annotated versions of their files
4. **Visual Feedback**: Only text feedback shown, not visual markup

### ✅ **What Exists:**
1. **FileAnnotationTool Component**: Fully functional annotation system
2. **Annotation API**: Working CRUD operations for annotations
3. **Quality Review System**: Basic feedback display
4. **File Storage**: Cloudflare R2 integration

## 🔧 **Implementation Plan**

### **Phase 1: Designer Project File Viewing**
**File: `src/app/designer/projects/[id]/page.tsx`**

Add a new section to show submitted files with annotations:

```tsx
// Add to existing project detail page
<div className="space-y-6">
  {/* Existing project info */}
  
  {/* NEW: Submitted Files with Annotations */}
  <Card>
    <CardHeader>
      <CardTitle>Submitted Files & Quality Feedback</CardTitle>
    </CardHeader>
    <CardContent>
      {submittedFiles.map((file) => (
        <div key={file.id} className="border rounded-lg p-4 mb-4">
          <div className="flex justify-between items-center mb-3">
            <h4 className="font-medium">{file.name}</h4>
            <Badge variant={getQualityStatusVariant(file.quality_status)}>
              {file.quality_status}
            </Badge>
          </div>
          
          {file.annotations?.length > 0 && (
            <div>
              <p className="text-sm text-gray-600 mb-3">
                Quality team has provided visual feedback on this file:
              </p>
              <FileAnnotationTool
                fileUrl={file.url}
                fileName={file.name}
                fileType={file.type}
                existingAnnotations={file.annotations}
                readOnly={true} // Designers can only view, not edit
                reviewerName="Quality Team"
              />
            </div>
          )}
        </div>
      ))}
    </CardContent>
  </Card>
</div>
```

### **Phase 2: Quality Review File Association**
**File: `src/app/api/quality/annotations/route.ts`**

Enhance the API to link annotations with quality reviews:

```typescript
// Add quality_review_id to annotation creation
const annotation = await supabase
  .from('file_annotations')
  .insert({
    file_url: fileUrl,
    file_name: fileName,
    annotation_type: annotationType,
    x_position: xPosition,
    y_position: yPosition,
    title,
    description,
    reviewer_name: user.user_metadata?.full_name,
    quality_review_id: qualityReviewId, // NEW: Link to quality review
    created_at: new Date().toISOString()
  });
```

### **Phase 3: Enhanced Designer Quality Page**
**File: `src/app/designer/quality/page.tsx`**

Add "View Annotated Files" functionality:

```tsx
{review.status === 'needs_revision' && (
  <div className="flex gap-2 mt-3">
    <Button 
      variant="outline" 
      onClick={() => viewAnnotatedFiles(review.id)}
    >
      <FileImage className="h-4 w-4 mr-2" />
      View Annotated Files
    </Button>
    <Button 
      variant="default" 
      onClick={() => navigateToProject(review.project_id)}
    >
      Update Project
    </Button>
  </div>
)}
```

## 🔧 **Save Annotations Fix**

### **Current Issue:**
```tsx
const handleSave = () => {
  if (onSaveAnnotations) {
    onSaveAnnotations(annotations); // Only calls prop, doesn't save to DB
  }
};
```

### **Enhanced Save Functionality:**
```tsx
const handleSave = async () => {
  try {
    setSaving(true);
    
    // Option 1: If using the callback pattern
    if (onSaveAnnotations) {
      await onSaveAnnotations(annotations);
    }
    
    // Option 2: Direct API save (for standalone use)
    else {
      const headers = await getAuthHeaders();
      const response = await fetch('/api/quality/annotations/bulk-save', {
        method: 'POST',
        headers,
        body: JSON.stringify({
          annotations,
          fileUrl,
          fileName,
          qualityReviewId
        })
      });
      
      if (!response.ok) {
        throw new Error('Failed to save annotations');
      }
    }
    
    // Show success feedback
    setShowSuccessMessage(true);
    setTimeout(() => setShowSuccessMessage(false), 3000);
    
  } catch (error) {
    console.error('Error saving annotations:', error);
    setShowErrorMessage(true);
    setTimeout(() => setShowErrorMessage(false), 5000);
  } finally {
    setSaving(false);
  }
};
```

## 📊 **Database Schema Updates**

### **Enhanced file_annotations table:**
```sql
ALTER TABLE file_annotations 
ADD COLUMN quality_review_id UUID REFERENCES quality_reviews(id),
ADD COLUMN project_id UUID REFERENCES projects(id),
ADD COLUMN is_visible_to_designer BOOLEAN DEFAULT true;

-- Index for performance
CREATE INDEX idx_file_annotations_quality_review 
ON file_annotations(quality_review_id);

CREATE INDEX idx_file_annotations_project 
ON file_annotations(project_id);
```

## 🎯 **Expected Outcomes**

After implementation:

### **✅ Designers Will Be Able To:**
1. **View annotated images** in their project details
2. **See visual feedback** from quality team on specific areas
3. **Understand revision requests** with precise visual context
4. **Navigate from quality reviews** to annotated files
5. **Track annotation history** across project revisions

### **✅ Quality Team Benefits:**
1. **More effective communication** through visual feedback
2. **Reduced back-and-forth** with clearer guidance
3. **Better designer education** through visual examples
4. **Audit trail** of feedback provided

### **✅ Platform Benefits:**
1. **Improved designer satisfaction** through better feedback
2. **Faster revision cycles** with clearer requirements
3. **Higher quality deliverables** through better communication
4. **Reduced support burden** with self-service feedback viewing

## 📁 **Files to Modify**

### **New Files:**
- `src/app/api/quality/annotations/bulk-save/route.ts` - Bulk save endpoint
- `src/components/designer/AnnotatedFileViewer.tsx` - Designer-specific file viewer

### **Modified Files:**
- `src/app/designer/projects/[id]/page.tsx` - Add annotation viewing
- `src/app/designer/quality/page.tsx` - Add "View Files" buttons
- `src/components/quality/FileAnnotationTool.tsx` - Enhanced save functionality
- `migrations/enhance_file_annotations.sql` - Database updates

## 🚀 **Implementation Priority**

1. **HIGH**: Fix save annotations functionality (immediate impact)
2. **HIGH**: Add designer file viewing in project details (core feature)
3. **MEDIUM**: Enhance quality page with file links (nice to have)
4. **LOW**: Advanced features like annotation history (future enhancement)

This implementation will complete the quality feedback loop and provide designers with the visual context they need to make effective revisions.
# Designer Annotation Viewing Enhancement Plan

## 🎯 **Objective**
Enable designers to view their submitted images with quality team annotations after reviews are completed.

## 📋 **Current Gap Analysis**

### ❌ **What's Missing:**
1. **Designer Project View**: No image annotation viewing in `/designer/projects/[id]`
2. **Quality Review Integration**: No connection between quality reviews and annotated files
3. **File Accessibility**: Designers can't access quality-annotated versions of their files
4. **Visual Feedback**: Only text feedback shown, not visual markup

### ✅ **What Exists:**
1. **FileAnnotationTool Component**: Fully functional annotation system
2. **Annotation API**: Working CRUD operations for annotations
3. **Quality Review System**: Basic feedback display
4. **File Storage**: Cloudflare R2 integration

## 🔧 **Implementation Plan**

### **Phase 1: Designer Project File Viewing**
**File: `src/app/designer/projects/[id]/page.tsx`**

Add a new section to show submitted files with annotations:

```tsx
// Add to existing project detail page
<div className="space-y-6">
  {/* Existing project info */}
  
  {/* NEW: Submitted Files with Annotations */}
  <Card>
    <CardHeader>
      <CardTitle>Submitted Files & Quality Feedback</CardTitle>
    </CardHeader>
    <CardContent>
      {submittedFiles.map((file) => (
        <div key={file.id} className="border rounded-lg p-4 mb-4">
          <div className="flex justify-between items-center mb-3">
            <h4 className="font-medium">{file.name}</h4>
            <Badge variant={getQualityStatusVariant(file.quality_status)}>
              {file.quality_status}
            </Badge>
          </div>
          
          {file.annotations?.length > 0 && (
            <div>
              <p className="text-sm text-gray-600 mb-3">
                Quality team has provided visual feedback on this file:
              </p>
              <FileAnnotationTool
                fileUrl={file.url}
                fileName={file.name}
                fileType={file.type}
                existingAnnotations={file.annotations}
                readOnly={true} // Designers can only view, not edit
                reviewerName="Quality Team"
              />
            </div>
          )}
        </div>
      ))}
    </CardContent>
  </Card>
</div>
```

### **Phase 2: Quality Review File Association**
**File: `src/app/api/quality/annotations/route.ts`**

Enhance the API to link annotations with quality reviews:

```typescript
// Add quality_review_id to annotation creation
const annotation = await supabase
  .from('file_annotations')
  .insert({
    file_url: fileUrl,
    file_name: fileName,
    annotation_type: annotationType,
    x_position: xPosition,
    y_position: yPosition,
    title,
    description,
    reviewer_name: user.user_metadata?.full_name,
    quality_review_id: qualityReviewId, // NEW: Link to quality review
    created_at: new Date().toISOString()
  });
```

### **Phase 3: Enhanced Designer Quality Page**
**File: `src/app/designer/quality/page.tsx`**

Add "View Annotated Files" functionality:

```tsx
{review.status === 'needs_revision' && (
  <div className="flex gap-2 mt-3">
    <Button 
      variant="outline" 
      onClick={() => viewAnnotatedFiles(review.id)}
    >
      <FileImage className="h-4 w-4 mr-2" />
      View Annotated Files
    </Button>
    <Button 
      variant="default" 
      onClick={() => navigateToProject(review.project_id)}
    >
      Update Project
    </Button>
  </div>
)}
```

## 🔧 **Save Annotations Fix**

### **Current Issue:**
```tsx
const handleSave = () => {
  if (onSaveAnnotations) {
    onSaveAnnotations(annotations); // Only calls prop, doesn't save to DB
  }
};
```

### **Enhanced Save Functionality:**
```tsx
const handleSave = async () => {
  try {
    setSaving(true);
    
    // Option 1: If using the callback pattern
    if (onSaveAnnotations) {
      await onSaveAnnotations(annotations);
    }
    
    // Option 2: Direct API save (for standalone use)
    else {
      const headers = await getAuthHeaders();
      const response = await fetch('/api/quality/annotations/bulk-save', {
        method: 'POST',
        headers,
        body: JSON.stringify({
          annotations,
          fileUrl,
          fileName,
          qualityReviewId
        })
      });
      
      if (!response.ok) {
        throw new Error('Failed to save annotations');
      }
    }
    
    // Show success feedback
    setShowSuccessMessage(true);
    setTimeout(() => setShowSuccessMessage(false), 3000);
    
  } catch (error) {
    console.error('Error saving annotations:', error);
    setShowErrorMessage(true);
    setTimeout(() => setShowErrorMessage(false), 5000);
  } finally {
    setSaving(false);
  }
};
```

## 📊 **Database Schema Updates**

### **Enhanced file_annotations table:**
```sql
ALTER TABLE file_annotations 
ADD COLUMN quality_review_id UUID REFERENCES quality_reviews(id),
ADD COLUMN project_id UUID REFERENCES projects(id),
ADD COLUMN is_visible_to_designer BOOLEAN DEFAULT true;

-- Index for performance
CREATE INDEX idx_file_annotations_quality_review 
ON file_annotations(quality_review_id);

CREATE INDEX idx_file_annotations_project 
ON file_annotations(project_id);
```

## 🎯 **Expected Outcomes**

After implementation:

### **✅ Designers Will Be Able To:**
1. **View annotated images** in their project details
2. **See visual feedback** from quality team on specific areas
3. **Understand revision requests** with precise visual context
4. **Navigate from quality reviews** to annotated files
5. **Track annotation history** across project revisions

### **✅ Quality Team Benefits:**
1. **More effective communication** through visual feedback
2. **Reduced back-and-forth** with clearer guidance
3. **Better designer education** through visual examples
4. **Audit trail** of feedback provided

### **✅ Platform Benefits:**
1. **Improved designer satisfaction** through better feedback
2. **Faster revision cycles** with clearer requirements
3. **Higher quality deliverables** through better communication
4. **Reduced support burden** with self-service feedback viewing

## 📁 **Files to Modify**

### **New Files:**
- `src/app/api/quality/annotations/bulk-save/route.ts` - Bulk save endpoint
- `src/components/designer/AnnotatedFileViewer.tsx` - Designer-specific file viewer

### **Modified Files:**
- `src/app/designer/projects/[id]/page.tsx` - Add annotation viewing
- `src/app/designer/quality/page.tsx` - Add "View Files" buttons
- `src/components/quality/FileAnnotationTool.tsx` - Enhanced save functionality
- `migrations/enhance_file_annotations.sql` - Database updates

## 🚀 **Implementation Priority**

1. **HIGH**: Fix save annotations functionality (immediate impact)
2. **HIGH**: Add designer file viewing in project details (core feature)
3. **MEDIUM**: Enhance quality page with file links (nice to have)
4. **LOW**: Advanced features like annotation history (future enhancement)

This implementation will complete the quality feedback loop and provide designers with the visual context they need to make effective revisions.
# Designer Annotation Viewing Enhancement Plan

## 🎯 **Objective**
Enable designers to view their submitted images with quality team annotations after reviews are completed.

## 📋 **Current Gap Analysis**

### ❌ **What's Missing:**
1. **Designer Project View**: No image annotation viewing in `/designer/projects/[id]`
2. **Quality Review Integration**: No connection between quality reviews and annotated files
3. **File Accessibility**: Designers can't access quality-annotated versions of their files
4. **Visual Feedback**: Only text feedback shown, not visual markup

### ✅ **What Exists:**
1. **FileAnnotationTool Component**: Fully functional annotation system
2. **Annotation API**: Working CRUD operations for annotations
3. **Quality Review System**: Basic feedback display
4. **File Storage**: Cloudflare R2 integration

## 🔧 **Implementation Plan**

### **Phase 1: Designer Project File Viewing**
**File: `src/app/designer/projects/[id]/page.tsx`**

Add a new section to show submitted files with annotations:

```tsx
// Add to existing project detail page
<div className="space-y-6">
  {/* Existing project info */}
  
  {/* NEW: Submitted Files with Annotations */}
  <Card>
    <CardHeader>
      <CardTitle>Submitted Files & Quality Feedback</CardTitle>
    </CardHeader>
    <CardContent>
      {submittedFiles.map((file) => (
        <div key={file.id} className="border rounded-lg p-4 mb-4">
          <div className="flex justify-between items-center mb-3">
            <h4 className="font-medium">{file.name}</h4>
            <Badge variant={getQualityStatusVariant(file.quality_status)}>
              {file.quality_status}
            </Badge>
          </div>
          
          {file.annotations?.length > 0 && (
            <div>
              <p className="text-sm text-gray-600 mb-3">
                Quality team has provided visual feedback on this file:
              </p>
              <FileAnnotationTool
                fileUrl={file.url}
                fileName={file.name}
                fileType={file.type}
                existingAnnotations={file.annotations}
                readOnly={true} // Designers can only view, not edit
                reviewerName="Quality Team"
              />
            </div>
          )}
        </div>
      ))}
    </CardContent>
  </Card>
</div>
```

### **Phase 2: Quality Review File Association**
**File: `src/app/api/quality/annotations/route.ts`**

Enhance the API to link annotations with quality reviews:

```typescript
// Add quality_review_id to annotation creation
const annotation = await supabase
  .from('file_annotations')
  .insert({
    file_url: fileUrl,
    file_name: fileName,
    annotation_type: annotationType,
    x_position: xPosition,
    y_position: yPosition,
    title,
    description,
    reviewer_name: user.user_metadata?.full_name,
    quality_review_id: qualityReviewId, // NEW: Link to quality review
    created_at: new Date().toISOString()
  });
```

### **Phase 3: Enhanced Designer Quality Page**
**File: `src/app/designer/quality/page.tsx`**

Add "View Annotated Files" functionality:

```tsx
{review.status === 'needs_revision' && (
  <div className="flex gap-2 mt-3">
    <Button 
      variant="outline" 
      onClick={() => viewAnnotatedFiles(review.id)}
    >
      <FileImage className="h-4 w-4 mr-2" />
      View Annotated Files
    </Button>
    <Button 
      variant="default" 
      onClick={() => navigateToProject(review.project_id)}
    >
      Update Project
    </Button>
  </div>
)}
```

## 🔧 **Save Annotations Fix**

### **Current Issue:**
```tsx
const handleSave = () => {
  if (onSaveAnnotations) {
    onSaveAnnotations(annotations); // Only calls prop, doesn't save to DB
  }
};
```

### **Enhanced Save Functionality:**
```tsx
const handleSave = async () => {
  try {
    setSaving(true);
    
    // Option 1: If using the callback pattern
    if (onSaveAnnotations) {
      await onSaveAnnotations(annotations);
    }
    
    // Option 2: Direct API save (for standalone use)
    else {
      const headers = await getAuthHeaders();
      const response = await fetch('/api/quality/annotations/bulk-save', {
        method: 'POST',
        headers,
        body: JSON.stringify({
          annotations,
          fileUrl,
          fileName,
          qualityReviewId
        })
      });
      
      if (!response.ok) {
        throw new Error('Failed to save annotations');
      }
    }
    
    // Show success feedback
    setShowSuccessMessage(true);
    setTimeout(() => setShowSuccessMessage(false), 3000);
    
  } catch (error) {
    console.error('Error saving annotations:', error);
    setShowErrorMessage(true);
    setTimeout(() => setShowErrorMessage(false), 5000);
  } finally {
    setSaving(false);
  }
};
```

## 📊 **Database Schema Updates**

### **Enhanced file_annotations table:**
```sql
ALTER TABLE file_annotations 
ADD COLUMN quality_review_id UUID REFERENCES quality_reviews(id),
ADD COLUMN project_id UUID REFERENCES projects(id),
ADD COLUMN is_visible_to_designer BOOLEAN DEFAULT true;

-- Index for performance
CREATE INDEX idx_file_annotations_quality_review 
ON file_annotations(quality_review_id);

CREATE INDEX idx_file_annotations_project 
ON file_annotations(project_id);
```

## 🎯 **Expected Outcomes**

After implementation:

### **✅ Designers Will Be Able To:**
1. **View annotated images** in their project details
2. **See visual feedback** from quality team on specific areas
3. **Understand revision requests** with precise visual context
4. **Navigate from quality reviews** to annotated files
5. **Track annotation history** across project revisions

### **✅ Quality Team Benefits:**
1. **More effective communication** through visual feedback
2. **Reduced back-and-forth** with clearer guidance
3. **Better designer education** through visual examples
4. **Audit trail** of feedback provided

### **✅ Platform Benefits:**
1. **Improved designer satisfaction** through better feedback
2. **Faster revision cycles** with clearer requirements
3. **Higher quality deliverables** through better communication
4. **Reduced support burden** with self-service feedback viewing

## 📁 **Files to Modify**

### **New Files:**
- `src/app/api/quality/annotations/bulk-save/route.ts` - Bulk save endpoint
- `src/components/designer/AnnotatedFileViewer.tsx` - Designer-specific file viewer

### **Modified Files:**
- `src/app/designer/projects/[id]/page.tsx` - Add annotation viewing
- `src/app/designer/quality/page.tsx` - Add "View Files" buttons
- `src/components/quality/FileAnnotationTool.tsx` - Enhanced save functionality
- `migrations/enhance_file_annotations.sql` - Database updates

## 🚀 **Implementation Priority**

1. **HIGH**: Fix save annotations functionality (immediate impact)
2. **HIGH**: Add designer file viewing in project details (core feature)
3. **MEDIUM**: Enhance quality page with file links (nice to have)
4. **LOW**: Advanced features like annotation history (future enhancement)

This implementation will complete the quality feedback loop and provide designers with the visual context they need to make effective revisions.
# Designer Annotation Viewing Enhancement Plan

## 🎯 **Objective**
Enable designers to view their submitted images with quality team annotations after reviews are completed.

## 📋 **Current Gap Analysis**

### ❌ **What's Missing:**
1. **Designer Project View**: No image annotation viewing in `/designer/projects/[id]`
2. **Quality Review Integration**: No connection between quality reviews and annotated files
3. **File Accessibility**: Designers can't access quality-annotated versions of their files
4. **Visual Feedback**: Only text feedback shown, not visual markup

### ✅ **What Exists:**
1. **FileAnnotationTool Component**: Fully functional annotation system
2. **Annotation API**: Working CRUD operations for annotations
3. **Quality Review System**: Basic feedback display
4. **File Storage**: Cloudflare R2 integration

## 🔧 **Implementation Plan**

### **Phase 1: Designer Project File Viewing**
**File: `src/app/designer/projects/[id]/page.tsx`**

Add a new section to show submitted files with annotations:

```tsx
// Add to existing project detail page
<div className="space-y-6">
  {/* Existing project info */}
  
  {/* NEW: Submitted Files with Annotations */}
  <Card>
    <CardHeader>
      <CardTitle>Submitted Files & Quality Feedback</CardTitle>
    </CardHeader>
    <CardContent>
      {submittedFiles.map((file) => (
        <div key={file.id} className="border rounded-lg p-4 mb-4">
          <div className="flex justify-between items-center mb-3">
            <h4 className="font-medium">{file.name}</h4>
            <Badge variant={getQualityStatusVariant(file.quality_status)}>
              {file.quality_status}
            </Badge>
          </div>
          
          {file.annotations?.length > 0 && (
            <div>
              <p className="text-sm text-gray-600 mb-3">
                Quality team has provided visual feedback on this file:
              </p>
              <FileAnnotationTool
                fileUrl={file.url}
                fileName={file.name}
                fileType={file.type}
                existingAnnotations={file.annotations}
                readOnly={true} // Designers can only view, not edit
                reviewerName="Quality Team"
              />
            </div>
          )}
        </div>
      ))}
    </CardContent>
  </Card>
</div>
```

### **Phase 2: Quality Review File Association**
**File: `src/app/api/quality/annotations/route.ts`**

Enhance the API to link annotations with quality reviews:

```typescript
// Add quality_review_id to annotation creation
const annotation = await supabase
  .from('file_annotations')
  .insert({
    file_url: fileUrl,
    file_name: fileName,
    annotation_type: annotationType,
    x_position: xPosition,
    y_position: yPosition,
    title,
    description,
    reviewer_name: user.user_metadata?.full_name,
    quality_review_id: qualityReviewId, // NEW: Link to quality review
    created_at: new Date().toISOString()
  });
```

### **Phase 3: Enhanced Designer Quality Page**
**File: `src/app/designer/quality/page.tsx`**

Add "View Annotated Files" functionality:

```tsx
{review.status === 'needs_revision' && (
  <div className="flex gap-2 mt-3">
    <Button 
      variant="outline" 
      onClick={() => viewAnnotatedFiles(review.id)}
    >
      <FileImage className="h-4 w-4 mr-2" />
      View Annotated Files
    </Button>
    <Button 
      variant="default" 
      onClick={() => navigateToProject(review.project_id)}
    >
      Update Project
    </Button>
  </div>
)}
```

## 🔧 **Save Annotations Fix**

### **Current Issue:**
```tsx
const handleSave = () => {
  if (onSaveAnnotations) {
    onSaveAnnotations(annotations); // Only calls prop, doesn't save to DB
  }
};
```

### **Enhanced Save Functionality:**
```tsx
const handleSave = async () => {
  try {
    setSaving(true);
    
    // Option 1: If using the callback pattern
    if (onSaveAnnotations) {
      await onSaveAnnotations(annotations);
    }
    
    // Option 2: Direct API save (for standalone use)
    else {
      const headers = await getAuthHeaders();
      const response = await fetch('/api/quality/annotations/bulk-save', {
        method: 'POST',
        headers,
        body: JSON.stringify({
          annotations,
          fileUrl,
          fileName,
          qualityReviewId
        })
      });
      
      if (!response.ok) {
        throw new Error('Failed to save annotations');
      }
    }
    
    // Show success feedback
    setShowSuccessMessage(true);
    setTimeout(() => setShowSuccessMessage(false), 3000);
    
  } catch (error) {
    console.error('Error saving annotations:', error);
    setShowErrorMessage(true);
    setTimeout(() => setShowErrorMessage(false), 5000);
  } finally {
    setSaving(false);
  }
};
```

## 📊 **Database Schema Updates**

### **Enhanced file_annotations table:**
```sql
ALTER TABLE file_annotations 
ADD COLUMN quality_review_id UUID REFERENCES quality_reviews(id),
ADD COLUMN project_id UUID REFERENCES projects(id),
ADD COLUMN is_visible_to_designer BOOLEAN DEFAULT true;

-- Index for performance
CREATE INDEX idx_file_annotations_quality_review 
ON file_annotations(quality_review_id);

CREATE INDEX idx_file_annotations_project 
ON file_annotations(project_id);
```

## 🎯 **Expected Outcomes**

After implementation:

### **✅ Designers Will Be Able To:**
1. **View annotated images** in their project details
2. **See visual feedback** from quality team on specific areas
3. **Understand revision requests** with precise visual context
4. **Navigate from quality reviews** to annotated files
5. **Track annotation history** across project revisions

### **✅ Quality Team Benefits:**
1. **More effective communication** through visual feedback
2. **Reduced back-and-forth** with clearer guidance
3. **Better designer education** through visual examples
4. **Audit trail** of feedback provided

### **✅ Platform Benefits:**
1. **Improved designer satisfaction** through better feedback
2. **Faster revision cycles** with clearer requirements
3. **Higher quality deliverables** through better communication
4. **Reduced support burden** with self-service feedback viewing

## 📁 **Files to Modify**

### **New Files:**
- `src/app/api/quality/annotations/bulk-save/route.ts` - Bulk save endpoint
- `src/components/designer/AnnotatedFileViewer.tsx` - Designer-specific file viewer

### **Modified Files:**
- `src/app/designer/projects/[id]/page.tsx` - Add annotation viewing
- `src/app/designer/quality/page.tsx` - Add "View Files" buttons
- `src/components/quality/FileAnnotationTool.tsx` - Enhanced save functionality
- `migrations/enhance_file_annotations.sql` - Database updates

## 🚀 **Implementation Priority**

1. **HIGH**: Fix save annotations functionality (immediate impact)
2. **HIGH**: Add designer file viewing in project details (core feature)
3. **MEDIUM**: Enhance quality page with file links (nice to have)
4. **LOW**: Advanced features like annotation history (future enhancement)

This implementation will complete the quality feedback loop and provide designers with the visual context they need to make effective revisions.
# Designer Annotation Viewing Enhancement Plan

## 🎯 **Objective**
Enable designers to view their submitted images with quality team annotations after reviews are completed.

## 📋 **Current Gap Analysis**

### ❌ **What's Missing:**
1. **Designer Project View**: No image annotation viewing in `/designer/projects/[id]`
2. **Quality Review Integration**: No connection between quality reviews and annotated files
3. **File Accessibility**: Designers can't access quality-annotated versions of their files
4. **Visual Feedback**: Only text feedback shown, not visual markup

### ✅ **What Exists:**
1. **FileAnnotationTool Component**: Fully functional annotation system
2. **Annotation API**: Working CRUD operations for annotations
3. **Quality Review System**: Basic feedback display
4. **File Storage**: Cloudflare R2 integration

## 🔧 **Implementation Plan**

### **Phase 1: Designer Project File Viewing**
**File: `src/app/designer/projects/[id]/page.tsx`**

Add a new section to show submitted files with annotations:

```tsx
// Add to existing project detail page
<div className="space-y-6">
  {/* Existing project info */}
  
  {/* NEW: Submitted Files with Annotations */}
  <Card>
    <CardHeader>
      <CardTitle>Submitted Files & Quality Feedback</CardTitle>
    </CardHeader>
    <CardContent>
      {submittedFiles.map((file) => (
        <div key={file.id} className="border rounded-lg p-4 mb-4">
          <div className="flex justify-between items-center mb-3">
            <h4 className="font-medium">{file.name}</h4>
            <Badge variant={getQualityStatusVariant(file.quality_status)}>
              {file.quality_status}
            </Badge>
          </div>
          
          {file.annotations?.length > 0 && (
            <div>
              <p className="text-sm text-gray-600 mb-3">
                Quality team has provided visual feedback on this file:
              </p>
              <FileAnnotationTool
                fileUrl={file.url}
                fileName={file.name}
                fileType={file.type}
                existingAnnotations={file.annotations}
                readOnly={true} // Designers can only view, not edit
                reviewerName="Quality Team"
              />
            </div>
          )}
        </div>
      ))}
    </CardContent>
  </Card>
</div>
```

### **Phase 2: Quality Review File Association**
**File: `src/app/api/quality/annotations/route.ts`**

Enhance the API to link annotations with quality reviews:

```typescript
// Add quality_review_id to annotation creation
const annotation = await supabase
  .from('file_annotations')
  .insert({
    file_url: fileUrl,
    file_name: fileName,
    annotation_type: annotationType,
    x_position: xPosition,
    y_position: yPosition,
    title,
    description,
    reviewer_name: user.user_metadata?.full_name,
    quality_review_id: qualityReviewId, // NEW: Link to quality review
    created_at: new Date().toISOString()
  });
```

### **Phase 3: Enhanced Designer Quality Page**
**File: `src/app/designer/quality/page.tsx`**

Add "View Annotated Files" functionality:

```tsx
{review.status === 'needs_revision' && (
  <div className="flex gap-2 mt-3">
    <Button 
      variant="outline" 
      onClick={() => viewAnnotatedFiles(review.id)}
    >
      <FileImage className="h-4 w-4 mr-2" />
      View Annotated Files
    </Button>
    <Button 
      variant="default" 
      onClick={() => navigateToProject(review.project_id)}
    >
      Update Project
    </Button>
  </div>
)}
```

## 🔧 **Save Annotations Fix**

### **Current Issue:**
```tsx
const handleSave = () => {
  if (onSaveAnnotations) {
    onSaveAnnotations(annotations); // Only calls prop, doesn't save to DB
  }
};
```

### **Enhanced Save Functionality:**
```tsx
const handleSave = async () => {
  try {
    setSaving(true);
    
    // Option 1: If using the callback pattern
    if (onSaveAnnotations) {
      await onSaveAnnotations(annotations);
    }
    
    // Option 2: Direct API save (for standalone use)
    else {
      const headers = await getAuthHeaders();
      const response = await fetch('/api/quality/annotations/bulk-save', {
        method: 'POST',
        headers,
        body: JSON.stringify({
          annotations,
          fileUrl,
          fileName,
          qualityReviewId
        })
      });
      
      if (!response.ok) {
        throw new Error('Failed to save annotations');
      }
    }
    
    // Show success feedback
    setShowSuccessMessage(true);
    setTimeout(() => setShowSuccessMessage(false), 3000);
    
  } catch (error) {
    console.error('Error saving annotations:', error);
    setShowErrorMessage(true);
    setTimeout(() => setShowErrorMessage(false), 5000);
  } finally {
    setSaving(false);
  }
};
```

## 📊 **Database Schema Updates**

### **Enhanced file_annotations table:**
```sql
ALTER TABLE file_annotations 
ADD COLUMN quality_review_id UUID REFERENCES quality_reviews(id),
ADD COLUMN project_id UUID REFERENCES projects(id),
ADD COLUMN is_visible_to_designer BOOLEAN DEFAULT true;

-- Index for performance
CREATE INDEX idx_file_annotations_quality_review 
ON file_annotations(quality_review_id);

CREATE INDEX idx_file_annotations_project 
ON file_annotations(project_id);
```

## 🎯 **Expected Outcomes**

After implementation:

### **✅ Designers Will Be Able To:**
1. **View annotated images** in their project details
2. **See visual feedback** from quality team on specific areas
3. **Understand revision requests** with precise visual context
4. **Navigate from quality reviews** to annotated files
5. **Track annotation history** across project revisions

### **✅ Quality Team Benefits:**
1. **More effective communication** through visual feedback
2. **Reduced back-and-forth** with clearer guidance
3. **Better designer education** through visual examples
4. **Audit trail** of feedback provided

### **✅ Platform Benefits:**
1. **Improved designer satisfaction** through better feedback
2. **Faster revision cycles** with clearer requirements
3. **Higher quality deliverables** through better communication
4. **Reduced support burden** with self-service feedback viewing

## 📁 **Files to Modify**

### **New Files:**
- `src/app/api/quality/annotations/bulk-save/route.ts` - Bulk save endpoint
- `src/components/designer/AnnotatedFileViewer.tsx` - Designer-specific file viewer

### **Modified Files:**
- `src/app/designer/projects/[id]/page.tsx` - Add annotation viewing
- `src/app/designer/quality/page.tsx` - Add "View Files" buttons
- `src/components/quality/FileAnnotationTool.tsx` - Enhanced save functionality
- `migrations/enhance_file_annotations.sql` - Database updates

## 🚀 **Implementation Priority**

1. **HIGH**: Fix save annotations functionality (immediate impact)
2. **HIGH**: Add designer file viewing in project details (core feature)
3. **MEDIUM**: Enhance quality page with file links (nice to have)
4. **LOW**: Advanced features like annotation history (future enhancement)

This implementation will complete the quality feedback loop and provide designers with the visual context they need to make effective revisions.
-- Enhance file_annotations table for better designer-quality team integration
-- This migration adds columns to link annotations with quality reviews and projects

-- Add new columns if they don't exist
DO $$
BEGIN
    -- Add quality_review_id column if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'file_annotations' AND column_name = 'quality_review_id') THEN
        ALTER TABLE file_annotations 
        ADD COLUMN quality_review_id UUID REFERENCES quality_reviews(id);
    END IF;

    -- Add project_id column if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'file_annotations' AND column_name = 'project_id') THEN
        ALTER TABLE file_annotations 
        ADD COLUMN project_id UUID REFERENCES projects(id);
    END IF;

    -- Add is_visible_to_designer column if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'file_annotations' AND column_name = 'is_visible_to_designer') THEN
        ALTER TABLE file_annotations 
        ADD COLUMN is_visible_to_designer BOOLEAN DEFAULT true;
    END IF;

    -- Add submission_id column if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'file_annotations' AND column_name = 'submission_id') THEN
        ALTER TABLE file_annotations 
        ADD COLUMN submission_id UUID;
    END IF;

    -- Add submission_type column if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'file_annotations' AND column_name = 'submission_type') THEN
        ALTER TABLE file_annotations 
        ADD COLUMN submission_type VARCHAR(50) DEFAULT 'project';
    END IF;
END $$;

-- Create indexes for better performance if they don't exist
CREATE INDEX IF NOT EXISTS idx_file_annotations_quality_review 
ON file_annotations(quality_review_id);

CREATE INDEX IF NOT EXISTS idx_file_annotations_project 
ON file_annotations(project_id);

CREATE INDEX IF NOT EXISTS idx_file_annotations_submission 
ON file_annotations(submission_id);

CREATE INDEX IF NOT EXISTS idx_file_annotations_file_url 
ON file_annotations(file_url);

-- Update RLS policies for designer access
-- Allow designers to view annotations on their own project files
DO $$
BEGIN
    -- Drop existing policy if it exists
    DROP POLICY IF EXISTS "Designers can view annotations on their projects" ON file_annotations;
    
    -- Create new policy for designer access
    CREATE POLICY "Designers can view annotations on their projects" ON file_annotations
        FOR SELECT USING (
            is_visible_to_designer = true AND (
                -- Direct project access
                project_id IN (
                    SELECT id FROM projects WHERE designer_id = auth.uid()
                ) OR
                -- Through quality reviews
                quality_review_id IN (
                    SELECT id FROM quality_reviews WHERE designer_id = auth.uid()
                ) OR
                -- Through submission type
                submission_type = 'project' AND EXISTS (
                    SELECT 1 FROM projects p 
                    JOIN quality_reviews qr ON qr.project_id = p.id
                    WHERE p.designer_id = auth.uid() 
                    AND qr.id = file_annotations.quality_review_id
                )
            )
        );
END $$;

-- Add helpful comments
COMMENT ON COLUMN file_annotations.quality_review_id IS 'Links annotation to a specific quality review';
COMMENT ON COLUMN file_annotations.project_id IS 'Links annotation to a specific project';
COMMENT ON COLUMN file_annotations.is_visible_to_designer IS 'Controls whether designers can see this annotation';
COMMENT ON COLUMN file_annotations.submission_id IS 'Links annotation to a specific submission';
COMMENT ON COLUMN file_annotations.submission_type IS 'Type of submission: project, work, vision, etc.';

-- Create a view for easier designer annotation access
CREATE OR REPLACE VIEW designer_annotated_files AS
SELECT DISTINCT
    fa.file_url,
    fa.file_name,
    fa.file_type,
    fa.project_id,
    qr.status as quality_status,
    qr.feedback as quality_feedback,
    qr.designer_id,
    COUNT(fa.id) as annotation_count,
    MAX(fa.created_at) as last_annotation_date
FROM file_annotations fa
LEFT JOIN quality_reviews qr ON fa.quality_review_id = qr.id
WHERE fa.is_visible_to_designer = true
GROUP BY fa.file_url, fa.file_name, fa.file_type, fa.project_id, qr.status, qr.feedback, qr.designer_id;

-- Grant access to the view
GRANT SELECT ON designer_annotated_files TO authenticated;

-- Add RLS to the view
ALTER VIEW designer_annotated_files SET (security_invoker = true);

-- Create a function to get annotations for a specific file
CREATE OR REPLACE FUNCTION get_file_annotations(file_url_param TEXT)
RETURNS TABLE (
    id UUID,
    x_position NUMERIC,
    y_position NUMERIC,
    width NUMERIC,
    height NUMERIC,
    annotation_type VARCHAR,
    title TEXT,
    description TEXT,
    reviewer_name VARCHAR,
    created_at TIMESTAMPTZ
) 
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
    RETURN QUERY
    SELECT 
        fa.id,
        fa.x_position,
        fa.y_position,
        fa.width,
        fa.height,
        fa.annotation_type,
        fa.title,
        fa.description,
        fa.reviewer_name,
        fa.created_at
    FROM file_annotations fa
    WHERE fa.file_url = file_url_param
    AND fa.is_visible_to_designer = true
    ORDER BY fa.created_at DESC;
END;
$$;

-- Grant execute permission on the function
GRANT EXECUTE ON FUNCTION get_file_annotations(TEXT) TO authenticated;
