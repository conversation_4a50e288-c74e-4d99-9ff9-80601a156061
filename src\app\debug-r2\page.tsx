"use client";

import { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { CheckCircle, XCircle, AlertTriangle } from "lucide-react";

export default function DebugR2Page() {
  const [envVars, setEnvVars] = useState<any>(null);

  const checkEnvironmentVariables = () => {
    // Check your specific R2 environment variable setup
    const vars = {
      // Your current R2 configuration
      CLOUDFLARE_R2_ENDPOINT: process.env.CLOUDFLARE_R2_ENDPOINT,
      CLOUDFLARE_R2_ACCESS_KEY_ID: process.env.CLOUDFLARE_R2_ACCESS_KEY_ID,
      CLOUDFLARE_R2_SECRET_ACCESS_KEY: process.env.CLOUDFLARE_R2_SECRET_ACCESS_KEY,
      CLOUDFLARE_R2_PUBLIC_URL: process.env.CLOUDFLARE_R2_PUBLIC_URL,
    };

    setEnvVars(vars);
  };

  const renderStatus = (value: string | undefined, isSecret = false) => {
    if (value) {
      return (
        <div className="flex items-center gap-2">
          <CheckCircle className="h-4 w-4 text-green-600" />
          <span className="text-green-700">
            {isSecret ? `${value.slice(0, 8)}...` : value}
          </span>
        </div>
      );
    } else {
      return (
        <div className="flex items-center gap-2">
          <XCircle className="h-4 w-4 text-red-600" />
          <span className="text-red-700">Not set</span>
        </div>
      );
    }
  };

  return (
    <div className="max-w-4xl mx-auto p-6 space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">R2 Configuration Debug</h1>
          <p className="text-gray-600 mt-2">Check Cloudflare R2 environment variables</p>
        </div>
        <Button onClick={checkEnvironmentVariables}>
          Check Environment Variables
        </Button>
      </div>

      {envVars && (
        <Card>
          <CardHeader>
            <CardTitle>Your R2 Configuration Status</CardTitle>
            <p className="text-sm text-gray-600">Current environment variables for Cloudflare R2</p>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <strong>CLOUDFLARE_R2_ENDPOINT:</strong>
              {renderStatus(envVars.CLOUDFLARE_R2_ENDPOINT)}
            </div>
            <div>
              <strong>CLOUDFLARE_R2_ACCESS_KEY_ID:</strong>
              {renderStatus(envVars.CLOUDFLARE_R2_ACCESS_KEY_ID, true)}
            </div>
            <div>
              <strong>CLOUDFLARE_R2_SECRET_ACCESS_KEY:</strong>
              {renderStatus(envVars.CLOUDFLARE_R2_SECRET_ACCESS_KEY, true)}
            </div>
            <div>
              <strong>CLOUDFLARE_R2_PUBLIC_URL:</strong>
              {renderStatus(envVars.CLOUDFLARE_R2_PUBLIC_URL)}
            </div>

            {envVars.CLOUDFLARE_R2_ENDPOINT && envVars.CLOUDFLARE_R2_ACCESS_KEY_ID && envVars.CLOUDFLARE_R2_SECRET_ACCESS_KEY ? (
              <div className="bg-green-50 border border-green-200 p-4 rounded mt-4">
                <div className="flex items-center gap-2">
                  <CheckCircle className="h-5 w-5 text-green-600" />
                  <span className="font-semibold text-green-900">Configuration Complete!</span>
                </div>
                <p className="text-green-800 text-sm mt-1">
                  All required R2 environment variables are set. File uploads should work correctly.
                </p>
              </div>
            ) : (
              <div className="bg-red-50 border border-red-200 p-4 rounded mt-4">
                <div className="flex items-center gap-2">
                  <XCircle className="h-5 w-5 text-red-600" />
                  <span className="font-semibold text-red-900">Configuration Incomplete</span>
                </div>
                <p className="text-red-800 text-sm mt-1">
                  Some required environment variables are missing. Check your .env.local file.
                </p>
              </div>
            )}
          </CardContent>
        </Card>
      )}

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <AlertTriangle className="h-5 w-5 text-yellow-600" />
            Setup Instructions
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="bg-yellow-50 border border-yellow-200 p-4 rounded">
            <h4 className="font-semibold text-yellow-900 mb-2">Required Environment Variables:</h4>
            <div className="text-sm text-yellow-800 space-y-1">
              <p><strong>Option 1:</strong> Set server-side variables (recommended for security)</p>
              <ul className="list-disc list-inside ml-4 space-y-1">
                <li><code>CLOUDFLARE_ACCOUNT_ID</code> - Your Cloudflare account ID</li>
                <li><code>CLOUDFLARE_R2_ACCESS_KEY_ID</code> - R2 API token access key</li>
                <li><code>CLOUDFLARE_R2_SECRET_ACCESS_KEY</code> - R2 API token secret</li>
              </ul>
              
              <p className="mt-3"><strong>Option 2:</strong> Set client-side variables (less secure)</p>
              <ul className="list-disc list-inside ml-4 space-y-1">
                <li><code>NEXT_PUBLIC_CLOUDFLARE_ACCOUNT_ID</code></li>
                <li><code>NEXT_PUBLIC_CLOUDFLARE_R2_ACCESS_KEY_ID</code></li>
                <li><code>NEXT_PUBLIC_CLOUDFLARE_R2_SECRET_ACCESS_KEY</code></li>
              </ul>
            </div>
          </div>

          <div className="bg-blue-50 border border-blue-200 p-4 rounded">
            <h4 className="font-semibold text-blue-900 mb-2">How to get these values:</h4>
            <div className="text-sm text-blue-800 space-y-1">
              <p><strong>Account ID:</strong> Cloudflare Dashboard → Right sidebar → Account ID</p>
              <p><strong>R2 API Token:</strong> Cloudflare Dashboard → R2 → Manage R2 API tokens → Create token</p>
              <p><strong>Permissions needed:</strong> Object Read & Write for your R2 buckets</p>
            </div>
          </div>

          <div className="bg-green-50 border border-green-200 p-4 rounded">
            <h4 className="font-semibold text-green-900 mb-2">Add to your .env.local file:</h4>
            <pre className="text-sm text-green-800 bg-green-100 p-2 rounded mt-2">
{`CLOUDFLARE_ACCOUNT_ID=your_account_id_here
CLOUDFLARE_R2_ACCESS_KEY_ID=your_access_key_here
CLOUDFLARE_R2_SECRET_ACCESS_KEY=your_secret_key_here`}
            </pre>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
