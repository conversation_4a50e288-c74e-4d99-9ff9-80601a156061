-- Fix the problematic parts of triggers while keeping automatic functionality
-- This addresses both the priority constraint and project_type column issues

-- ==============================================
-- PART 1: Fix Priority Constraint Issue
-- ==============================================

-- Drop the old trigger that uses 'medium' priority
DROP TRIGGER IF EXISTS trigger_create_quality_review ON project_submissions;
DROP TRIGGER IF EXISTS trigger_create_quality_assignment ON project_submissions;
DROP TRIGGER IF EXISTS trigger_auto_assign_quality_review ON project_submissions;

-- Fix existing records with invalid priority
UPDATE quality_reviews_new 
SET priority = 'normal' 
WHERE priority = 'medium' OR priority NOT IN ('low', 'normal', 'high', 'urgent');

-- Create a FIXED trigger function that uses 'normal' priority
CREATE OR REPLACE FUNCTION create_quality_review_on_submission_fixed()
RETURNS TRIGGER AS $$
BEGIN
    -- Only create quality review for submitted status
    IF NEW.status = 'submitted' AND (OLD.status IS NULL OR OLD.status != 'submitted') THEN
        INSERT INTO quality_reviews_new (
            project_id,
            submission_id,
            milestone_id,
            designer_id,
            review_type,
            status,
            priority,
            created_at,
            updated_at
        ) VALUES (
            NEW.project_id,
            NEW.id,
            NEW.milestone_id,
            NEW.designer_id,
            'submission',
            'pending',
            'normal',  -- FIXED: Use 'normal' instead of 'medium'
            NOW(),
            NOW()
        );
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Recreate the trigger with the FIXED function
CREATE TRIGGER trigger_create_quality_review_fixed
    AFTER INSERT OR UPDATE ON project_submissions
    FOR EACH ROW
    EXECUTE FUNCTION create_quality_review_on_submission_fixed();

-- ==============================================
-- PART 2: Fix Project Type Column Issue
-- ==============================================

-- Drop the problematic quality standards trigger
DROP TRIGGER IF EXISTS assign_quality_standards_trigger ON quality_reviews_new;

-- Create a FIXED version that handles missing project_type column gracefully
CREATE OR REPLACE FUNCTION trigger_assign_quality_standards_fixed()
RETURNS TRIGGER AS $$
DECLARE
    v_project_type VARCHAR(100) := 'general'; -- Default value
    v_project_exists BOOLEAN := FALSE;
    v_column_exists BOOLEAN := FALSE;
BEGIN
    -- Check if project_type column exists in projects table
    SELECT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'projects' AND column_name = 'project_type'
    ) INTO v_column_exists;
    
    -- Check if project exists
    SELECT EXISTS (
        SELECT 1 FROM projects WHERE id = NEW.project_id
    ) INTO v_project_exists;
    
    IF v_project_exists THEN
        IF v_column_exists THEN
            -- Project_type column exists, get the value
            SELECT COALESCE(project_type, 'general') INTO v_project_type
            FROM projects
            WHERE id = NEW.project_id;
        ELSE
            -- Project_type column doesn't exist, use default
            v_project_type := 'general';
        END IF;
    ELSE
        -- Project doesn't exist, use default
        v_project_type := 'general';
    END IF;
    
    -- Only try to assign standards if the function exists
    BEGIN
        PERFORM assign_quality_standards_to_review(NEW.id, v_project_type);
    EXCEPTION
        WHEN undefined_function THEN
            -- Function doesn't exist, just continue without error
            NULL;
        WHEN OTHERS THEN
            -- Log the error but don't fail the trigger
            RAISE NOTICE 'Error in assign_quality_standards_to_review: %', SQLERRM;
    END;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Recreate the trigger with the FIXED function
CREATE TRIGGER assign_quality_standards_trigger_fixed
    AFTER INSERT ON quality_reviews_new
    FOR EACH ROW
    EXECUTE FUNCTION trigger_assign_quality_standards_fixed();

-- ==============================================
-- PART 3: Optional - Add project_type column to projects table
-- ==============================================

-- Uncomment the following if you want to add the project_type column to projects table
-- This will make the quality standards assignment more accurate

/*
-- Add project_type column if it doesn't exist
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'projects' AND column_name = 'project_type'
    ) THEN
        ALTER TABLE projects ADD COLUMN project_type VARCHAR(100) DEFAULT 'general';
        
        -- Update existing projects with appropriate project types based on existing data
        UPDATE projects SET project_type = 'residential_interior' WHERE project_type IS NULL;
        
        RAISE NOTICE 'Added project_type column to projects table';
    END IF;
END $$;
*/

-- ==============================================
-- PART 4: Test the fixes
-- ==============================================

-- Test 1: Verify priority constraint works
DO $$
BEGIN
    -- This should fail with constraint violation
    INSERT INTO quality_reviews_new (priority) VALUES ('medium');
    RAISE NOTICE 'ERROR: Invalid priority was accepted!';
EXCEPTION
    WHEN check_violation THEN
        RAISE NOTICE 'SUCCESS: Priority constraint working correctly';
    WHEN OTHERS THEN
        RAISE NOTICE 'UNEXPECTED ERROR: %', SQLERRM;
END $$;

-- Test 2: Check current priority distribution
SELECT 'Current priority distribution:' as info;
SELECT priority, COUNT(*) as count
FROM quality_reviews_new 
GROUP BY priority
ORDER BY priority;

-- Test 3: Check if triggers are active
SELECT 'Active triggers on project_submissions:' as info;
SELECT triggername, proname as function_name
FROM pg_trigger t
JOIN pg_proc p ON t.tgfoid = p.oid
JOIN pg_class c ON t.tgrelid = c.oid
WHERE c.relname = 'project_submissions' AND NOT t.tgisinternal;

SELECT 'Active triggers on quality_reviews_new:' as info;
SELECT triggername, proname as function_name
FROM pg_trigger t
JOIN pg_proc p ON t.tgfoid = p.oid
JOIN pg_class c ON t.tgrelid = c.oid
WHERE c.relname = 'quality_reviews_new' AND NOT t.tgisinternal;

SELECT '✅ Trigger fixes completed - automatic functionality preserved!' as status;
