-- =====================================================
-- FIX: INSPIRATION BOARDS RLS POLICY
-- =====================================================

-- Drop any existing incorrect policies for inspiration_boards
DROP POLICY IF EXISTS "Users can view their inspiration boards" ON inspiration_boards;
DROP POLICY IF EXISTS "Clients can manage their inspiration boards" ON inspiration_boards;
DROP POLICY IF EXISTS "Users can access their project inspiration boards" ON inspiration_boards;

-- Enable RLS on inspiration_boards if not already enabled
ALTER TABLE inspiration_boards ENABLE ROW LEVEL SECURITY;

-- Create correct policy for inspiration_boards
-- Users can only access inspiration boards for projects they own or are assigned to
CREATE POLICY "Users can access their project inspiration boards" ON inspiration_boards
  FOR ALL 
  USING (
    EXISTS (
      SELECT 1 FROM projects 
      WHERE projects.id = inspiration_boards.project_id 
      AND (
        projects.client_id = auth.uid() OR 
        projects.designer_id = auth.uid()
      )
    )
  )
  WITH CHECK (
    EXISTS (
      SELECT 1 FROM projects 
      WHERE projects.id = inspiration_boards.project_id 
      AND (
        projects.client_id = auth.uid() OR 
        projects.designer_id = auth.uid()
      )
    )
  );

-- Grant necessary permissions
GRANT ALL ON inspiration_boards TO authenticated;
