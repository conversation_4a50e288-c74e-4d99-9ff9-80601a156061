-- =====================================================
-- FIX QUALITY FEEDBACK FOREIGN KEY CONSTRAINT
-- This fixes the foreign key constraint mismatch between quality_feedback 
-- and quality_reviews vs quality_reviews_new tables
-- =====================================================

-- Step 1: Check current constraint and table structure
DO $$
DECLARE
    constraint_exists BOOLEAN;
    old_table_exists BOOLEAN;
    new_table_exists BOOLEAN;
BEGIN
    -- Check if quality_reviews table exists
    SELECT EXISTS (
        SELECT FROM information_schema.tables 
        WHERE table_name = 'quality_reviews'
    ) INTO old_table_exists;
    
    -- Check if quality_reviews_new table exists
    SELECT EXISTS (
        SELECT FROM information_schema.tables 
        WHERE table_name = 'quality_reviews_new'
    ) INTO new_table_exists;
    
    -- Check if the problematic constraint exists
    SELECT EXISTS (
        SELECT FROM information_schema.table_constraints 
        WHERE constraint_name = 'quality_feedback_review_id_fkey'
        AND table_name = 'quality_feedback'
    ) INTO constraint_exists;
    
    RAISE NOTICE 'Table status: quality_reviews=%, quality_reviews_new=%, constraint_exists=%', 
                 old_table_exists, new_table_exists, constraint_exists;
END $$;

-- Step 2: Drop the existing constraint if it exists
DO $$
BEGIN
    IF EXISTS (
        SELECT FROM information_schema.table_constraints 
        WHERE constraint_name = 'quality_feedback_review_id_fkey'
        AND table_name = 'quality_feedback'
    ) THEN
        ALTER TABLE quality_feedback DROP CONSTRAINT quality_feedback_review_id_fkey;
        RAISE NOTICE 'Dropped existing foreign key constraint';
    END IF;
END $$;

-- Step 3: Create the correct constraint pointing to quality_reviews_new
DO $$
BEGIN
    -- Only create if quality_reviews_new exists and quality_feedback exists
    IF EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'quality_reviews_new') 
       AND EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'quality_feedback') THEN
        
        ALTER TABLE quality_feedback 
        ADD CONSTRAINT quality_feedback_review_id_fkey 
        FOREIGN KEY (review_id) REFERENCES quality_reviews_new(id) ON DELETE CASCADE;
        
        RAISE NOTICE 'Created new foreign key constraint pointing to quality_reviews_new';
    ELSE
        RAISE NOTICE 'Required tables not found, skipping constraint creation';
    END IF;
END $$;

-- Step 4: Ensure quality_feedback table exists with correct structure
CREATE TABLE IF NOT EXISTS quality_feedback (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    review_id UUID NOT NULL,
    standard_id UUID REFERENCES quality_standards(id),
    passed BOOLEAN NOT NULL,
    score INTEGER CHECK (score >= 1 AND score <= 5),
    comments TEXT,
    suggestions TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Step 5: Add the constraint if it doesn't exist (safety check)
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT FROM information_schema.table_constraints 
        WHERE constraint_name = 'quality_feedback_review_id_fkey'
        AND table_name = 'quality_feedback'
    ) THEN
        ALTER TABLE quality_feedback 
        ADD CONSTRAINT quality_feedback_review_id_fkey 
        FOREIGN KEY (review_id) REFERENCES quality_reviews_new(id) ON DELETE CASCADE;
        
        RAISE NOTICE 'Added missing foreign key constraint';
    END IF;
END $$;

-- Step 6: Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_quality_feedback_review_id ON quality_feedback(review_id);
CREATE INDEX IF NOT EXISTS idx_quality_feedback_standard_id ON quality_feedback(standard_id);

-- Step 7: Ensure RLS policies are correct
ALTER TABLE quality_feedback ENABLE ROW LEVEL SECURITY;

-- Policy for quality team to view all feedback
DROP POLICY IF EXISTS "Quality team can view all feedback" ON quality_feedback;
CREATE POLICY "Quality team can view all feedback" ON quality_feedback
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM profiles 
            WHERE profiles.id = auth.uid() 
            AND profiles.role IN ('quality_team', 'admin', 'manager')
        )
    );

-- Policy for quality team to insert feedback
DROP POLICY IF EXISTS "Quality team can create feedback" ON quality_feedback;
CREATE POLICY "Quality team can create feedback" ON quality_feedback
    FOR INSERT WITH CHECK (
        EXISTS (
            SELECT 1 FROM profiles 
            WHERE profiles.id = auth.uid() 
            AND profiles.role IN ('quality_team', 'admin', 'manager')
        )
    );

-- Policy for quality team to update feedback
DROP POLICY IF EXISTS "Quality team can update feedback" ON quality_feedback;
CREATE POLICY "Quality team can update feedback" ON quality_feedback
    FOR UPDATE USING (
        EXISTS (
            SELECT 1 FROM profiles 
            WHERE profiles.id = auth.uid() 
            AND profiles.role IN ('quality_team', 'admin', 'manager')
        )
    );

-- Policy for quality team to delete feedback
DROP POLICY IF EXISTS "Quality team can delete feedback" ON quality_feedback;
CREATE POLICY "Quality team can delete feedback" ON quality_feedback
    FOR DELETE USING (
        EXISTS (
            SELECT 1 FROM profiles 
            WHERE profiles.id = auth.uid() 
            AND profiles.role IN ('quality_team', 'admin', 'manager')
        )
    );

-- Step 8: Verify the fix
DO $$
DECLARE
    constraint_target TEXT;
BEGIN
    -- Check what table the constraint now points to
    SELECT ccu.table_name INTO constraint_target
    FROM information_schema.table_constraints tc
    JOIN information_schema.constraint_column_usage ccu 
        ON tc.constraint_name = ccu.constraint_name
    WHERE tc.constraint_name = 'quality_feedback_review_id_fkey'
    AND tc.table_name = 'quality_feedback';
    
    IF constraint_target = 'quality_reviews_new' THEN
        RAISE NOTICE '✅ SUCCESS: quality_feedback now correctly references quality_reviews_new';
    ELSIF constraint_target = 'quality_reviews' THEN
        RAISE NOTICE '❌ WARNING: quality_feedback still references old quality_reviews table';
    ELSE
        RAISE NOTICE '❓ UNKNOWN: Constraint target: %', constraint_target;
    END IF;
END $$;

-- Step 9: Clean up old quality_reviews table if it's empty and not needed
DO $$
DECLARE
    old_table_count INTEGER;
    new_table_count INTEGER;
BEGIN
    -- Count records in both tables
    SELECT COUNT(*) INTO old_table_count FROM quality_reviews WHERE EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'quality_reviews');
    SELECT COUNT(*) INTO new_table_count FROM quality_reviews_new WHERE EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'quality_reviews_new');
    
    RAISE NOTICE 'Record counts: quality_reviews=%, quality_reviews_new=%', old_table_count, new_table_count;
    
    -- Only suggest cleanup if old table is empty and new table has data
    IF old_table_count = 0 AND new_table_count > 0 THEN
        RAISE NOTICE 'SUGGESTION: Old quality_reviews table is empty and can be safely dropped';
        RAISE NOTICE 'Run: DROP TABLE IF EXISTS quality_reviews CASCADE;';
    END IF;
END $$;

-- Final verification query
SELECT 
    'quality_feedback_constraint_fix' as status,
    tc.constraint_name,
    tc.table_name as source_table,
    ccu.table_name as target_table,
    ccu.column_name as target_column
FROM information_schema.table_constraints tc
JOIN information_schema.constraint_column_usage ccu 
    ON tc.constraint_name = ccu.constraint_name
WHERE tc.constraint_name = 'quality_feedback_review_id_fkey'
AND tc.table_name = 'quality_feedback';
