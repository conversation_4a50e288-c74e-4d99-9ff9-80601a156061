// Simple client-side test component to verify Cloudflare R2 configuration
// Add this to a page to test environment variable access

export default function R2ConfigTest() {
  const testEnvironment = () => {
    console.log('🧪 [R2 CONFIG TEST] Environment Variables:');
    console.log('NEXT_PUBLIC_CLOUDFLARE_R2_PUBLIC_URL:', process.env.NEXT_PUBLIC_CLOUDFLARE_R2_PUBLIC_URL);
    console.log('NODE_ENV:', process.env.NODE_ENV);
    
    const r2BaseUrl = process.env.NEXT_PUBLIC_CLOUDFLARE_R2_PUBLIC_URL;
    if (r2BaseUrl) {
      console.log('✅ [R2 CONFIG TEST] Environment variable found:', r2BaseUrl);
      
      // Test URL construction
      const testUrls = [
        'submissions/test-123/design.pdf',
        'submissions/test-456/plan.dwg',
        'submissions/test-789/render.jpg'
      ];
      
      console.log('🔗 [R2 CONFIG TEST] Sam<PERSON> constructed URLs:');
      testUrls.forEach((path, index) => {
        const fullUrl = `${r2BaseUrl}/${path}`;
        console.log(`  ${index + 1}. ${fullUrl}`);
      });
    } else {
      console.error('❌ [R2 CONFIG TEST] NEXT_PUBLIC_CLOUDFLARE_R2_PUBLIC_URL not found!');
    }
  };

  return (
    <div className="p-4 border rounded-lg bg-gray-50">
      <h3 className="text-lg font-semibold mb-2">R2 Configuration Test</h3>
      <button 
        onClick={testEnvironment}
        className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
      >
        Test Environment Variables
      </button>
      <p className="text-sm text-gray-600 mt-2">
        Click to test and check browser console for results
      </p>
    </div>
  );
}
