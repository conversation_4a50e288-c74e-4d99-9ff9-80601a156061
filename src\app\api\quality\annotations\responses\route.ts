import { type NextRequest, NextResponse } from 'next/server';
import { supabase } from '@/lib/supabase';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const annotationId = searchParams.get('annotationId');

    if (!annotationId) {
      return NextResponse.json({ error: 'Annotation ID is required' }, { status: 400 });
    }

    // Get user from auth header
    const authHeader = request.headers.get('authorization');
    if (!authHeader?.startsWith('Bearer ')) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const token = authHeader.split(' ')[1];
    const { data: { user }, error: authError } = await supabase.auth.getUser(token);
    
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Fetch responses for the annotation
    const { data: responses, error } = await supabase
      .from('annotation_responses')
      .select(`
        id,
        response_text,
        response_type,
        responder_name,
        responder_role,
        created_at,
        updated_at
      `)
      .eq('annotation_id', annotationId)
      .order('created_at', { ascending: true });

    if (error) {
      console.error('Error fetching annotation responses:', error);
      return NextResponse.json({ error: 'Failed to fetch responses' }, { status: 500 });
    }

    return NextResponse.json({ responses: responses || [] });
  } catch (error) {
    console.error('Error in annotation responses GET:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();

    const {
      annotationId,
      responseText,
      responseType = 'comment'
    } = body;

    // Validate required fields
    if (!annotationId || !responseText?.trim()) {
      return NextResponse.json({ error: 'Annotation ID and response text are required' }, { status: 400 });
    }

    // Get user from auth header
    const authHeader = request.headers.get('authorization');
    if (!authHeader?.startsWith('Bearer ')) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const token = authHeader.split(' ')[1];
    const { data: { user }, error: authError } = await supabase.auth.getUser(token);
    
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get user profile
    const { data: profile } = await supabase
      .from('profiles')
      .select('role, full_name')
      .eq('id', user.id)
      .single();

    if (!profile) {
      return NextResponse.json({ error: 'User profile not found' }, { status: 404 });
    }

    // Check if user can respond to this annotation
    const { data: annotation } = await supabase
      .from('file_annotations')
      .select(`
        id,
        quality_review_id,
        quality_reviews_new!inner(designer_id)
      `)
      .eq('id', annotationId)
      .single();

    if (!annotation) {
      return NextResponse.json({ error: 'Annotation not found' }, { status: 404 });
    }

    // Check permissions
    const canRespond = 
      ['quality_team', 'admin', 'manager'].includes(profile.role) || // Quality team can always respond
      (profile.role === 'designer' && annotation.quality_reviews_new?.[0]?.designer_id === user.id); // Designer can respond to their own work

    if (!canRespond) {
      return NextResponse.json({ error: 'Insufficient permissions to respond' }, { status: 403 });
    }

    // Create response
    const { data: response, error } = await supabase
      .from('annotation_responses')
      .insert({
        annotation_id: annotationId,
        response_text: responseText.trim(),
        response_type: responseType,
        created_by: user.id,
        responder_name: profile.full_name,
        responder_role: profile.role
      })
      .select()
      .single();

    if (error) {
      console.error('Error creating annotation response:', error);
      return NextResponse.json({ error: 'Failed to create response' }, { status: 500 });
    }

    // If this is a resolution response, mark annotation as resolved
    if (responseType === 'resolution') {
      await supabase
        .from('file_annotations')
        .update({
          status: 'resolved',
          resolved_at: new Date().toISOString(),
          resolved_by: user.id
        })
        .eq('id', annotationId);
    }

    return NextResponse.json({ response }, { status: 201 });
  } catch (error) {
    console.error('Error in annotation responses POST:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

export async function PUT(request: NextRequest) {
  try {
    const body = await request.json();

    const {
      id,
      responseText,
      responseType
    } = body;

    if (!id) {
      return NextResponse.json({ error: 'Response ID is required' }, { status: 400 });
    }

    // Get user from auth header
    const authHeader = request.headers.get('authorization');
    if (!authHeader?.startsWith('Bearer ')) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const token = authHeader.split(' ')[1];
    const { data: { user }, error: authError } = await supabase.auth.getUser(token);
    
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check if user owns this response or has admin privileges
    const { data: existingResponse } = await supabase
      .from('annotation_responses')
      .select('created_by')
      .eq('id', id)
      .single();

    if (!existingResponse) {
      return NextResponse.json({ error: 'Response not found' }, { status: 404 });
    }

    const { data: profile } = await supabase
      .from('profiles')
      .select('role')
      .eq('id', user.id)
      .single();

    const canEdit = 
      existingResponse.created_by === user.id || // User owns the response
      ['admin', 'manager'].includes(profile?.role); // Admin can edit any response

    if (!canEdit) {
      return NextResponse.json({ error: 'Cannot edit this response' }, { status: 403 });
    }

    // Update response
    const updateData: any = { updated_at: new Date().toISOString() };
    if (responseText !== undefined) updateData.response_text = responseText.trim();
    if (responseType !== undefined) updateData.response_type = responseType;

    const { data: response, error } = await supabase
      .from('annotation_responses')
      .update(updateData)
      .eq('id', id)
      .select()
      .single();

    if (error) {
      console.error('Error updating annotation response:', error);
      return NextResponse.json({ error: 'Failed to update response' }, { status: 500 });
    }

    return NextResponse.json({ response });
  } catch (error) {
    console.error('Error in annotation responses PUT:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const id = searchParams.get('id');

    if (!id) {
      return NextResponse.json({ error: 'Response ID is required' }, { status: 400 });
    }

    // Get user from auth header
    const authHeader = request.headers.get('authorization');
    if (!authHeader?.startsWith('Bearer ')) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const token = authHeader.split(' ')[1];
    const { data: { user }, error: authError } = await supabase.auth.getUser(token);
    
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check if user owns this response or has admin privileges
    const { data: existingResponse } = await supabase
      .from('annotation_responses')
      .select('created_by')
      .eq('id', id)
      .single();

    if (!existingResponse) {
      return NextResponse.json({ error: 'Response not found' }, { status: 404 });
    }

    const { data: profile } = await supabase
      .from('profiles')
      .select('role')
      .eq('id', user.id)
      .single();

    const canDelete = 
      existingResponse.created_by === user.id || // User owns the response
      ['admin', 'manager'].includes(profile?.role); // Admin can delete any response

    if (!canDelete) {
      return NextResponse.json({ error: 'Cannot delete this response' }, { status: 403 });
    }

    // Delete response
    const { error } = await supabase
      .from('annotation_responses')
      .delete()
      .eq('id', id);

    if (error) {
      console.error('Error deleting annotation response:', error);
      return NextResponse.json({ error: 'Failed to delete response' }, { status: 500 });
    }

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Error in annotation responses DELETE:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
