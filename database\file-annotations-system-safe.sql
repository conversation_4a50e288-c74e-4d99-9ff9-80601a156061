-- File Annotations System Database Schema (Safe Version)
-- Integrates with existing quality review workflow

-- 1. FILE ANNOTATIONS TABLE
-- Stores visual annotations on files (images, PDFs, etc.)
CREATE TABLE IF NOT EXISTS file_annotations (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    file_url TEXT NOT NULL, -- URL of the annotated file
    file_name TEXT NOT NULL,
    file_type VARCHAR(100) NOT NULL,
    
    -- Annotation details
    annotation_type VARCHAR(50) NOT NULL CHECK (annotation_type IN ('issue', 'suggestion', 'approval', 'info')),
    x_position DECIMAL(5,2) NOT NULL, -- X coordinate as percentage (0-100)
    y_position DECIMAL(5,2) NOT NULL, -- Y coordinate as percentage (0-100)
    width DECIMAL(5,2), -- Width as percentage (for rectangles)
    height DECIMAL(5,2), -- Height as percentage (for rectangles)
    
    -- Content
    title VARCHAR(255),
    description TEXT,
    color VARCHAR(7) DEFAULT '#ff0000', -- Hex color code
    
    -- Relationships (nullable initially, will add constraints later)
    quality_review_id UUID, -- Will reference quality_reviews_new(id) when available
    submission_id UUID, -- Can reference submissions or designer_work_submissions
    submission_type VARCHAR(50) DEFAULT 'project' CHECK (submission_type IN ('project', 'work', 'vision')),
    
    -- User tracking
    created_by UUID, -- Will reference profiles(id) when available
    reviewer_name VARCHAR(255), -- Store name for display
    
    -- Status
    status VARCHAR(50) DEFAULT 'active' CHECK (status IN ('active', 'resolved', 'archived')),
    resolved_at TIMESTAMP WITH TIME ZONE,
    resolved_by UUID, -- Will reference profiles(id) when available
    
    -- Timestamps
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 2. ANNOTATION RESPONSES TABLE
-- Allows designers to respond to annotations
CREATE TABLE IF NOT EXISTS annotation_responses (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    annotation_id UUID REFERENCES file_annotations(id) ON DELETE CASCADE,
    
    -- Response content
    response_text TEXT NOT NULL,
    response_type VARCHAR(50) DEFAULT 'comment' CHECK (response_type IN ('comment', 'question', 'clarification', 'resolution')),
    
    -- User tracking
    created_by UUID, -- Will reference profiles(id) when available
    responder_name VARCHAR(255),
    responder_role VARCHAR(50), -- 'designer', 'quality_team', 'client', etc.
    
    -- Timestamps
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 3. ANNOTATION HISTORY TABLE
-- Track changes to annotations
CREATE TABLE IF NOT EXISTS annotation_history (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    annotation_id UUID REFERENCES file_annotations(id) ON DELETE CASCADE,
    
    -- Change tracking
    action VARCHAR(50) NOT NULL CHECK (action IN ('created', 'updated', 'resolved', 'reopened', 'deleted')),
    old_values JSONB,
    new_values JSONB,
    change_description TEXT,
    
    -- User tracking
    changed_by UUID, -- Will reference profiles(id) when available
    changed_by_name VARCHAR(255),
    
    -- Timestamps
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 4. INDEXES FOR PERFORMANCE
CREATE INDEX IF NOT EXISTS idx_file_annotations_file_url ON file_annotations(file_url);
CREATE INDEX IF NOT EXISTS idx_file_annotations_quality_review ON file_annotations(quality_review_id);
CREATE INDEX IF NOT EXISTS idx_file_annotations_submission ON file_annotations(submission_id, submission_type);
CREATE INDEX IF NOT EXISTS idx_file_annotations_created_by ON file_annotations(created_by);
CREATE INDEX IF NOT EXISTS idx_file_annotations_status ON file_annotations(status);
CREATE INDEX IF NOT EXISTS idx_file_annotations_created_at ON file_annotations(created_at);

CREATE INDEX IF NOT EXISTS idx_annotation_responses_annotation ON annotation_responses(annotation_id);
CREATE INDEX IF NOT EXISTS idx_annotation_responses_created_by ON annotation_responses(created_by);
CREATE INDEX IF NOT EXISTS idx_annotation_responses_created_at ON annotation_responses(created_at);

CREATE INDEX IF NOT EXISTS idx_annotation_history_annotation ON annotation_history(annotation_id);
CREATE INDEX IF NOT EXISTS idx_annotation_history_changed_by ON annotation_history(changed_by);
CREATE INDEX IF NOT EXISTS idx_annotation_history_created_at ON annotation_history(created_at);

-- 5. TRIGGERS FOR AUTOMATIC UPDATES
-- Update annotation updated_at timestamp
CREATE OR REPLACE FUNCTION update_annotation_timestamp()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_update_annotation_timestamp
    BEFORE UPDATE ON file_annotations
    FOR EACH ROW
    EXECUTE FUNCTION update_annotation_timestamp();

-- Create annotation history entry on changes
CREATE OR REPLACE FUNCTION create_annotation_history()
RETURNS TRIGGER AS $$
BEGIN
    IF TG_OP = 'INSERT' THEN
        INSERT INTO annotation_history (annotation_id, action, new_values, changed_by, changed_by_name)
        VALUES (NEW.id, 'created', to_jsonb(NEW), NEW.created_by, NEW.reviewer_name);
        RETURN NEW;
    ELSIF TG_OP = 'UPDATE' THEN
        -- Only create history if significant fields changed
        IF OLD.status != NEW.status OR OLD.description != NEW.description OR OLD.title != NEW.title THEN
            INSERT INTO annotation_history (annotation_id, action, old_values, new_values, changed_by, changed_by_name)
            VALUES (NEW.id, 'updated', to_jsonb(OLD), to_jsonb(NEW), NEW.created_by, NEW.reviewer_name);
        END IF;
        RETURN NEW;
    ELSIF TG_OP = 'DELETE' THEN
        INSERT INTO annotation_history (annotation_id, action, old_values, changed_by)
        VALUES (OLD.id, 'deleted', to_jsonb(OLD), OLD.created_by);
        RETURN OLD;
    END IF;
    RETURN NULL;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_annotation_history
    AFTER INSERT OR UPDATE OR DELETE ON file_annotations
    FOR EACH ROW
    EXECUTE FUNCTION create_annotation_history();

-- 6. ADD FOREIGN KEY CONSTRAINTS (if tables exist)
-- This section will add proper foreign key constraints when the referenced tables exist

DO $$
BEGIN
    -- Add foreign key to quality_reviews_new if it exists
    IF EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'quality_reviews_new') THEN
        -- Check if constraint doesn't already exist
        IF NOT EXISTS (
            SELECT 1 FROM information_schema.table_constraints 
            WHERE constraint_name = 'fk_file_annotations_quality_review'
        ) THEN
            ALTER TABLE file_annotations 
            ADD CONSTRAINT fk_file_annotations_quality_review 
            FOREIGN KEY (quality_review_id) REFERENCES quality_reviews_new(id) ON DELETE CASCADE;
            
            RAISE NOTICE '✅ Added foreign key constraint to quality_reviews_new';
        END IF;
    ELSE
        RAISE NOTICE '⚠️  quality_reviews_new table not found - foreign key constraint skipped';
    END IF;

    -- Add foreign key to profiles if it exists
    IF EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'profiles') THEN
        -- Add created_by constraint
        IF NOT EXISTS (
            SELECT 1 FROM information_schema.table_constraints 
            WHERE constraint_name = 'fk_file_annotations_created_by'
        ) THEN
            ALTER TABLE file_annotations 
            ADD CONSTRAINT fk_file_annotations_created_by 
            FOREIGN KEY (created_by) REFERENCES profiles(id) ON DELETE SET NULL;
        END IF;
        
        -- Add resolved_by constraint
        IF NOT EXISTS (
            SELECT 1 FROM information_schema.table_constraints 
            WHERE constraint_name = 'fk_file_annotations_resolved_by'
        ) THEN
            ALTER TABLE file_annotations 
            ADD CONSTRAINT fk_file_annotations_resolved_by 
            FOREIGN KEY (resolved_by) REFERENCES profiles(id) ON DELETE SET NULL;
        END IF;
        
        -- Add annotation_responses created_by constraint
        IF NOT EXISTS (
            SELECT 1 FROM information_schema.table_constraints 
            WHERE constraint_name = 'fk_annotation_responses_created_by'
        ) THEN
            ALTER TABLE annotation_responses 
            ADD CONSTRAINT fk_annotation_responses_created_by 
            FOREIGN KEY (created_by) REFERENCES profiles(id) ON DELETE SET NULL;
        END IF;
        
        -- Add annotation_history changed_by constraint
        IF NOT EXISTS (
            SELECT 1 FROM information_schema.table_constraints 
            WHERE constraint_name = 'fk_annotation_history_changed_by'
        ) THEN
            ALTER TABLE annotation_history 
            ADD CONSTRAINT fk_annotation_history_changed_by 
            FOREIGN KEY (changed_by) REFERENCES profiles(id) ON DELETE SET NULL;
        END IF;
        
        RAISE NOTICE '✅ Added foreign key constraints to profiles';
    ELSE
        RAISE NOTICE '⚠️  profiles table not found - foreign key constraints skipped';
    END IF;
END $$;

-- 7. ENABLE ROW LEVEL SECURITY
ALTER TABLE file_annotations ENABLE ROW LEVEL SECURITY;
ALTER TABLE annotation_responses ENABLE ROW LEVEL SECURITY;
ALTER TABLE annotation_history ENABLE ROW LEVEL SECURITY;

-- 8. CREATE RLS POLICIES
-- Policies for file_annotations
CREATE POLICY "Quality team can manage all annotations" ON file_annotations
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM profiles 
            WHERE profiles.id = auth.uid() 
            AND profiles.role IN ('quality_team', 'admin', 'manager')
        )
    );

CREATE POLICY "Designers can view annotations on their work" ON file_annotations
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM profiles 
            WHERE profiles.id = auth.uid() 
            AND profiles.role = 'designer'
        )
    );

-- Policies for annotation_responses
CREATE POLICY "Users can manage responses to annotations" ON annotation_responses
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM profiles 
            WHERE profiles.id = auth.uid() 
            AND profiles.role IN ('quality_team', 'admin', 'manager', 'designer')
        )
    );

-- Policies for annotation_history (read-only for authorized users)
CREATE POLICY "Authorized users can view annotation history" ON annotation_history
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM profiles 
            WHERE profiles.id = auth.uid() 
            AND profiles.role IN ('quality_team', 'admin', 'manager')
        )
    );

-- 9. COMMENTS FOR DOCUMENTATION
COMMENT ON TABLE file_annotations IS 'Visual annotations on files during quality review process';
COMMENT ON TABLE annotation_responses IS 'Responses and discussions on file annotations';
COMMENT ON TABLE annotation_history IS 'Audit trail for annotation changes';

COMMENT ON COLUMN file_annotations.x_position IS 'X coordinate as percentage (0-100) of file width';
COMMENT ON COLUMN file_annotations.y_position IS 'Y coordinate as percentage (0-100) of file height';
COMMENT ON COLUMN file_annotations.submission_type IS 'Type of submission: project (main submissions), work (designer work), vision (vision requests)';

-- 10. SUCCESS MESSAGE
DO $$
BEGIN
    RAISE NOTICE '🎉 File Annotations System successfully installed!';
    RAISE NOTICE '📋 Created tables: file_annotations, annotation_responses, annotation_history';
    RAISE NOTICE '🔒 Enabled Row Level Security with appropriate policies';
    RAISE NOTICE '⚡ Added indexes and triggers for optimal performance';
    RAISE NOTICE '🔗 Foreign key constraints will be added when referenced tables are available';
END $$;
