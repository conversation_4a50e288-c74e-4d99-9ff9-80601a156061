import { NextRequest, NextResponse } from 'next/server';
import { S3Client, PutObjectCommand } from '@aws-sdk/client-s3';

// Server-side R2 configuration (can access all environment variables)
const getR2Config = () => {
  const accessKeyId = process.env.CLOUDFLARE_R2_ACCESS_KEY_ID;
  const secretAccessKey = process.env.CLOUDFLARE_R2_SECRET_ACCESS_KEY;
  const endpoint = process.env.CLOUDFLARE_R2_ENDPOINT;

  console.log('🔍 [R2 API] Environment variables check:', {
    hasAccessKeyId: !!accessKeyId,
    hasSecretAccessKey: !!secretAccessKey,
    hasEndpoint: !!endpoint,
    endpoint: endpoint ? `${endpoint.slice(0, 30)}...` : 'undefined'
  });

  if (!endpoint) {
    throw new Error('Missing CLOUDFLARE_R2_ENDPOINT environment variable');
  }

  if (!accessKeyId || !secretAccessKey) {
    throw new Error('Missing CLOUDFLARE_R2_ACCESS_KEY_ID or CLOUDFLARE_R2_SECRET_ACCESS_KEY environment variables');
  }

  return {
    region: 'auto',
    endpoint: endpoint,
    credentials: {
      accessKeyId: accessKeyId,
      secretAccessKey: secretAccessKey,
    },
    forcePathStyle: true, // Required for R2
  };
};

// Initialize S3 client for Cloudflare R2
const s3Client = new S3Client(getR2Config());

export async function POST(request: NextRequest) {
  try {
    const formData = await request.formData();
    const file = formData.get('file') as File;
    const bucketName = formData.get('bucketName') as string;
    const folderPath = formData.get('folderPath') as string;

    if (!file) {
      return NextResponse.json({ error: 'No file provided' }, { status: 400 });
    }

    if (!bucketName) {
      return NextResponse.json({ error: 'No bucket name provided' }, { status: 400 });
    }

    console.log('🔍 [R2 API] Upload request:', {
      fileName: file.name,
      fileSize: file.size,
      fileType: file.type,
      bucketName,
      folderPath
    });

    // Generate a unique file name to avoid collisions
    const uniqueFileName = `${Date.now()}-${file.name.replace(/\s+/g, '-')}`;

    // Create the full key path
    const key = folderPath
      ? `${folderPath.replace(/\/+$/, '')}/${uniqueFileName}`
      : uniqueFileName;

    // Convert File to Buffer
    const arrayBuffer = await file.arrayBuffer();
    const fileBuffer = Buffer.from(arrayBuffer);

    // Determine content type
    let contentType = file.type || 'application/octet-stream';
    if (!contentType || contentType === 'application/octet-stream') {
      // Fallback content type detection
      if (file.name.match(/\.(jpg|jpeg)$/i)) contentType = 'image/jpeg';
      else if (file.name.match(/\.png$/i)) contentType = 'image/png';
      else if (file.name.match(/\.gif$/i)) contentType = 'image/gif';
      else if (file.name.match(/\.pdf$/i)) contentType = 'application/pdf';
      else if (file.name.match(/\.doc$/i)) contentType = 'application/msword';
      else if (file.name.match(/\.docx$/i)) contentType = 'application/vnd.openxmlformats-officedocument.wordprocessingml.document';
      else if (file.name.match(/\.txt$/i)) contentType = 'text/plain';
      else if (file.name.match(/\.zip$/i)) contentType = 'application/zip';
    }

    // Upload the file to R2
    const command = new PutObjectCommand({
      Bucket: bucketName,
      Key: key,
      Body: fileBuffer,
      ContentType: contentType,
    });

    console.log('🔍 [R2 API] Upload command:', {
      Bucket: bucketName,
      Key: key,
      ContentType: contentType,
      BodySize: fileBuffer.length
    });

    await s3Client.send(command);

    console.log('✅ [R2 API] Upload successful, key:', key);

    // Return the file key and metadata
    return NextResponse.json({
      success: true,
      key: key,
      fileName: file.name,
      fileSize: file.size,
      contentType: contentType,
      url: `${process.env.CLOUDFLARE_R2_PUBLIC_URL}/${key}` // Public URL if needed
    });

  } catch (error) {
    console.error('❌ [R2 API] Error uploading file:', error);
    return NextResponse.json(
      { 
        error: 'Failed to upload file',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
