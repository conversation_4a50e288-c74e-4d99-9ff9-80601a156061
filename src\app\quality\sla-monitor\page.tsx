'use client';

import React from 'react';
import { Card, Card<PERSON>ontent, Card<PERSON>eader, CardTitle } from '@/components/ui/card';
import QualitySLAMonitor from '@/components/quality/QualitySLAMonitor';
import { useOptimizedAuth } from '@/hooks/useOptimizedAuth';
import { Timer, AlertTriangle } from 'lucide-react';

export default function SLAMonitorPage() {
  const { user, profile, loading } = useOptimizedAuth();

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (!user || !['quality_team', 'admin', 'manager'].includes(profile?.role)) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <Card className="w-96">
          <CardContent className="p-6 text-center">
            <AlertTriangle className="h-12 w-12 text-red-500 mx-auto mb-4" />
            <h2 className="text-xl font-semibold mb-2">Access Denied</h2>
            <p className="text-gray-600">You don't have permission to access this page.</p>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="mb-8">
        <div className="flex items-center gap-3 mb-2">
          <Timer className="h-8 w-8 text-orange-500" />
          <h1 className="text-3xl font-bold">SLA Monitor</h1>
        </div>
        <p className="text-gray-600">
          Track review deadlines, monitor team workload, and ensure SLA compliance
        </p>
      </div>

      <QualitySLAMonitor 
        teamMemberId={profile?.role === 'quality_team' ? user.id : undefined}
        showTeamOverview={['admin', 'manager'].includes(profile?.role)}
      />
    </div>
  );
}
