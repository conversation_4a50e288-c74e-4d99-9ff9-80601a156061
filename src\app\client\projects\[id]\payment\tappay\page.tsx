'use client';

import { useState, useEffect, Suspense } from 'react';
import { useParams, useSearchParams, useRouter } from 'next/navigation';
import { useOptimizedAuth } from '@/hooks/useOptimizedAuth';
import { supabase } from '@/lib/supabase';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import TapPaymentsV2Form from '@/components/payments/TapPaymentsV2Form';
import { 
  ArrowLeft, 
  CreditCard, 
  Shield, 
  CheckCircle, 
  AlertTriangle,
  Loader2,
  DollarSign,
  FileText
} from 'lucide-react';
import Link from 'next/link';

interface ProjectData {
  id: string;
  title: string;
  client_id: string;
  designer_id: string;
  status: string;
}

interface MilestoneData {
  id: string;
  title: string;
  amount: number;
  status: string;
  order_index: number;
}

function TapPayPaymentContent() {
  const { user } = useOptimizedAuth();
  const params = useParams();
  const searchParams = useSearchParams();
  const router = useRouter();
  
  const projectId = params.id as string;
  const milestoneId = searchParams.get('milestone');
  const amount = parseInt(searchParams.get('amount') || '0');
  const paymentType = searchParams.get('type') as 'deposit' | 'milestone' | 'final' || 'milestone';
  
  const [loading, setLoading] = useState(true);
  const [project, setProject] = useState<ProjectData | null>(null);
  const [milestone, setMilestone] = useState<MilestoneData | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [paymentSuccess, setPaymentSuccess] = useState(false);

  useEffect(() => {
    if (user && projectId) {
      fetchProjectData();
    }
  }, [user, projectId, milestoneId]);

  const fetchProjectData = async () => {
    try {
      setLoading(true);
      setError(null);

      // Fetch project data
      const { data: projectData, error: projectError } = await supabase
        .from('projects')
        .select('id, title, client_id, designer_id, status')
        .eq('id', projectId)
        .single();

      if (projectError || !projectData) {
        throw new Error('Project not found');
      }

      // Verify user is the client
      if (projectData.client_id !== user?.id) {
        throw new Error('Unauthorized - You are not the client for this project');
      }

      setProject(projectData);

      // Fetch milestone data if milestone ID is provided
      if (milestoneId) {
        const { data: milestoneData, error: milestoneError } = await supabase
          .from('project_milestones')
          .select('id, title, amount, status, order_index')
          .eq('id', milestoneId)
          .eq('project_id', projectId)
          .single();

        if (milestoneError || !milestoneData) {
          throw new Error('Milestone not found');
        }

        setMilestone(milestoneData);
      }

    } catch (error) {
      console.error('Error fetching project data:', error);
      setError(error instanceof Error ? error.message : 'Failed to load project data');
    } finally {
      setLoading(false);
    }
  };

  const handlePaymentSuccess = (result: any) => {
    console.log('TapPay payment successful:', result);
    setPaymentSuccess(true);
    
    // Redirect to project page after a delay
    setTimeout(() => {
      router.push(`/client/projects/${projectId}?payment=success&type=${paymentType}`);
    }, 3000);
  };

  const handlePaymentError = (error: string) => {
    console.error('TapPay payment error:', error);
    setError(error);
  };

  const getPaymentDescription = () => {
    if (paymentType === 'deposit') {
      return `Initial deposit for ${project?.title}`;
    } else if (milestone) {
      return `Payment for milestone: ${milestone.title}`;
    } else {
      return `Payment for ${project?.title}`;
    }
  };

  const getPaymentTitle = () => {
    if (paymentType === 'deposit') {
      return 'Initial Deposit Payment';
    } else if (milestone) {
      return `Milestone Payment: ${milestone.title}`;
    } else {
      return 'Project Payment';
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4 text-blue-600" />
          <p className="text-gray-600">Loading payment details...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center p-4">
        <Card className="w-full max-w-md">
          <CardContent className="p-6 text-center">
            <AlertTriangle className="h-12 w-12 text-red-500 mx-auto mb-4" />
            <h2 className="text-xl font-semibold text-gray-900 mb-2">Payment Error</h2>
            <p className="text-gray-600 mb-4">{error}</p>
            <Link href={`/client/projects/${projectId}`}>
              <Button variant="outline" className="w-full">
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back to Project
              </Button>
            </Link>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (paymentSuccess) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center p-4">
        <Card className="w-full max-w-md">
          <CardContent className="p-6 text-center">
            <CheckCircle className="h-12 w-12 text-green-500 mx-auto mb-4" />
            <h2 className="text-xl font-semibold text-gray-900 mb-2">Payment Successful!</h2>
            <p className="text-gray-600 mb-4">
              Your payment has been processed successfully. You will be redirected to your project shortly.
            </p>
            <div className="text-sm text-gray-500">
              <p>Amount: ${(amount / 100).toFixed(2)}</p>
              <p>Payment Method: TapPay</p>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-2xl mx-auto px-4">
        {/* Header */}
        <div className="mb-6">
          <Link href={`/client/projects/${projectId}`}>
            <Button variant="ghost" className="mb-4">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Project
            </Button>
          </Link>
          
          <div className="bg-white rounded-lg shadow-sm p-6 mb-6">
            <div className="flex items-center justify-between mb-4">
              <h1 className="text-2xl font-bold text-gray-900">{getPaymentTitle()}</h1>
              <Badge variant="outline" className="bg-blue-50 text-blue-700 border-blue-200">
                <CreditCard className="h-3 w-3 mr-1" />
                TapPay
              </Badge>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
              <div>
                <p className="text-gray-500">Project</p>
                <p className="font-medium text-gray-900">{project?.title}</p>
              </div>
              <div>
                <p className="text-gray-500">Amount</p>
                <p className="font-medium text-gray-900 text-lg">
                  ${(amount / 100).toFixed(2)} USD
                </p>
              </div>
              {milestone && (
                <>
                  <div>
                    <p className="text-gray-500">Milestone</p>
                    <p className="font-medium text-gray-900">{milestone.title}</p>
                  </div>
                  <div>
                    <p className="text-gray-500">Payment Type</p>
                    <p className="font-medium text-gray-900 capitalize">{paymentType}</p>
                  </div>
                </>
              )}
            </div>
          </div>
        </div>

        {/* Payment Form */}
        <div className="bg-white rounded-lg shadow-sm">
          <div className="p-6 border-b border-gray-200">
            <div className="flex items-center justify-between">
              <h2 className="text-lg font-semibold text-gray-900">Payment Details</h2>
              <div className="flex items-center text-sm text-gray-500">
                <Shield className="h-4 w-4 mr-1" />
                Secured by TapPay
              </div>
            </div>
          </div>
          
          <div className="p-6">
            {error && (
              <Alert variant="destructive" className="mb-6">
                <AlertTriangle className="h-4 w-4" />
                <AlertDescription>{error}</AlertDescription>
              </Alert>
            )}

            <TapPaymentsV2Form
              projectId={projectId}
              milestoneId={milestoneId || undefined}
              amount={amount}
              description={getPaymentDescription()}
              paymentType={paymentType}
              clientId={user?.id || ''}
              designerId={project?.designer_id}
              onSuccess={handlePaymentSuccess}
              onError={handlePaymentError}
            />
          </div>
        </div>

        {/* Security Notice */}
        <div className="mt-6 text-center text-sm text-gray-500">
          <div className="flex items-center justify-center mb-2">
            <Shield className="h-4 w-4 mr-2" />
            <span>Your payment is secured with 3D Secure authentication</span>
          </div>
          <p>All transactions are encrypted and processed securely through TapPay</p>
        </div>
      </div>
    </div>
  );
}

export default function TapPayPaymentPage() {
  return (
    <Suspense fallback={
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <Loader2 className="h-8 w-8 animate-spin text-blue-600" />
      </div>
    }>
      <TapPayPaymentContent />
    </Suspense>
  );
}
