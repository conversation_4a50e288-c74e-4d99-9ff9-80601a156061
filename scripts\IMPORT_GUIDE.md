# Quick Import Guide

## 🎯 What the <PERSON>ript Found

Your environment sync revealed:

- **🔐 20 variables** already exist in Vercel production (encrypted for security)
- **📝 27 new variables** in your local environment that need to be added to production
- **✅ 0 variables** only in production

## 🚀 Next Steps

### Option 1: Bulk Import (Recommended)

The `production.env` file is ready for import. However, since Vercel CLI doesn't support bulk import from file, you'll need to add variables individually or use the dashboard.

### Option 2: Add Missing Variables Individually

Based on the analysis, you need to add these 27 variables to production:

```bash
# Core Supabase variables
npx vercel env add NEXT_PUBLIC_SUPABASE_URL production
npx vercel env add NEXT_PUBLIC_SUPABASE_ANON_KEY production
npx vercel env add SUPABASE_SERVICE_ROLE_KEY production

# Site configuration
npx vercel env add NEXT_PUBLIC_SITE_URL production
npx vercel env add NEXT_PUBLIC_APP_URL production
npx vercel env add NEXT_PUBLIC_COOKIE_DOMAIN production

# Stripe
npx vercel env add STRIPE_SECRET_KEY production
npx vercel env add NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY production
npx vercel env add STRIPE_WEBHOOK_SECRET production

# Email & Communication
npx vercel env add RESEND_API_KEY production
npx vercel env add NEWSLETTER_FROM_EMAIL production
npx vercel env add NEWSLETTER_FROM_NAME production

# reCAPTCHA
npx vercel env add NEXT_PUBLIC_RECAPTCHA_SITE_KEY production
npx vercel env add RECAPTCHA_SECRET_KEY production
npx vercel env add SKIP_RECAPTCHA production

# Cloudflare R2
npx vercel env add CLOUDFLARE_R2_ENDPOINT production
npx vercel env add CLOUDFLARE_R2_ACCESS_KEY_ID production
npx vercel env add CLOUDFLARE_R2_SECRET_ACCESS_KEY production
npx vercel env add CLOUDFLARE_R2_PUBLIC_URL production
npx vercel env add NEXT_PUBLIC_CLOUDFLARE_R2_PUBLIC_URL production

# Security & Encryption
npx vercel env add ENCRYPTION_KEY production
npx vercel env add ENCRYPTION_KEY_BACKUP production

# AI & Analytics
npx vercel env add GOOGLE_API_KEY production
npx vercel env add ENABLE_AI_GENERATION production
npx vercel env add MAX_DAILY_AI_REQUESTS production
npx vercel env add NEXT_PUBLIC_GA_MEASUREMENT_ID production
npx vercel env add NEXT_PUBLIC_ANALYTICS_SALT production
```

### Option 3: Use Vercel Dashboard

1. Go to your [Vercel Dashboard](https://vercel.com/dashboard)
2. Select your project: `seniors-archi-firm`
3. Go to Settings → Environment Variables
4. Add each variable manually using the values from `production.env`

## ⚠️ Important Production Updates

Before importing, update these values for production:

```bash
# Change from localhost to your production domain
NEXT_PUBLIC_SITE_URL=https://your-production-domain.com
NEXT_PUBLIC_APP_URL=https://your-production-domain.com

# Disable development flags
SKIP_RECAPTCHA=false

# Update payment environments to production
TAPPAY_ENVIRONMENT=production
CLICKPAY_ENVIRONMENT=production

# Remove development webhook skip
# PAYPAL_SKIP_WEBHOOK_VERIFICATION=false (or remove entirely)
```

## 🔧 Automated Import Script

Create a quick import script:

```bash
#!/bin/bash
# import-env.sh

# Read production.env and add each variable
while IFS='=' read -r key value; do
  # Skip comments and empty lines
  if [[ $key =~ ^[[:space:]]*# ]] || [[ -z $key ]]; then
    continue
  fi
  
  # Remove any quotes from value
  value=$(echo "$value" | sed 's/^"//;s/"$//')
  
  echo "Adding $key..."
  npx vercel env add "$key" production <<< "$value"
done < production.env
```

Make it executable and run:
```bash
chmod +x import-env.sh
./import-env.sh
```

## 🔍 Verification

After importing, verify your environment variables:

```bash
# List all production environment variables
npx vercel env ls production

# Check specific variable
npx vercel env ls production | grep NEXT_PUBLIC_SUPABASE_URL
```

## 📋 Summary

- ✅ Script successfully identified environment differences
- ✅ Generated production-ready environment file
- ✅ 27 new variables ready for import
- ✅ 20 existing variables confirmed in production

Your environment sync is complete! 🎉
