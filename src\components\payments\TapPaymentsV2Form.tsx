'use client';

import React, { useState, useEffect, useRef } from 'react';
import { Card, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Loader2, CreditCard, CheckCircle, Shield } from 'lucide-react';

interface TapPaymentsV2FormProps {
  projectId: string;
  milestoneId?: string;
  amount: number; // Amount in USD cents
  description: string;
  clientId: string;
  designerId?: string;
  paymentType: 'deposit' | 'milestone' | 'final';
  onSuccess: (result: any) => void;
  onError: (error: string) => void;
}

declare global {
  interface Window {
    TPDirect: any;
  }
}

export default function TapPaymentsV2Form({
  projectId,
  milestoneId,
  amount,
  description,
  clientId,
  designerId,
  paymentType,
  onSuccess,
  onError
}: TapPaymentsV2FormProps) {
  const [isLoading, setIsLoading] = useState(true);
  const [isReady, setIsReady] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [validationState, setValidationState] = useState<any>(null);
  const [retryCount, setRetryCount] = useState(0);
  const containerRef = useRef<HTMLDivElement>(null);
  const initRef = useRef(false);
  const unmountRef = useRef<(() => void) | null>(null);

  // TapPay Taiwan configuration from environment variables
  const TAP_CONFIG = {
    appId: process.env.NEXT_PUBLIC_TAPPAY_APP_ID || '66713781',
    // Use the app key from SimpleTapPayForm which is known to work
    appKey: process.env.NEXT_PUBLIC_TAPPAY_APP_KEY || 'app_whdEWBH8e8Lzy4N6BysVRdIDjAXfKWvlrE6ykjSzGjTUcuTVJ2ZpzAPqfcb0',
    partnerKey: process.env.TAPPAY_PARTNER_KEY || 'sk_test_8VUKxaGXi15YiA4HfJukbzQR',
    merchantId: process.env.TAPPAY_MERCHANT_ID || '66713781',
    environment: process.env.TAPPAY_ENVIRONMENT || 'sandbox',
    usingTestCredentials: true // Keep this true for now
  };

  // Debug configuration
  useEffect(() => {
    console.log('🔧 TapPay Taiwan Configuration Debug:');
    console.log('   App ID:', TAP_CONFIG.appId || 'NOT SET');
    console.log('   App Key:', TAP_CONFIG.appKey ? `${TAP_CONFIG.appKey.substring(0, 20)}...` : 'NOT SET');
    console.log('   Merchant ID:', TAP_CONFIG.merchantId || 'NOT SET');
    console.log('   Environment:', TAP_CONFIG.environment || 'NOT SET');

    if (TAP_CONFIG.usingTestCredentials) {
      console.warn('⚠️ USING TEST CREDENTIALS - TapPay Taiwan sandbox environment');
      console.warn('⚠️ To use production, change TAPPAY_ENVIRONMENT to "production"');
    }
  }, []);

  // Additional containment enforcement after component mounts
  useEffect(() => {
    const enforceContainment = () => {
      const container = document.getElementById('tap-card-container');
      if (container) {
        // Apply additional containment styles
        container.style.position = 'relative';
        container.style.overflow = 'hidden';
        container.style.zIndex = '1';
        container.style.isolation = 'isolate';

        // Find and fix any TapPay elements that might have escaped
        const tapElements = container.querySelectorAll('*');
        tapElements.forEach((element: any) => {
          if (element.style) {
            // Reset any absolute positioning
            if (element.style.position === 'absolute' || element.style.position === 'fixed') {
              element.style.position = 'relative';
              element.style.top = 'auto';
              element.style.left = 'auto';
              element.style.right = 'auto';
              element.style.bottom = 'auto';
            }
          }
        });
      }
    };

    // Run immediately and then periodically to catch dynamic elements
    enforceContainment();
    const interval = setInterval(enforceContainment, 1000);

    return () => clearInterval(interval);
  }, [isReady]);

  useEffect(() => {
    if (initRef.current) return;
    initRef.current = true;

    const initTapPayments = async () => {
      try {
        console.log('🔄 Initializing Tap Payments SDK V2...');
        setError(null);

        // Load Tap Payments SDK V2
        await loadTapSDK();

        // Wait for container to be available in DOM
        await waitForContainer();

        // Setup the card form
        await setupTapCard();

        console.log('✅ Tap Payments V2 ready');

      } catch (error) {
        console.error('❌ Tap Payments initialization failed:', error);
        const errorMessage = error instanceof Error ? error.message : 'Unknown error';
        setError(`Payment system initialization failed: ${errorMessage}`);

        // Show specific error messages for common issues
        if (errorMessage.includes('timeout')) {
          setError('Payment system is taking too long to load. Please check your internet connection and try again.');
        } else if (errorMessage.includes('CDN')) {
          setError('Unable to load payment system from external service. Please try again later.');
        } else if (errorMessage.includes('CardSDK')) {
          setError('Payment system components not available. Please refresh the page and try again.');
        }
      } finally {
        setIsLoading(false);
      }
    };

    // Delay initialization to ensure component is mounted
    const timer = setTimeout(initTapPayments, 100);

    // Cleanup on unmount
    return () => {
      clearTimeout(timer);
      if (unmountRef.current) {
        try {
          unmountRef.current();
          console.log('🧹 Tap Payments SDK unmounted');
        } catch (error) {
          console.warn('Cleanup warning:', error);
        }
      }
    };
  }, []);

  const waitForContainer = (): Promise<void> => {
    return new Promise((resolve, reject) => {
      const checkContainer = () => {
        const container = document.getElementById('tap-card-container');
        if (container) {
          console.log('✅ Container found, proceeding with TapPay setup');
          resolve();
        } else {
          console.log('⏳ Waiting for container...');
        }
      };

      // Check immediately
      checkContainer();

      // If not found, keep checking
      let attempts = 0;
      const maxAttempts = 50; // 5 seconds max wait
      const interval = setInterval(() => {
        attempts++;
        const container = document.getElementById('tap-card-container');

        if (container) {
          clearInterval(interval);
          console.log('✅ Container found after', attempts * 100, 'ms');
          resolve();
        } else if (attempts >= maxAttempts) {
          clearInterval(interval);
          console.error('❌ Container not found after', maxAttempts * 100, 'ms');
          reject(new Error('Card container element not found - DOM not ready'));
        }
      }, 100);
    });
  };

  const loadTapSDK = (): Promise<void> => {
    return new Promise((resolve, reject) => {
      console.log('🔄 Starting TapPay SDK load process...');

      // Check if already loaded
      if (window.TPDirect) {
        console.log('✅ TapPay SDK already loaded');
        resolve();
        return;
      }

      // Check if we're in development environment
      const isDevelopment = window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1';

      if (isDevelopment) {
        console.log('🔧 Development environment detected - using mock TapPay SDK');
        createMockTapPaySDK();
        resolve();
        return;
      }

      console.log('🔄 Loading TapPay Taiwan SDK from CDN...');

      // Check if script already exists
      const existingScript = document.querySelector('script[src*="tappaysdk.com"]');
      if (existingScript) {
        console.log('⚠️ TapPay script already exists, waiting for load...');

        const checkLoaded = () => {
          if (window.TPDirect) {
            console.log('✅ TapPay SDK loaded from existing script');
            resolve();
          } else {
            setTimeout(checkLoaded, 100);
          }
        };

        setTimeout(() => {
          if (!window.TPDirect) {
            console.log('⚠️ TapPay SDK failed to load, falling back to mock');
            createMockTapPaySDK();
            resolve();
          }
        }, 5000);

        checkLoaded();
        return;
      }

      const script = document.createElement('script');
      script.src = 'https://js.tappaysdk.com/tpdirect/v5.14.0';
      script.async = true;
      script.crossOrigin = 'anonymous';

      // Add timeout to prevent infinite loading
      const timeout = setTimeout(() => {
        console.warn('⚠️ TapPay SDK loading timeout - falling back to mock SDK');
        createMockTapPaySDK();
        resolve();
      }, 5000); // Reduced timeout

      script.onload = () => {
        clearTimeout(timeout);
        console.log('✅ TapPay SDK script loaded successfully');

        if (window.TPDirect) {
          console.log('✅ TPDirect object available');
          resolve();
        } else {
          console.log('⚠️ TPDirect not available, using mock');
          createMockTapPaySDK();
          resolve();
        }
      };

      script.onerror = (error) => {
        clearTimeout(timeout);
        console.warn('⚠️ Failed to load TapPay SDK, falling back to mock:', error);
        createMockTapPaySDK();
        resolve();
      };

      document.head.appendChild(script);
      console.log('📡 TapPay SDK script added to page head');
    });
  };

  const createMockTapPaySDK = () => {
    console.log('🔧 Creating mock TapPay SDK for development...');

    window.TPDirect = {
      setupSDK: (appId: string, appKey: string, environment: string) => {
        console.log('🔧 Mock TapPay SDK setup:', { appId, environment });
      },
      card: {
        setup: (config: any) => {
          console.log('🔧 Mock card setup:', config);

          // Create mock input fields
          setTimeout(() => {
            const fields = ['card-number', 'card-expiry', 'card-ccv'];
            fields.forEach(fieldId => {
              const element = document.getElementById(fieldId);
              if (element) {
                element.innerHTML = `
                  <input
                    type="text"
                    placeholder="${fieldId === 'card-number' ? '4242 4242 4242 4242 (Mock)' : fieldId === 'card-expiry' ? '12/25' : '123'}"
                    value="${fieldId === 'card-number' ? '4242 4242 4242 4242' : fieldId === 'card-expiry' ? '12/25' : '123'}"
                    style="width: 100%; height: 100%; border: none; outline: none; padding: 8px; font-size: 16px; background: #f8f9fa;"
                    data-mock-field="${fieldId}"
                    readonly
                  />
                `;
              }
            });

            // Simulate card ready state
            setTimeout(() => {
              if (window.TPDirect.card.onUpdateCallback) {
                window.TPDirect.card.onUpdateCallback({
                  canGetPrime: true,
                  cardType: 'visa',
                  hasError: false
                });
              }
            }, 500);
          }, 100);
        },
        onUpdate: (callback: any) => {
          console.log('🔧 Mock onUpdate callback registered');
          window.TPDirect.card.onUpdateCallback = callback;
        },
        getPrime: (callback: any) => {
          console.log('🔧 Mock getPrime called');

          // Simulate getting prime token
          setTimeout(() => {
            const mockPrime = 'mock_prime_' + Date.now();
            console.log('✅ Mock prime generated:', mockPrime);

            callback({
              status: 0,
              msg: 'Success',
              card: {
                prime: mockPrime,
                bincode: '424242',
                last_four: '4242',
                issuer: 'visa',
                funding: 1,
                type: 1,
                level: '',
                country: 'TW',
                country_code: 'TW'
              }
            });
          }, 1000);
        },
        onUpdateCallback: null
      }
    };

    console.log('✅ Mock TapPay SDK created successfully');
  };

  const setupTapCard = (): Promise<void> => {
    return new Promise((resolve, reject) => {
      try {
        console.log('🔧 Starting TapPay card setup...');

        if (!window.TPDirect) {
          console.error('❌ TPDirect not available');
          reject(new Error('TPDirect not available - SDK not loaded properly'));
          return;
        }

        console.log('✅ TPDirect available, checking methods...');
        console.log('   TPDirect methods:', Object.keys(window.TPDirect));

        if (!TAP_CONFIG.appId) {
          console.error('❌ App ID missing:', TAP_CONFIG.appId);
          reject(new Error('TapPay App ID not configured'));
          return;
        }

        if (!TAP_CONFIG.appKey) {
          console.error('❌ App Key missing:', TAP_CONFIG.appKey);
          reject(new Error('TapPay App Key not configured'));
          return;
        }

        console.log('🔧 Setting up TapPay Taiwan card form...');
        console.log(`   App ID: ${TAP_CONFIG.appId}`);
        console.log(`   App Key: ${TAP_CONFIG.appKey?.substring(0, 20)}...`);
        console.log(`   Environment: ${TAP_CONFIG.environment}`);

        // Check if setupSDK method exists
        if (!window.TPDirect.setupSDK) {
          console.error('❌ TPDirect.setupSDK method not available');
          reject(new Error('TPDirect.setupSDK method not available'));
          return;
        }

        // Initialize TapPay SDK
        console.log('🔄 Calling TPDirect.setupSDK...');
        window.TPDirect.setupSDK(TAP_CONFIG.appId, TAP_CONFIG.appKey, TAP_CONFIG.environment);
        console.log('✅ TapPay SDK initialized');

        // Check if card setup is available
        if (!window.TPDirect.card) {
          console.error('❌ TPDirect.card not available after setup');
          reject(new Error('TPDirect.card not available'));
          return;
        }

        console.log('✅ TPDirect.card available, checking methods...');
        console.log('   TPDirect.card methods:', Object.keys(window.TPDirect.card));

        // Check if required DOM elements exist
        const cardNumberEl = document.getElementById('card-number');
        const cardExpiryEl = document.getElementById('card-expiry');
        const cardCcvEl = document.getElementById('card-ccv');

        if (!cardNumberEl || !cardExpiryEl || !cardCcvEl) {
          console.error('❌ Required DOM elements not found:');
          console.error('   card-number:', !!cardNumberEl);
          console.error('   card-expiry:', !!cardExpiryEl);
          console.error('   card-ccv:', !!cardCcvEl);
          reject(new Error('Required DOM elements not found'));
          return;
        }

        console.log('✅ All required DOM elements found');

        // Setup TapPay card form fields
        const fields = {
          number: {
            element: '#card-number',
            placeholder: '**** **** **** ****'
          },
          expirationDate: {
            element: '#card-expiry',
            placeholder: 'MM / YY'
          },
          ccv: {
            element: '#card-ccv',
            placeholder: 'CVV'
          }
        };

        const styles = {
          'input': {
            'color': 'gray',
            'font-size': '16px',
            'line-height': '24px',
            'font-weight': '300',
            'font-family': 'system-ui, -apple-system, sans-serif'
          },
          'input.ccv': {
            'font-size': '16px'
          },
          ':focus': {
            'color': 'black'
          },
          '.valid': {
            'color': 'green'
          },
          '.invalid': {
            'color': 'red'
          }
        };

        console.log('🔄 Setting up TapPay card with fields and styles...');

        // Setup TapPay card
        try {
          window.TPDirect.card.setup({
            fields: fields,
            styles: styles,
            isMaskCreditCardNumber: true,
            maskCreditCardNumberRange: {
              beginIndex: 6,
              endIndex: 11
            }
          });
          console.log('✅ TapPay card.setup() called successfully');
        } catch (setupError) {
          console.error('❌ Error during card.setup():', setupError);
          reject(setupError);
          return;
        }

        // Add timeout for card readiness
        const readyTimeout = setTimeout(() => {
          console.error('❌ TapPay card form not ready after 10 seconds');
          reject(new Error('TapPay card form setup timeout'));
        }, 10000);

        // Listen for card setup completion
        window.TPDirect.card.onUpdate((update: any) => {
          console.log('📝 TapPay card update:', update);
          setValidationState(update);

          if (update.canGetPrime) {
            clearTimeout(readyTimeout);
            console.log('✅ TapPay card form ready - canGetPrime: true');
            setIsReady(true);
            resolve();
          }
        });

        console.log('✅ TapPay card form setup completed, waiting for ready state...');

      } catch (error) {
        console.error('❌ Card setup error:', error);
        reject(error);
      }
    });
  };

  const handlePrimeSuccess = async (prime: string) => {
    try {
      console.log('🔄 Processing payment with prime token...');

      // Process payment via our API
      const response = await fetch('/api/payments/tappay/process', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          projectId,
          milestoneId,
          amount,
          description,
          clientId,
          designerId,
          paymentType,
          prime: prime
        })
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Payment processing failed');
      }

      const paymentResult = await response.json();
      console.log('✅ Payment processed successfully');

      // Check if this was a mock payment
      if (paymentResult.mock) {
        console.log('🔧 Mock payment completed:', paymentResult);
      }

      onSuccess({
        transactionId: paymentResult.transactionId,
        paymentMethod: 'tappay',
        amount: amount / 100,
        prime: prime,
        data: paymentResult,
        mock: paymentResult.mock || false
      });

    } catch (error) {
      console.error('❌ Payment processing failed:', error);
      const errorMessage = error instanceof Error ? error.message : 'Payment processing failed';
      setError(errorMessage);
      onError(errorMessage);
    } finally {
      setIsProcessing(false);
    }
  };

  const handleRetry = () => {
    console.log('🔄 Retrying TapPay initialization...');
    setError(null);
    setIsLoading(true);
    setIsReady(false);
    setRetryCount(prev => prev + 1);
    initRef.current = false;

    // Clear any existing unmount function
    if (unmountRef.current) {
      try {
        unmountRef.current();
      } catch (e) {
        console.warn('Cleanup warning during retry:', e);
      }
      unmountRef.current = null;
    }

    // Restart initialization
    setTimeout(() => {
      initRef.current = true;
      const initTapPayments = async () => {
        try {
          await loadTapSDK();
          await waitForContainer();
          await setupTapCard();
          console.log('✅ TapPay retry successful');
        } catch (error) {
          console.error('❌ TapPay retry failed:', error);
          const errorMessage = error instanceof Error ? error.message : 'Unknown error';
          setError(`Retry failed: ${errorMessage}`);
        } finally {
          setIsLoading(false);
        }
      };
      initTapPayments();
    }, 100);
  };

  const handleSubmitPayment = async () => {
    if (!isReady || isProcessing) return;

    try {
      setIsProcessing(true);
      setError(null);
      console.log('🔄 Submitting payment...');

      // Get prime token from TapPay
      window.TPDirect.card.getPrime((result: any) => {
        if (result.status !== 0) {
          console.error('❌ Get prime failed:', result);
          setError(result.msg || 'Failed to get payment token');
          setIsProcessing(false);
          return;
        }

        console.log('✅ Prime token obtained:', result.card.prime);
        handlePrimeSuccess(result.card.prime);
      });

    } catch (error) {
      console.error('❌ Payment submission failed:', error);
      const errorMessage = error instanceof Error ? error.message : 'Payment submission failed';
      setError(errorMessage);
      onError(errorMessage);
      setIsProcessing(false);
    }
  };

  // Always render the container, even during loading
  // This ensures the container exists in the DOM when TapPay tries to initialize
  const renderCardContainer = () => (
    <div className="space-y-4">
      <div className="grid grid-cols-1 gap-4">
        {/* Card Number */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Card Number
          </label>
          <div
            id="card-number"
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            style={{ minHeight: '40px' }}
          />
        </div>

        {/* Expiry and CVV */}
        <div className="grid grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Expiry Date
            </label>
            <div
              id="card-expiry"
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              style={{ minHeight: '40px' }}
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              CVV
            </label>
            <div
              id="card-ccv"
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              style={{ minHeight: '40px' }}
            />
          </div>
        </div>
      </div>

      {/* Hidden container for compatibility */}
      <div
        id="tap-card-container"
        ref={containerRef}
        style={{ display: 'none' }}
      />
    </div>
  );

  if (isLoading) {
    return (
      <Card className="w-full max-w-md mx-auto">
        <CardContent className="p-6">
          <div className="flex items-center justify-center space-x-2 mb-4">
            <Loader2 className="h-5 w-5 animate-spin" />
            <span>Loading TapPay...</span>
          </div>

          <div className="text-xs text-gray-500 text-center mb-4">
            <p>Initializing TapPay Taiwan SDK...</p>
            <p>Retry count: {retryCount}</p>
          </div>

          {/* Render container even during loading */}
          <div className="opacity-50 pointer-events-none">
            {renderCardContainer()}
          </div>

          {/* Manual skip button for debugging */}
          <div className="mt-4 text-center">
            <Button
              onClick={() => {
                console.log('🔄 Manual initialization skip/retry');
                setIsLoading(false);
                setError('Manual skip - SDK may not be loaded');
              }}
              variant="outline"
              size="sm"
            >
              Skip Loading (Debug)
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <>
      {/* CSS for TapPay Taiwan SDK styling */}
      <style jsx global>{`
        /* TapPay input field styling */
        #card-number iframe,
        #card-expiry iframe,
        #card-ccv iframe {
          width: 100% !important;
          height: 40px !important;
          border: none !important;
          background: transparent !important;
        }

        /* Ensure TapPay iframes are properly contained */
        #card-number,
        #card-expiry,
        #card-ccv {
          position: relative !important;
          overflow: hidden !important;
        }

        /* Focus states for TapPay fields */
        #card-number:focus-within,
        #card-expiry:focus-within,
        #card-ccv:focus-within {
          border-color: #3b82f6 !important;
          box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.1) !important;
        }
      `}</style>

      <Card className="w-full max-w-md mx-auto">
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <CreditCard className="h-5 w-5" />
            <span>TapPay</span>
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
        {/* Payment Summary */}
        <div className="bg-gray-50 p-4 rounded-lg">
          <div className="flex justify-between items-center">
            <span className="font-medium">Amount:</span>
            <span className="text-lg font-bold">${(amount / 100).toFixed(2)} USD</span>
          </div>
          <div className="text-sm text-gray-600 mt-1">{description}</div>
        </div>

        {/* Test Credentials Warning */}
        {TAP_CONFIG.usingTestCredentials && (
          <Alert className="border-yellow-200 bg-yellow-50">
            <AlertDescription className="text-yellow-800">
              ⚠️ <strong>Development Mode</strong> - Using mock TapPay SDK for localhost development.
              {window.location.hostname === 'localhost' ?
                ' Real TapPay SDK will be used in production.' :
                ' TapPay Taiwan sandbox environment.'
              }
            </AlertDescription>
          </Alert>
        )}

        {/* Error Display */}
        {error && (
          <Alert variant="destructive">
            <AlertDescription>
              <div className="space-y-2">
                <p>{error}</p>
                <Button
                  onClick={handleRetry}
                  variant="outline"
                  size="sm"
                  className="mt-2"
                >
                  Try Again {retryCount > 0 && `(Attempt ${retryCount + 1})`}
                </Button>
              </div>
            </AlertDescription>
          </Alert>
        )}

        {/* Tap Payments Card Form */}
        <div className="space-y-2">
          <div className="mb-2">
            <span className="text-sm font-medium">Card Information</span>
          </div>
          {renderCardContainer()}
        </div>

        {/* Security Notice */}
        <div className="flex items-center space-x-2 text-sm text-gray-600">
          <Shield className="h-4 w-4" />
          <span>Secured by TapPay with 3D Secure</span>
        </div>

        {/* Payment Button */}
        <Button
          onClick={handleSubmitPayment}
          disabled={!isReady || isProcessing}
          className="w-full"
          size="lg"
        >
          {isProcessing ? (
            <>
              <Loader2 className="h-4 w-4 animate-spin mr-2" />
              Processing Payment...
            </>
          ) : (
            <>
              <CreditCard className="h-4 w-4 mr-2" />
              Pay ${(amount / 100).toFixed(2)} USD
            </>
          )}
        </Button>

        {/* Status Indicator */}
        {isReady && !isProcessing && (
          <div className="flex items-center justify-center space-x-2 text-sm text-green-600">
            <CheckCircle className="h-4 w-4" />
            <span>Payment form ready</span>
          </div>
        )}

        {/* Supported Cards */}
        <div className="text-xs text-gray-500 text-center">
          <p>Accepts Visa, Mastercard, American Express</p>
          <p>Supports customers worldwide</p>
        </div>
      </CardContent>
    </Card>
    </>
  );
}
