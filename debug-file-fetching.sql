-- =====================================================
-- DEBUG FILE FETCHING FOR ANNOTATIONS
-- This investigates why files aren't appearing in the annotations tab
-- =====================================================

-- Step 1: Check the specific review and its submission relationship
SELECT 
    'Review-Submission Relationship' as debug_section,
    qr.id as review_id,
    qr.submission_id,
    qr.project_id,
    qr.status as review_status,
    ps.id as actual_submission_id,
    ps.status as submission_status,
    ps.files,
    ps.description,
    ps.submitted_at,
    CASE 
        WHEN qr.submission_id IS NULL THEN '❌ Review has no submission_id'
        WHEN ps.id IS NULL THEN '❌ Submission not found'
        WHEN ps.files IS NULL THEN '❌ Submission has no files'
        WHEN jsonb_array_length(ps.files) = 0 THEN '❌ Files array is empty'
        ELSE '✅ Files should be available'
    END as file_status
FROM quality_reviews_new qr
LEFT JOIN project_submissions ps ON qr.submission_id = ps.id
ORDER BY qr.created_at DESC
LIMIT 5;

-- Step 2: Check file structure in project_submissions
SELECT 
    'File Structure Analysis' as debug_section,
    ps.id as submission_id,
    ps.project_id,
    ps.status,
    ps.files,
    jsonb_array_length(ps.files) as file_count,
    jsonb_pretty(ps.files) as files_formatted
FROM project_submissions ps
WHERE ps.files IS NOT NULL 
AND jsonb_array_length(ps.files) > 0
ORDER BY ps.submitted_at DESC
LIMIT 3;

-- Step 3: Check what file properties are available
SELECT 
    'File Properties Analysis' as debug_section,
    ps.id as submission_id,
    file_item,
    jsonb_object_keys(file_item) as available_keys
FROM project_submissions ps,
     jsonb_array_elements(ps.files) as file_item
WHERE ps.files IS NOT NULL 
AND jsonb_array_length(ps.files) > 0
LIMIT 10;

-- Step 4: Check if there are files with different property names
SELECT 
    'File URL Patterns' as debug_section,
    ps.id as submission_id,
    file_item->>'name' as file_name,
    file_item->>'fileName' as file_fileName,
    file_item->>'url' as file_url,
    file_item->>'fileUrl' as file_fileUrl,
    file_item->>'path' as file_path,
    file_item->>'key' as file_key,
    file_item->>'bucket' as file_bucket
FROM project_submissions ps,
     jsonb_array_elements(ps.files) as file_item
WHERE ps.files IS NOT NULL 
AND jsonb_array_length(ps.files) > 0
LIMIT 5;

-- Step 5: Check for annotatable file types
SELECT 
    'Annotatable Files Check' as debug_section,
    ps.id as submission_id,
    ps.project_id,
    file_item->>'name' as file_name,
    file_item->>'fileName' as alt_file_name,
    COALESCE(file_item->>'name', file_item->>'fileName') as resolved_name,
    COALESCE(file_item->>'url', file_item->>'fileUrl', file_item->>'path') as resolved_url,
    CASE 
        WHEN COALESCE(file_item->>'name', file_item->>'fileName') IS NULL THEN '❌ No file name'
        WHEN COALESCE(file_item->>'url', file_item->>'fileUrl', file_item->>'path') IS NULL THEN '❌ No file URL'
        ELSE 
            CASE 
                WHEN LOWER(RIGHT(COALESCE(file_item->>'name', file_item->>'fileName'), 4)) IN ('.jpg', '.png', '.gif', '.pdf', '.dwg', '.dxf') 
                OR LOWER(RIGHT(COALESCE(file_item->>'name', file_item->>'fileName'), 5)) IN ('.jpeg') 
                THEN '✅ Annotatable'
                ELSE '⚠️ Not annotatable'
            END
    END as annotation_status
FROM project_submissions ps,
     jsonb_array_elements(ps.files) as file_item
WHERE ps.files IS NOT NULL 
AND jsonb_array_length(ps.files) > 0
ORDER BY ps.submitted_at DESC
LIMIT 10;

-- Step 6: Check Cloudflare R2 URL patterns
SELECT 
    'R2 URL Patterns' as debug_section,
    ps.id as submission_id,
    file_item->>'name' as file_name,
    COALESCE(file_item->>'url', file_item->>'fileUrl', file_item->>'path') as file_url,
    CASE 
        WHEN COALESCE(file_item->>'url', file_item->>'fileUrl', file_item->>'path') LIKE '%r2.dev%' THEN '✅ R2 Public URL'
        WHEN COALESCE(file_item->>'url', file_item->>'fileUrl', file_item->>'path') LIKE '%cloudflarestorage.com%' THEN '✅ R2 Storage URL'
        WHEN COALESCE(file_item->>'url', file_item->>'fileUrl', file_item->>'path') LIKE 'project-files/%' THEN '⚠️ Relative path (needs R2 base URL)'
        ELSE '❌ Unknown URL pattern'
    END as url_type
FROM project_submissions ps,
     jsonb_array_elements(ps.files) as file_item
WHERE ps.files IS NOT NULL 
AND jsonb_array_length(ps.files) > 0
LIMIT 10;

-- Step 7: Check if there are any submissions without quality reviews
SELECT 
    'Submissions Without Reviews' as debug_section,
    ps.id as submission_id,
    ps.project_id,
    ps.status,
    ps.submitted_at,
    jsonb_array_length(ps.files) as file_count,
    qr.id as review_id,
    CASE 
        WHEN qr.id IS NULL THEN '❌ No quality review created'
        ELSE '✅ Has quality review'
    END as review_status
FROM project_submissions ps
LEFT JOIN quality_reviews_new qr ON qr.submission_id = ps.id
WHERE ps.files IS NOT NULL 
AND jsonb_array_length(ps.files) > 0
ORDER BY ps.submitted_at DESC
LIMIT 5;

-- Step 8: Show environment info for R2 URLs
SELECT 
    'Environment Check' as debug_section,
    'Check if CLOUDFLARE_R2_PUBLIC_URL is set correctly' as note,
    'Expected format: https://pub-xxxxx.r2.dev' as expected_format,
    'Files should be accessible at: {R2_PUBLIC_URL}/{bucket}/{key}' as url_structure;
