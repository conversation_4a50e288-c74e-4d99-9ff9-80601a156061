-- Fix Quality Standards Project Types Migration
-- This script updates the incorrect project types in quality standards to match actual platform services

-- 1. UPDATE EXISTING QUALITY STANDARDS WITH CORRECT PROJECT TYPES
UPDATE quality_standards 
SET applicable_project_types = ARRAY[
  'Residential - Single Family',
  'Residential - Multi-Family', 
  'Commercial - Office',
  'Commercial - Retail',
  'Commercial - Hospitality',
  'Institutional',
  'Industrial',
  'Mixed-Use',
  'Landscape',
  'Interior Design',
  'Other',
  'all'
]
WHERE applicable_project_types && ARRAY['logo_design', 'web_design', 'branding', 'print_design', 'app_design'];

-- 2. UPDATE QUALITY STANDARD TEMPLATES WITH CORRECT PROJECT TYPES
UPDATE quality_standard_templates 
SET standards = (
  SELECT jsonb_agg(
    CASE 
      WHEN standard_obj->>'standard_id' = 'Visual Hierarchy' THEN 
        jsonb_build_object(
          'standard_id', 'Visual Hierarchy',
          'weight', COALESCE((standard_obj->>'weight')::integer, 8)
        )
      WHEN standard_obj->>'standard_id' = 'Color Harmony' THEN 
        jsonb_build_object(
          'standard_id', 'Color Harmony',
          'weight', COALESCE((standard_obj->>'weight')::integer, 9)
        )
      WHEN standard_obj->>'standard_id' = 'Typography Excellence' THEN 
        jsonb_build_object(
          'standard_id', 'Typography Excellence',
          'weight', COALESCE((standard_obj->>'weight')::integer, 7)
        )
      WHEN standard_obj->>'standard_id' = 'File Format Compliance' THEN 
        jsonb_build_object(
          'standard_id', 'File Format Compliance',
          'weight', COALESCE((standard_obj->>'weight')::integer, 10)
        )
      WHEN standard_obj->>'standard_id' = 'Resolution and Size Standards' THEN 
        jsonb_build_object(
          'standard_id', 'Resolution and Size Standards',
          'weight', COALESCE((standard_obj->>'weight')::integer, 9)
        )
      WHEN standard_obj->>'standard_id' = 'File Organization' THEN 
        jsonb_build_object(
          'standard_id', 'File Organization',
          'weight', COALESCE((standard_obj->>'weight')::integer, 6)
        )
      WHEN standard_obj->>'standard_id' = 'Brand Color Compliance' THEN 
        jsonb_build_object(
          'standard_id', 'Brand Color Compliance',
          'weight', COALESCE((standard_obj->>'weight')::integer, 10)
        )
      WHEN standard_obj->>'standard_id' = 'Typography Compliance' THEN 
        jsonb_build_object(
          'standard_id', 'Typography Compliance',
          'weight', COALESCE((standard_obj->>'weight')::integer, 8)
        )
      WHEN standard_obj->>'standard_id' = 'Logo Usage Compliance' THEN 
        jsonb_build_object(
          'standard_id', 'Logo Usage Compliance',
          'weight', COALESCE((standard_obj->>'weight')::integer, 10)
        )
      WHEN standard_obj->>'standard_id' = 'Usability' THEN 
        jsonb_build_object(
          'standard_id', 'Usability',
          'weight', COALESCE((standard_obj->>'weight')::integer, 9)
        )
      WHEN standard_obj->>'standard_id' = 'Accessibility' THEN 
        jsonb_build_object(
          'standard_id', 'Accessibility',
          'weight', COALESCE((standard_obj->>'weight')::integer, 9)
        )
      WHEN standard_obj->>'standard_id' = 'Responsive Design' THEN 
        jsonb_build_object(
          'standard_id', 'Responsive Design',
          'weight', COALESCE((standard_obj->>'weight')::integer, 10)
        )
      ELSE standard_obj
    END
  )
  FROM jsonb_array_elements(standards) AS standard_obj
)
WHERE project_type IN ('logo_design', 'web_design');

-- 3. UPDATE TEMPLATE PROJECT TYPES
UPDATE quality_standard_templates 
SET project_type = CASE 
  WHEN project_type = 'logo_design' THEN 'Commercial - Retail'
  WHEN project_type = 'web_design' THEN 'Commercial - Office'
  WHEN project_type = 'branding' THEN 'Commercial - Retail'
  WHEN project_type = 'print_design' THEN 'Commercial - Office'
  WHEN project_type = 'app_design' THEN 'Commercial - Office'
  ELSE project_type
END
WHERE project_type IN ('logo_design', 'web_design', 'branding', 'print_design', 'app_design');

-- 4. ADD NEW ARCHITECTURAL-SPECIFIC QUALITY STANDARDS
INSERT INTO quality_standards (standard_name, description, category, subcategory, is_mandatory, weight, passing_threshold, criteria, applicable_project_types, is_active) VALUES

-- Architectural Design Standards
('Structural Integrity', 'Evaluation of structural design soundness and safety compliance', 'Technical Quality', 'Structural', true, 10, 4.0, 
 '[{"criterion": "Load calculations accuracy", "description": "Verify structural load calculations are correct and complete"}, 
   {"criterion": "Building code compliance", "description": "Ensure design meets local building codes and regulations"}, 
   {"criterion": "Material specifications", "description": "Check material specifications are appropriate and detailed"}]',
 ARRAY['Residential - Single Family', 'Residential - Multi-Family', 'Commercial - Office', 'Commercial - Retail', 'Institutional', 'Industrial', 'Mixed-Use'], true),

('Space Planning Efficiency', 'Assessment of space utilization and flow optimization', 'Design Fundamentals', 'Planning', true, 9, 3.5,
 '[{"criterion": "Circulation efficiency", "description": "Evaluate traffic flow and movement patterns"}, 
   {"criterion": "Space allocation", "description": "Check if spaces are appropriately sized for their function"}, 
   {"criterion": "Accessibility compliance", "description": "Ensure ADA compliance and universal design principles"}]',
 ARRAY['Residential - Single Family', 'Residential - Multi-Family', 'Commercial - Office', 'Commercial - Retail', 'Institutional', 'Mixed-Use', 'Interior Design'], true),

('Environmental Sustainability', 'Evaluation of sustainable design practices and energy efficiency', 'Performance', 'Sustainability', false, 8, 3.0,
 '[{"criterion": "Energy efficiency measures", "description": "Review HVAC, lighting, and insulation strategies"}, 
   {"criterion": "Sustainable materials", "description": "Check use of eco-friendly and locally sourced materials"}, 
   {"criterion": "Water conservation", "description": "Evaluate water-saving fixtures and systems"}]',
 ARRAY['Residential - Single Family', 'Residential - Multi-Family', 'Commercial - Office', 'Commercial - Retail', 'Institutional', 'Industrial', 'Mixed-Use'], true),

('Landscape Integration', 'Assessment of landscape design integration with architecture', 'Design Fundamentals', 'Landscape', false, 7, 3.0,
 '[{"criterion": "Site context harmony", "description": "Evaluate how design responds to site conditions"}, 
   {"criterion": "Plant selection appropriateness", "description": "Check climate-appropriate and native plant choices"}, 
   {"criterion": "Drainage and grading", "description": "Review site drainage and grading solutions"}]',
 ARRAY['Landscape', 'Residential - Single Family', 'Commercial - Office', 'Mixed-Use'], true),

('Construction Documentation', 'Quality of technical drawings and specifications', 'Technical Quality', 'Documentation', true, 9, 4.0,
 '[{"criterion": "Drawing completeness", "description": "Verify all necessary drawings and details are included"}, 
   {"criterion": "Specification accuracy", "description": "Check technical specifications are detailed and accurate"}, 
   {"criterion": "Coordination between trades", "description": "Ensure architectural, structural, and MEP coordination"}]',
 ARRAY['Residential - Single Family', 'Residential - Multi-Family', 'Commercial - Office', 'Commercial - Retail', 'Institutional', 'Industrial', 'Mixed-Use'], true);

-- 5. UPDATE EXISTING GENERIC STANDARDS TO BE MORE ARCHITECTURAL
UPDATE quality_standards 
SET 
  description = CASE 
    WHEN standard_name = 'Visual Hierarchy' THEN 'Evaluation of architectural composition, proportion, and visual organization'
    WHEN standard_name = 'Color Harmony' THEN 'Assessment of material palette, color coordination, and aesthetic coherence'
    WHEN standard_name = 'Typography Excellence' THEN 'Quality of text elements, signage, and graphic communication in architectural context'
    ELSE description
  END,
  criteria = CASE 
    WHEN standard_name = 'Visual Hierarchy' THEN 
      '[{"criterion": "Architectural composition", "description": "Evaluate building massing, proportion, and visual balance"}, 
        {"criterion": "Facade organization", "description": "Check hierarchy of architectural elements and openings"}, 
        {"criterion": "Material transitions", "description": "Review how different materials and textures are organized"}]'
    WHEN standard_name = 'Color Harmony' THEN 
      '[{"criterion": "Material palette coherence", "description": "Evaluate consistency and harmony of material choices"}, 
        {"criterion": "Color coordination", "description": "Check color relationships between different building elements"}, 
        {"criterion": "Context sensitivity", "description": "Assess how material choices respond to site and surroundings"}]'
    WHEN standard_name = 'Typography Excellence' THEN 
      '[{"criterion": "Signage integration", "description": "Evaluate how signage integrates with architectural design"}, 
        {"criterion": "Wayfinding clarity", "description": "Check clarity and effectiveness of directional elements"}, 
        {"criterion": "Brand consistency", "description": "Ensure consistent application of client brand elements"}]'
    ELSE criteria
  END
WHERE standard_name IN ('Visual Hierarchy', 'Color Harmony', 'Typography Excellence');

-- 6. CREATE NEW ARCHITECTURAL TEMPLATE
INSERT INTO quality_standard_templates (name, description, project_type, standards, created_by) VALUES
('Residential Architecture Template', 'Comprehensive quality checklist for residential architectural projects', 'Residential - Single Family',
 '[{"standard_id": "Structural Integrity", "weight": 10}, 
   {"standard_id": "Space Planning Efficiency", "weight": 9}, 
   {"standard_id": "Visual Hierarchy", "weight": 8}, 
   {"standard_id": "Environmental Sustainability", "weight": 8}, 
   {"standard_id": "Construction Documentation", "weight": 9}, 
   {"standard_id": "File Format Compliance", "weight": 7}]',
 NULL),

('Commercial Architecture Template', 'Quality standards for commercial architectural projects', 'Commercial - Office',
 '[{"standard_id": "Structural Integrity", "weight": 10}, 
   {"standard_id": "Space Planning Efficiency", "weight": 9}, 
   {"standard_id": "Visual Hierarchy", "weight": 8}, 
   {"standard_id": "Environmental Sustainability", "weight": 9}, 
   {"standard_id": "Construction Documentation", "weight": 10}, 
   {"standard_id": "Accessibility", "weight": 9}, 
   {"standard_id": "File Format Compliance", "weight": 7}]',
 NULL);

-- 7. COMMENTS FOR DOCUMENTATION
COMMENT ON TABLE quality_standards IS 'Quality standards for architectural project reviews with proper project type classifications';
COMMENT ON COLUMN quality_standards.applicable_project_types IS 'Array of project types that match the actual architectural services offered by the platform';

-- 8. VERIFICATION QUERY (for testing)
-- SELECT standard_name, applicable_project_types FROM quality_standards WHERE 'all' = ANY(applicable_project_types);
