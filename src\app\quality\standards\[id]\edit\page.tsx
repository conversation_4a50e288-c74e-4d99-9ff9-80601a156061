'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Switch } from '@/components/ui/switch';
import { useOptimizedAuth } from '@/hooks/useOptimizedAuth';
import { supabase } from '@/lib/supabase';
import { 
  Save, 
  ArrowLeft, 
  AlertTriangle, 
  Plus, 
  Trash2,
  Star,
  Info
} from 'lucide-react';

interface QualityStandard {
  id: string;
  standard_name: string;
  description: string;
  category: string;
  subcategory?: string;
  is_mandatory: boolean;
  weight: number;
  passing_threshold?: number;
  criteria: Array<{
    criterion: string;
    description: string;
  }>;
  applicable_project_types: string[];
  is_active: boolean;
}

interface EditQualityStandardPageProps {
  params: { id: string };
}

export default function EditQualityStandardPage({ params }: EditQualityStandardPageProps) {
  const { user, profile, loading: authLoading } = useOptimizedAuth();
  const router = useRouter();
  const [standard, setStandard] = useState<QualityStandard | null>(null);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [errors, setErrors] = useState<Record<string, string>>({});

  const categories = [
    'Design Fundamentals',
    'Technical Quality', 
    'Brand Compliance',
    'User Experience',
    'Content Quality',
    'Performance'
  ];

  const projectTypes = [
    'Residential - Single Family',
    'Residential - Multi-Family',
    'Commercial - Office',
    'Commercial - Retail',
    'Commercial - Hospitality',
    'Institutional',
    'Industrial',
    'Mixed-Use',
    'Landscape',
    'Interior Design',
    'Other',
    'all'
  ];

  useEffect(() => {
    if (user && ['quality_team', 'admin'].includes(profile?.role)) {
      fetchStandard();
    }
  }, [user, profile, params.id]);

  const fetchStandard = async () => {
    try {
      const { data, error } = await supabase
        .from('quality_standards')
        .select('*')
        .eq('id', params.id)
        .single();

      if (error) throw error;

      // Parse criteria if it's a JSON string
      let criteria = [];
      try {
        if (typeof data.criteria === 'string') {
          criteria = JSON.parse(data.criteria);
        } else if (Array.isArray(data.criteria)) {
          criteria = data.criteria;
        }
      } catch (e) {
        console.error('Error parsing criteria:', e);
        criteria = [];
      }

      setStandard({
        ...data,
        criteria,
        applicable_project_types: data.applicable_project_types || ['all']
      });
    } catch (error) {
      console.error('Error fetching standard:', error);
      router.push('/quality/standards');
    } finally {
      setLoading(false);
    }
  };

  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!standard?.standard_name?.trim()) {
      newErrors.standard_name = 'Standard name is required';
    }

    if (!standard?.description?.trim()) {
      newErrors.description = 'Description is required';
    }

    if (!standard?.category) {
      newErrors.category = 'Category is required';
    }

    if (!standard?.weight || standard.weight < 1 || standard.weight > 10) {
      newErrors.weight = 'Weight must be between 1 and 10';
    }

    if (standard?.criteria?.length === 0) {
      newErrors.criteria = 'At least one criterion is required';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSave = async () => {
    if (!standard || !validateForm()) return;

    setSaving(true);
    try {
      const { error } = await supabase
        .from('quality_standards')
        .update({
          standard_name: standard.standard_name,
          description: standard.description,
          category: standard.category,
          subcategory: standard.subcategory || null,
          is_mandatory: standard.is_mandatory,
          weight: standard.weight,
          passing_threshold: standard.passing_threshold || 3.0,
          criteria: JSON.stringify(standard.criteria),
          applicable_project_types: standard.applicable_project_types,
          is_active: standard.is_active,
          updated_at: new Date().toISOString()
        })
        .eq('id', params.id);

      if (error) throw error;

      router.push('/quality/standards');
    } catch (error) {
      console.error('Error saving standard:', error);
      setErrors({ general: 'Failed to save standard. Please try again.' });
    } finally {
      setSaving(false);
    }
  };

  const addCriterion = () => {
    if (!standard) return;
    
    setStandard({
      ...standard,
      criteria: [
        ...standard.criteria,
        { criterion: '', description: '' }
      ]
    });
  };

  const removeCriterion = (index: number) => {
    if (!standard) return;
    
    setStandard({
      ...standard,
      criteria: standard.criteria.filter((_, i) => i !== index)
    });
  };

  const updateCriterion = (index: number, field: 'criterion' | 'description', value: string) => {
    if (!standard) return;
    
    const updatedCriteria = [...standard.criteria];
    updatedCriteria[index] = {
      ...updatedCriteria[index],
      [field]: value
    };
    
    setStandard({
      ...standard,
      criteria: updatedCriteria
    });
  };

  const toggleProjectType = (type: string) => {
    if (!standard) return;
    
    const currentTypes = standard.applicable_project_types;
    const updatedTypes = currentTypes.includes(type)
      ? currentTypes.filter(t => t !== type)
      : [...currentTypes, type];
    
    setStandard({
      ...standard,
      applicable_project_types: updatedTypes
    });
  };

  if (authLoading || loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (!user || !['quality_team', 'admin'].includes(profile?.role)) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <Card className="w-96">
          <CardContent className="p-6 text-center">
            <AlertTriangle className="h-12 w-12 text-red-500 mx-auto mb-4" />
            <h2 className="text-xl font-semibold mb-2">Access Denied</h2>
            <p className="text-gray-600">You don't have permission to edit quality standards.</p>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (!standard) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <Card className="w-96">
          <CardContent className="p-6 text-center">
            <AlertTriangle className="h-12 w-12 text-red-500 mx-auto mb-4" />
            <h2 className="text-xl font-semibold mb-2">Standard Not Found</h2>
            <p className="text-gray-600">The quality standard you're looking for doesn't exist.</p>
            <Button 
              onClick={() => router.push('/quality/standards')} 
              className="mt-4"
            >
              Back to Standards
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      {/* Header */}
      <div className="flex items-center gap-4 mb-8">
        <Button
          variant="outline"
          onClick={() => router.push('/quality/standards')}
          className="flex items-center gap-2"
        >
          <ArrowLeft className="h-4 w-4" />
          Back to Standards
        </Button>
        <div>
          <h1 className="text-3xl font-bold">Edit Quality Standard</h1>
          <p className="text-gray-600">Modify quality standard criteria and settings</p>
        </div>
      </div>

      {errors.general && (
        <Card className="mb-6 border-red-200 bg-red-50">
          <CardContent className="p-4">
            <div className="flex items-center gap-2 text-red-700">
              <AlertTriangle className="h-5 w-5" />
              {errors.general}
            </div>
          </CardContent>
        </Card>
      )}

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Main Form */}
        <div className="lg:col-span-2 space-y-6">
          {/* Basic Information */}
          <Card>
            <CardHeader>
              <CardTitle>Basic Information</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Label htmlFor="standard_name">Standard Name *</Label>
                <Input
                  id="standard_name"
                  value={standard.standard_name}
                  onChange={(e) => setStandard({ ...standard, standard_name: e.target.value })}
                  placeholder="Enter standard name"
                  className={errors.standard_name ? 'border-red-500' : ''}
                />
                {errors.standard_name && (
                  <p className="text-sm text-red-500 mt-1">{errors.standard_name}</p>
                )}
              </div>

              <div>
                <Label htmlFor="description">Description *</Label>
                <Textarea
                  id="description"
                  value={standard.description}
                  onChange={(e) => setStandard({ ...standard, description: e.target.value })}
                  placeholder="Describe what this standard evaluates"
                  rows={3}
                  className={errors.description ? 'border-red-500' : ''}
                />
                {errors.description && (
                  <p className="text-sm text-red-500 mt-1">{errors.description}</p>
                )}
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="category">Category *</Label>
                  <select
                    id="category"
                    value={standard.category}
                    onChange={(e) => setStandard({ ...standard, category: e.target.value })}
                    className={`w-full px-3 py-2 border rounded-md ${errors.category ? 'border-red-500' : 'border-gray-300'}`}
                  >
                    <option value="">Select category</option>
                    {categories.map(cat => (
                      <option key={cat} value={cat}>{cat}</option>
                    ))}
                  </select>
                  {errors.category && (
                    <p className="text-sm text-red-500 mt-1">{errors.category}</p>
                  )}
                </div>

                <div>
                  <Label htmlFor="subcategory">Subcategory</Label>
                  <Input
                    id="subcategory"
                    value={standard.subcategory || ''}
                    onChange={(e) => setStandard({ ...standard, subcategory: e.target.value })}
                    placeholder="Optional subcategory"
                  />
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Criteria Management */}
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <CardTitle>Evaluation Criteria</CardTitle>
                <Button
                  onClick={addCriterion}
                  size="sm"
                  className="flex items-center gap-2"
                >
                  <Plus className="h-4 w-4" />
                  Add Criterion
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              {errors.criteria && (
                <div className="mb-4 p-3 bg-red-50 border border-red-200 rounded-md">
                  <p className="text-sm text-red-600">{errors.criteria}</p>
                </div>
              )}

              <div className="space-y-4">
                {standard.criteria.map((criterion, index) => (
                  <div key={index} className="border rounded-lg p-4 bg-gray-50">
                    <div className="flex items-start justify-between mb-3">
                      <div className="flex items-center gap-2">
                        <Star className="h-4 w-4 text-yellow-500" />
                        <span className="font-medium text-sm">Criterion {index + 1}</span>
                      </div>
                      <Button
                        onClick={() => removeCriterion(index)}
                        size="sm"
                        variant="outline"
                        className="text-red-600 hover:text-red-700 hover:bg-red-50"
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>

                    <div className="space-y-3">
                      <div>
                        <Label htmlFor={`criterion-${index}`}>Criterion Title *</Label>
                        <Input
                          id={`criterion-${index}`}
                          value={criterion.criterion}
                          onChange={(e) => updateCriterion(index, 'criterion', e.target.value)}
                          placeholder="What should be evaluated?"
                        />
                      </div>

                      <div>
                        <Label htmlFor={`description-${index}`}>Description</Label>
                        <Textarea
                          id={`description-${index}`}
                          value={criterion.description}
                          onChange={(e) => updateCriterion(index, 'description', e.target.value)}
                          placeholder="Detailed explanation of this criterion"
                          rows={2}
                        />
                      </div>
                    </div>
                  </div>
                ))}

                {standard.criteria.length === 0 && (
                  <div className="text-center py-8 text-gray-500">
                    <Info className="h-12 w-12 mx-auto mb-4" />
                    <p>No criteria defined yet</p>
                    <p className="text-sm mt-2">Add criteria to define what should be evaluated</p>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Settings Sidebar */}
        <div className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Settings</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between">
                <div>
                  <Label>Mandatory Standard</Label>
                  <p className="text-sm text-gray-600">Must pass for approval</p>
                </div>
                <Switch
                  checked={standard.is_mandatory}
                  onCheckedChange={(checked) => setStandard({ ...standard, is_mandatory: checked })}
                />
              </div>

              <div className="flex items-center justify-between">
                <div>
                  <Label>Active</Label>
                  <p className="text-sm text-gray-600">Include in reviews</p>
                </div>
                <Switch
                  checked={standard.is_active}
                  onCheckedChange={(checked) => setStandard({ ...standard, is_active: checked })}
                />
              </div>

              <div>
                <Label htmlFor="weight">Weight (1-10) *</Label>
                <Input
                  id="weight"
                  type="number"
                  min="1"
                  max="10"
                  value={standard.weight}
                  onChange={(e) => setStandard({ ...standard, weight: Number.parseInt(e.target.value) || 1 })}
                  className={errors.weight ? 'border-red-500' : ''}
                />
                {errors.weight && (
                  <p className="text-sm text-red-500 mt-1">{errors.weight}</p>
                )}
              </div>

              <div>
                <Label htmlFor="passing_threshold">Passing Threshold</Label>
                <Input
                  id="passing_threshold"
                  type="number"
                  min="1"
                  max="5"
                  step="0.1"
                  value={standard.passing_threshold || 3.0}
                  onChange={(e) => setStandard({ ...standard, passing_threshold: Number.parseFloat(e.target.value) || 3.0 })}
                />
                <p className="text-sm text-gray-600 mt-1">Minimum score to pass (1.0-5.0)</p>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Project Types</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                {projectTypes.map(type => (
                  <div key={type} className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      id={type}
                      checked={standard.applicable_project_types.includes(type)}
                      onChange={() => toggleProjectType(type)}
                      className="rounded"
                    />
                    <Label htmlFor={type} className="text-sm">
                      {type}
                    </Label>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Action Buttons */}
      <div className="flex items-center justify-end gap-4 mt-8">
        <Button
          variant="outline"
          onClick={() => router.push('/quality/standards')}
        >
          Cancel
        </Button>
        <Button
          onClick={handleSave}
          disabled={saving}
          className="flex items-center gap-2"
        >
          <Save className="h-4 w-4" />
          {saving ? 'Saving...' : 'Save Changes'}
        </Button>
      </div>
    </div>
  );
}
