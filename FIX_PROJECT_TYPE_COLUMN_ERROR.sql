-- Fix for "column 'project_type' does not exist" error
-- The trigger is trying to access project_type from projects table but it doesn't exist

-- Step 1: Drop the problematic trigger
DROP TRIGGER IF EXISTS assign_quality_standards_trigger ON quality_reviews_new;

-- Step 2: Check if project_type column exists in projects table
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'projects' AND column_name = 'project_type'
    ) THEN
        RAISE NOTICE 'project_type column does not exist in projects table';
        -- Add the column if it doesn't exist (optional)
        -- ALTER TABLE projects ADD COLUMN project_type VARCHAR(100) DEFAULT 'general';
    ELSE
        RAISE NOTICE 'project_type column exists in projects table';
    END IF;
END $$;

-- Step 3: Create a fixed trigger function that handles missing project_type column
CREATE OR REPLACE FUNCTION trigger_assign_quality_standards_fixed()
RETURNS TRIGGER AS $$
DECLARE
    v_project_type VARCHAR(100) := 'general'; -- Default value
BEGIN
    -- Try to get project type from projects table, use default if column doesn't exist
    BEGIN
        SELECT COALESCE(project_type, 'general') INTO v_project_type
        FROM projects
        WHERE id = NEW.project_id;
    EXCEPTION
        WHEN undefined_column THEN
            -- If project_type column doesn't exist, use default
            v_project_type := 'general';
            RAISE NOTICE 'project_type column not found, using default: %', v_project_type;
    END;
    
    -- Only assign standards if the function exists
    BEGIN
        PERFORM assign_quality_standards_to_review(NEW.id, v_project_type);
    EXCEPTION
        WHEN undefined_function THEN
            RAISE NOTICE 'assign_quality_standards_to_review function not found, skipping';
    END;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Step 4: Alternative - completely disable the trigger if you don't need automatic quality standards assignment
-- (Uncomment the next line if you want to disable the trigger completely)
-- -- No trigger recreation - quality standards assignment disabled

-- Step 5: Or recreate the trigger with the fixed function (comment out if you prefer to disable)
CREATE TRIGGER assign_quality_standards_trigger_fixed
    AFTER INSERT ON quality_reviews_new
    FOR EACH ROW
    EXECUTE FUNCTION trigger_assign_quality_standards_fixed();

-- Step 6: Test the fix
SELECT 'Quality standards trigger fix completed!' as status;
