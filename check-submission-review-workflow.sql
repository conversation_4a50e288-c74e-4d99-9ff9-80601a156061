-- =====================================================
-- CHECK SUBMISSION-REVIEW WORKFLOW
-- This checks the relationship between submissions and reviews
-- =====================================================

-- Check the specific submission that was causing issues
SELECT 
    'Specific Submission Check' as analysis,
    ps.id as submission_id,
    ps.title,
    ps.status as submission_status,
    ps.project_id,
    ps.designer_id,
    ps.submitted_at,
    ps.submission_source,
    qr.id as review_id,
    qr.status as review_status,
    qr.submission_id as review_submission_id,
    CASE 
        WHEN qr.id IS NULL THEN '❌ No review exists - will be created'
        WHEN qr.submission_id = ps.id THEN '✅ Review exists and linked correctly'
        ELSE '⚠️ Review exists but wrong submission_id'
    END as workflow_status
FROM project_submissions ps
LEFT JOIN quality_reviews_new qr ON qr.submission_id = ps.id
WHERE ps.id = '0d167e18-75b9-4cfe-8a37-b1e8f539e3f0';

-- Check all submissions and their review status
SELECT 
    'All Submissions Review Status' as analysis,
    ps.id as submission_id,
    LEFT(ps.title, 30) as title_preview,
    ps.status as submission_status,
    ps.submission_source,
    qr.id as review_id,
    qr.status as review_status,
    CASE 
        WHEN qr.id IS NULL THEN '❌ No review'
        ELSE '✅ Has review'
    END as has_review,
    ps.submitted_at
FROM project_submissions ps
LEFT JOIN quality_reviews_new qr ON qr.submission_id = ps.id
ORDER BY ps.submitted_at DESC
LIMIT 10;

-- Check for orphaned reviews (reviews without submissions)
SELECT 
    'Orphaned Reviews Check' as analysis,
    qr.id as review_id,
    qr.submission_id,
    qr.status as review_status,
    ps.id as actual_submission_id,
    CASE 
        WHEN ps.id IS NULL THEN '❌ Review has no matching submission'
        ELSE '✅ Review has valid submission'
    END as submission_exists,
    qr.created_at
FROM quality_reviews_new qr
LEFT JOIN project_submissions ps ON qr.submission_id = ps.id
ORDER BY qr.created_at DESC
LIMIT 10;

-- Summary statistics
SELECT 
    'Workflow Statistics' as summary,
    (SELECT COUNT(*) FROM project_submissions) as total_submissions,
    (SELECT COUNT(*) FROM quality_reviews_new) as total_reviews,
    (SELECT COUNT(*) FROM project_submissions ps 
     WHERE EXISTS (SELECT 1 FROM quality_reviews_new qr WHERE qr.submission_id = ps.id)) as submissions_with_reviews,
    (SELECT COUNT(*) FROM quality_reviews_new qr 
     WHERE NOT EXISTS (SELECT 1 FROM project_submissions ps WHERE ps.id = qr.submission_id)) as orphaned_reviews;

-- Check the expected workflow for the problematic submission
SELECT 
    'Expected Workflow for Problematic Submission' as workflow,
    'Submission ID: 0d167e18-75b9-4cfe-8a37-b1e8f539e3f0' as step1,
    'Should find review WHERE submission_id = this ID' as step2,
    'If no review found, create new review with submission_id = this ID' as step3,
    'Navigate to /quality/reviews/{review.id}' as step4,
    'Review page loads review by review.id and shows submission details via submission_id' as step5;

-- Show what data is available for creating a review
SELECT 
    'Data Available for Review Creation' as info,
    ps.id as submission_id,
    ps.project_id,
    ps.designer_id,
    p.title as project_title,
    prof.full_name as designer_name,
    ps.status as submission_status,
    ps.submitted_at
FROM project_submissions ps
LEFT JOIN projects p ON ps.project_id = p.id
LEFT JOIN profiles prof ON ps.designer_id = prof.id
WHERE ps.id = '0d167e18-75b9-4cfe-8a37-b1e8f539e3f0';
