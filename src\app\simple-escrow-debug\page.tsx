"use client";

import { useState, useEffect } from "react";
import { useOptimizedAuth } from "@/hooks/useOptimizedAuth";
import { supabase } from "@/lib/supabase";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { RefreshCw, Database } from "lucide-react";

export default function SimpleEscrowDebugPage() {
  const { user, profile } = useOptimizedAuth();
  const [loading, setLoading] = useState(false);
  const [data, setData] = useState<any>({});

  const fetchAllData = async () => {
    setLoading(true);
    const results: any = {};

    try {
      // 1. Get all completed PayPal payments
      const { data: payments, error: paymentsError } = await supabase
        .from('payments')
        .select('*')
        .eq('payment_method', 'paypal')
        .eq('status', 'completed')
        .order('created_at', { ascending: false });

      results.payments = { data: payments, error: paymentsError, count: payments?.length || 0 };

      // 2. Get all transactions
      const { data: transactions, error: transactionsError } = await supabase
        .from('transactions')
        .select('*')
        .order('created_at', { ascending: false })
        .limit(10);

      results.transactions = { data: transactions, error: transactionsError, count: transactions?.length || 0 };

      // 3. Get all escrow accounts
      const { data: escrowAccounts, error: escrowAccountsError } = await supabase
        .from('escrow_accounts')
        .select('*')
        .order('created_at', { ascending: false });

      results.escrowAccounts = { data: escrowAccounts, error: escrowAccountsError, count: escrowAccounts?.length || 0 };

      // 4. Get all escrow holds
      const { data: escrowHolds, error: escrowHoldsError } = await supabase
        .from('escrow_holds')
        .select('*')
        .order('created_at', { ascending: false });

      results.escrowHolds = { data: escrowHolds, error: escrowHoldsError, count: escrowHolds?.length || 0 };

      // 5. Get all paypal escrow holds
      const { data: paypalEscrowHolds, error: paypalEscrowHoldsError } = await supabase
        .from('paypal_escrow_holds')
        .select('*')
        .order('created_at', { ascending: false });

      results.paypalEscrowHolds = { data: paypalEscrowHolds, error: paypalEscrowHoldsError, count: paypalEscrowHolds?.length || 0 };

      // 6. Get project milestones that are paid
      const { data: paidMilestones, error: milestonesError } = await supabase
        .from('project_milestones')
        .select('*, projects(title)')
        .eq('status', 'paid')
        .order('paid_at', { ascending: false });

      results.paidMilestones = { data: paidMilestones, error: milestonesError, count: paidMilestones?.length || 0 };

    } catch (error) {
      console.error('Error fetching data:', error);
      results.error = error;
    }

    setData(results);
    setLoading(false);
  };

  useEffect(() => {
    if (user && (profile?.role === 'admin' || profile?.role === 'manager')) {
      fetchAllData();
    }
  }, [user, profile]);

  if (!user || (profile?.role !== 'admin' && profile?.role !== 'manager')) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-900 mb-4">Access Denied</h1>
          <p className="text-gray-600">Only admins and managers can access this debug page.</p>
        </div>
      </div>
    );
  }

  const renderTable = (tableName: string, tableData: any) => {
    if (tableData.error) {
      return (
        <Card key={tableName}>
          <CardHeader>
            <CardTitle className="flex items-center gap-2 text-red-600">
              <Database className="h-5 w-5" />
              {tableName.toUpperCase()} - ERROR
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-red-600 text-sm">
              <strong>Error:</strong> {tableData.error.message}
            </div>
          </CardContent>
        </Card>
      );
    }

    return (
      <Card key={tableName}>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Database className="h-5 w-5" />
            {tableName.toUpperCase()} ({tableData.count} records)
          </CardTitle>
        </CardHeader>
        <CardContent>
          {tableData.count === 0 ? (
            <div className="text-gray-500 text-sm">No records found</div>
          ) : (
            <div className="space-y-3 max-h-96 overflow-y-auto">
              {tableData.data.slice(0, 5).map((record: any, index: number) => (
                <div key={index} className="bg-gray-50 p-3 rounded text-xs">
                  <div className="grid grid-cols-2 gap-2">
                    <div><strong>ID:</strong> {record.id?.slice(0, 8)}...</div>
                    <div><strong>Created:</strong> {record.created_at ? new Date(record.created_at).toLocaleDateString() : 'N/A'}</div>
                    
                    {record.amount && <div><strong>Amount:</strong> ${record.amount}</div>}
                    {record.gross_amount && <div><strong>Gross:</strong> ${record.gross_amount}</div>}
                    {record.status && <div><strong>Status:</strong> {record.status}</div>}
                    {record.payment_method && <div><strong>Method:</strong> {record.payment_method}</div>}
                    {record.payment_type && <div><strong>Type:</strong> {record.payment_type}</div>}
                    {record.project_id && <div><strong>Project:</strong> {record.project_id.slice(0, 8)}...</div>}
                    {record.milestone_id && <div><strong>Milestone:</strong> {record.milestone_id.slice(0, 8)}...</div>}
                    {record.client_id && <div><strong>Client:</strong> {record.client_id.slice(0, 8)}...</div>}
                    {record.designer_id && <div><strong>Designer:</strong> {record.designer_id.slice(0, 8)}...</div>}
                    {record.transaction_id && <div><strong>Transaction:</strong> {record.transaction_id.slice(0, 8)}...</div>}
                    {record.escrow_account_id && <div><strong>Escrow Acc:</strong> {record.escrow_account_id.slice(0, 8)}...</div>}
                    {record.account_number && <div><strong>Account #:</strong> {record.account_number}</div>}
                    {record.total_held && <div><strong>Total Held:</strong> ${record.total_held}</div>}
                    {record.projects?.title && <div><strong>Project Title:</strong> {record.projects.title}</div>}
                  </div>
                </div>
              ))}
              {tableData.count > 5 && (
                <div className="text-gray-500 text-sm text-center">
                  ... and {tableData.count - 5} more records
                </div>
              )}
            </div>
          )}
        </CardContent>
      </Card>
    );
  };

  return (
    <div className="max-w-7xl mx-auto p-6 space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Simple Escrow Debug</h1>
          <p className="text-gray-600 mt-2">Raw data view of all payment and escrow tables</p>
        </div>
        <Button onClick={fetchAllData} disabled={loading}>
          <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
          Refresh Data
        </Button>
      </div>

      {loading ? (
        <div className="flex items-center justify-center py-12">
          <RefreshCw className="h-8 w-8 animate-spin text-blue-600" />
          <span className="ml-2">Loading all data...</span>
        </div>
      ) : (
        <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
          {Object.entries(data).map(([tableName, tableData]: [string, any]) => 
            renderTable(tableName, tableData)
          )}
        </div>
      )}

      {data.payments && (
        <Card className="mt-6">
          <CardHeader>
            <CardTitle>🎯 Quick Analysis</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <div className="bg-blue-50 border border-blue-200 p-3 rounded text-center">
                <div className="text-2xl font-bold text-blue-700">{data.payments?.count || 0}</div>
                <div className="text-sm text-blue-600">PayPal Payments</div>
              </div>
              <div className="bg-green-50 border border-green-200 p-3 rounded text-center">
                <div className="text-2xl font-bold text-green-700">{data.escrowAccounts?.count || 0}</div>
                <div className="text-sm text-green-600">Escrow Accounts</div>
              </div>
              <div className="bg-purple-50 border border-purple-200 p-3 rounded text-center">
                <div className="text-2xl font-bold text-purple-700">{data.escrowHolds?.count || 0}</div>
                <div className="text-sm text-purple-600">Escrow Holds</div>
              </div>
              <div className="bg-orange-50 border border-orange-200 p-3 rounded text-center">
                <div className="text-2xl font-bold text-orange-700">{data.paidMilestones?.count || 0}</div>
                <div className="text-sm text-orange-600">Paid Milestones</div>
              </div>
            </div>

            <div className="bg-yellow-50 border border-yellow-200 p-4 rounded">
              <h4 className="font-semibold text-yellow-900 mb-2">Expected Flow:</h4>
              <div className="text-sm text-yellow-800">
                PayPal Payment → Transaction → Escrow Account → Escrow Hold → Manager Visibility
              </div>
            </div>

            {data.payments?.count > 0 && data.escrowHolds?.count === 0 && (
              <div className="bg-red-50 border border-red-200 p-4 rounded">
                <h4 className="font-semibold text-red-900 mb-2">⚠️ Issue Detected:</h4>
                <div className="text-sm text-red-800">
                  You have {data.payments.count} PayPal payment(s) but {data.escrowHolds.count} escrow hold(s). 
                  This means escrow records are not being created when payments complete.
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      )}
    </div>
  );
}
