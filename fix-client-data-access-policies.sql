-- Fix Client Data Access Policies
-- This migration adds proper RLS policies for clients to access their own data

-- Enable RLS on tables if not already enabled
ALTER TABLE quality_reviews_new ENABLE ROW LEVEL SECURITY;
ALTER TABLE project_submissions ENABLE ROW LEVEL SECURITY;

-- Drop existing conflicting policies if they exist
DROP POLICY IF EXISTS "Clients can view quality reviews for their projects" ON quality_reviews_new;
DROP POLICY IF EXISTS "Clients can view submissions for their projects" ON project_submissions;

-- Create client access policy for quality reviews
CREATE POLICY "Clients can view quality reviews for their projects" ON quality_reviews_new
FOR SELECT USING (
    EXISTS (
        SELECT 1 FROM projects p
        WHERE p.id = quality_reviews_new.project_id
        AND p.client_id = auth.uid()
    )
);

-- Create client access policy for project submissions
CREATE POLICY "Clients can view submissions for their projects" ON project_submissions
FOR SELECT USING (
    EXISTS (
        SELECT 1 FROM projects p
        WHERE p.id = project_submissions.project_id
        AND p.client_id = auth.uid()
    )
);

-- Grant necessary permissions
GRANT SELECT ON quality_reviews_new TO authenticated;
GRANT SELECT ON project_submissions TO authenticated;

-- Add indexes for performance
CREATE INDEX IF NOT EXISTS idx_quality_reviews_project_client ON quality_reviews_new(project_id);
CREATE INDEX IF NOT EXISTS idx_project_submissions_project_client ON project_submissions(project_id);
CREATE INDEX IF NOT EXISTS idx_projects_client_id ON projects(client_id);

-- Verify policies are working
SELECT schemaname, tablename, policyname, permissive, roles, cmd, qual, with_check
FROM pg_policies
WHERE tablename IN ('quality_reviews_new', 'project_submissions')
ORDER BY tablename, policyname;
