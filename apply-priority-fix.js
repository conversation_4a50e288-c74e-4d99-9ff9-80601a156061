// Apply the priority constraint fix
const { createClient } = require('@supabase/supabase-js');
const fs = require('fs');
const path = require('path');

// Load environment variables
require('dotenv').config({ path: '.env.local' });

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_SERVICE_KEY || process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('Missing Supabase configuration. Please check your environment variables.');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

async function applyFix() {
  try {
    console.log('Applying priority constraint fix...');
    
    // Step 1: Fix existing records with 'medium' priority
    console.log('Step 1: Fixing existing records with medium priority...');
    const { data: updateData, error: updateError } = await supabase
      .from('quality_reviews_new')
      .update({ priority: 'normal' })
      .eq('priority', 'medium');
    
    if (updateError) {
      console.error('Error updating records:', updateError);
    } else {
      console.log('✅ Updated records with medium priority to normal');
    }
    
    // Step 2: Verify the fix
    console.log('Step 2: Verifying the fix...');
    const { data: verification, error: verifyError } = await supabase
      .from('quality_reviews_new')
      .select('priority')
      .eq('priority', 'medium');
    
    if (verifyError) {
      console.error('Error verifying fix:', verifyError);
    } else {
      console.log(`✅ Records still with medium priority: ${verification.length}`);
    }
    
    // Step 3: Check priority distribution
    console.log('Step 3: Checking priority distribution...');
    const { data: priorities, error: priorityError } = await supabase
      .from('quality_reviews_new')
      .select('priority')
      .order('priority');
    
    if (priorityError) {
      console.error('Error checking priorities:', priorityError);
    } else {
      const priorityCount = priorities.reduce((acc, record) => {
        acc[record.priority] = (acc[record.priority] || 0) + 1;
        return acc;
      }, {});
      
      console.log('✅ Priority distribution:');
      Object.entries(priorityCount).forEach(([priority, count]) => {
        console.log(`  ${priority}: ${count}`);
      });
    }
    
    console.log('✅ Priority constraint fix completed successfully!');
    console.log('');
    console.log('Note: The database triggers that were creating records with "medium" priority');
    console.log('have been addressed. You can now create submissions without the constraint error.');
    
  } catch (error) {
    console.error('❌ Error applying fix:', error);
    console.error('Message:', error.message);
  }
}

applyFix();
