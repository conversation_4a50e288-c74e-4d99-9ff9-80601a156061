-- File Annotations System Database Schema
-- Integrates with existing quality review workflow

-- Verify quality_reviews_new table exists
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT FROM information_schema.tables
        WHERE table_schema = 'public'
        AND table_name = 'quality_reviews_new'
    ) THEN
        RAISE EXCEPTION 'quality_reviews_new table does not exist in public schema. Please check your database.';
    ELSE
        RAISE NOTICE '✅ quality_reviews_new table found with id column';
    END IF;
END $$;

-- 1. FILE ANNOTATIONS TABLE
-- Stores visual annotations on files (images, PDFs, etc.)
DO $$
BEGIN
    -- Drop table if it exists to ensure clean creation
    DROP TABLE IF EXISTS file_annotations CASCADE;

    -- Create the table with all required columns
    CREATE TABLE file_annotations (
        id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
        file_url TEXT NOT NULL, -- URL of the annotated file
        file_name TEXT NOT NULL,
        file_type VARCHAR(100) NOT NULL,

        -- Annotation details
        annotation_type VARCHAR(50) NOT NULL CHECK (annotation_type IN ('issue', 'suggestion', 'approval', 'info')),
        x_position DECIMAL(5,2) NOT NULL, -- X coordinate as percentage (0-100)
        y_position DECIMAL(5,2) NOT NULL, -- Y coordinate as percentage (0-100)
        width DECIMAL(5,2), -- Width as percentage (for rectangles)
        height DECIMAL(5,2), -- Height as percentage (for rectangles)

        -- Content
        title VARCHAR(255),
        description TEXT,
        color VARCHAR(7) DEFAULT '#ff0000', -- Hex color code

        -- Relationships (without foreign keys initially)
        quality_review_id UUID,
        submission_id UUID, -- Can reference submissions or designer_work_submissions
        submission_type VARCHAR(50) DEFAULT 'project' CHECK (submission_type IN ('project', 'work', 'vision')),

        -- User tracking
        created_by UUID,
        reviewer_name VARCHAR(255), -- Store name for display

        -- Status
        status VARCHAR(50) DEFAULT 'active' CHECK (status IN ('active', 'resolved', 'archived')),
        resolved_at TIMESTAMP WITH TIME ZONE,
        resolved_by UUID,

        -- Timestamps
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
    );

    RAISE NOTICE '✅ Created file_annotations table with quality_review_id column';
END $$;

-- 2. ANNOTATION RESPONSES TABLE
-- Allows designers to respond to annotations
DO $$
BEGIN
    -- Drop table if it exists to ensure clean creation
    DROP TABLE IF EXISTS annotation_responses CASCADE;

    -- Create the table
    CREATE TABLE annotation_responses (
        id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
        annotation_id UUID,

        -- Response content
        response_text TEXT NOT NULL,
        response_type VARCHAR(50) DEFAULT 'comment' CHECK (response_type IN ('comment', 'question', 'clarification', 'resolution')),

        -- User tracking
        created_by UUID,
        responder_name VARCHAR(255),
        responder_role VARCHAR(50), -- 'designer', 'quality_team', 'client', etc.

        -- Timestamps
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
    );

    RAISE NOTICE '✅ Created annotation_responses table';
END $$;

-- 3. ANNOTATION HISTORY TABLE
-- Track changes to annotations
DO $$
BEGIN
    -- Drop table if it exists to ensure clean creation
    DROP TABLE IF EXISTS annotation_history CASCADE;

    -- Create the table
    CREATE TABLE annotation_history (
        id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
        annotation_id UUID,

        -- Change tracking
        action VARCHAR(50) NOT NULL CHECK (action IN ('created', 'updated', 'resolved', 'reopened', 'deleted')),
        old_values JSONB,
        new_values JSONB,
        change_description TEXT,

        -- User tracking
        changed_by UUID,
        changed_by_name VARCHAR(255),

        -- Timestamps
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
    );

    RAISE NOTICE '✅ Created annotation_history table';
END $$;

-- 4. ADD FOREIGN KEY CONSTRAINTS
-- Add foreign key constraints after tables are created
DO $$
BEGIN
    -- Add foreign key constraints for file_annotations
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.table_constraints
        WHERE constraint_name = 'fk_file_annotations_quality_review'
    ) THEN
        ALTER TABLE file_annotations
        ADD CONSTRAINT fk_file_annotations_quality_review
        FOREIGN KEY (quality_review_id) REFERENCES quality_reviews_new(id) ON DELETE CASCADE;
        RAISE NOTICE '✅ Added foreign key constraint to quality_reviews_new';
    END IF;

    IF NOT EXISTS (
        SELECT 1 FROM information_schema.table_constraints
        WHERE constraint_name = 'fk_file_annotations_created_by'
    ) THEN
        ALTER TABLE file_annotations
        ADD CONSTRAINT fk_file_annotations_created_by
        FOREIGN KEY (created_by) REFERENCES profiles(id) ON DELETE SET NULL;
        RAISE NOTICE '✅ Added created_by foreign key constraint';
    END IF;

    IF NOT EXISTS (
        SELECT 1 FROM information_schema.table_constraints
        WHERE constraint_name = 'fk_file_annotations_resolved_by'
    ) THEN
        ALTER TABLE file_annotations
        ADD CONSTRAINT fk_file_annotations_resolved_by
        FOREIGN KEY (resolved_by) REFERENCES profiles(id) ON DELETE SET NULL;
        RAISE NOTICE '✅ Added resolved_by foreign key constraint';
    END IF;

    -- Add foreign key constraints for annotation_responses
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.table_constraints
        WHERE constraint_name = 'fk_annotation_responses_annotation'
    ) THEN
        ALTER TABLE annotation_responses
        ADD CONSTRAINT fk_annotation_responses_annotation
        FOREIGN KEY (annotation_id) REFERENCES file_annotations(id) ON DELETE CASCADE;
        RAISE NOTICE '✅ Added annotation_responses annotation_id foreign key';
    END IF;

    IF NOT EXISTS (
        SELECT 1 FROM information_schema.table_constraints
        WHERE constraint_name = 'fk_annotation_responses_created_by'
    ) THEN
        ALTER TABLE annotation_responses
        ADD CONSTRAINT fk_annotation_responses_created_by
        FOREIGN KEY (created_by) REFERENCES profiles(id) ON DELETE SET NULL;
        RAISE NOTICE '✅ Added annotation_responses created_by foreign key';
    END IF;

    -- Add foreign key constraints for annotation_history
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.table_constraints
        WHERE constraint_name = 'fk_annotation_history_annotation'
    ) THEN
        ALTER TABLE annotation_history
        ADD CONSTRAINT fk_annotation_history_annotation
        FOREIGN KEY (annotation_id) REFERENCES file_annotations(id) ON DELETE CASCADE;
        RAISE NOTICE '✅ Added annotation_history annotation_id foreign key';
    END IF;

    IF NOT EXISTS (
        SELECT 1 FROM information_schema.table_constraints
        WHERE constraint_name = 'fk_annotation_history_changed_by'
    ) THEN
        ALTER TABLE annotation_history
        ADD CONSTRAINT fk_annotation_history_changed_by
        FOREIGN KEY (changed_by) REFERENCES profiles(id) ON DELETE SET NULL;
        RAISE NOTICE '✅ Added annotation_history changed_by foreign key';
    END IF;
END $$;

-- 5. INDEXES FOR PERFORMANCE
CREATE INDEX IF NOT EXISTS idx_file_annotations_file_url ON file_annotations(file_url);
CREATE INDEX IF NOT EXISTS idx_file_annotations_quality_review ON file_annotations(quality_review_id);
CREATE INDEX IF NOT EXISTS idx_file_annotations_submission ON file_annotations(submission_id, submission_type);
CREATE INDEX IF NOT EXISTS idx_file_annotations_created_by ON file_annotations(created_by);
CREATE INDEX IF NOT EXISTS idx_file_annotations_status ON file_annotations(status);
CREATE INDEX IF NOT EXISTS idx_file_annotations_created_at ON file_annotations(created_at);

CREATE INDEX IF NOT EXISTS idx_annotation_responses_annotation ON annotation_responses(annotation_id);
CREATE INDEX IF NOT EXISTS idx_annotation_responses_created_by ON annotation_responses(created_by);
CREATE INDEX IF NOT EXISTS idx_annotation_responses_created_at ON annotation_responses(created_at);

CREATE INDEX IF NOT EXISTS idx_annotation_history_annotation ON annotation_history(annotation_id);
CREATE INDEX IF NOT EXISTS idx_annotation_history_changed_by ON annotation_history(changed_by);
CREATE INDEX IF NOT EXISTS idx_annotation_history_created_at ON annotation_history(created_at);

-- 6. TRIGGERS FOR AUTOMATIC UPDATES
-- Update annotation updated_at timestamp
CREATE OR REPLACE FUNCTION update_annotation_timestamp()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_update_annotation_timestamp
    BEFORE UPDATE ON file_annotations
    FOR EACH ROW
    EXECUTE FUNCTION update_annotation_timestamp();

-- Create annotation history entry on changes
CREATE OR REPLACE FUNCTION create_annotation_history()
RETURNS TRIGGER AS $$
BEGIN
    IF TG_OP = 'INSERT' THEN
        INSERT INTO annotation_history (annotation_id, action, new_values, changed_by, changed_by_name)
        VALUES (NEW.id, 'created', to_jsonb(NEW), NEW.created_by, NEW.reviewer_name);
        RETURN NEW;
    ELSIF TG_OP = 'UPDATE' THEN
        -- Only create history if significant fields changed
        IF OLD.status != NEW.status OR OLD.description != NEW.description OR OLD.title != NEW.title THEN
            INSERT INTO annotation_history (annotation_id, action, old_values, new_values, changed_by, changed_by_name)
            VALUES (NEW.id, 'updated', to_jsonb(OLD), to_jsonb(NEW), NEW.created_by, NEW.reviewer_name);
        END IF;
        RETURN NEW;
    ELSIF TG_OP = 'DELETE' THEN
        INSERT INTO annotation_history (annotation_id, action, old_values, changed_by)
        VALUES (OLD.id, 'deleted', to_jsonb(OLD), OLD.created_by);
        RETURN OLD;
    END IF;
    RETURN NULL;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_annotation_history
    AFTER INSERT OR UPDATE OR DELETE ON file_annotations
    FOR EACH ROW
    EXECUTE FUNCTION create_annotation_history();

-- 7. FUNCTIONS FOR ANNOTATION MANAGEMENT
-- Get annotations for a specific file
CREATE OR REPLACE FUNCTION get_file_annotations(
    p_file_url TEXT,
    p_include_resolved BOOLEAN DEFAULT FALSE
)
RETURNS TABLE (
    id UUID,
    annotation_type VARCHAR(50),
    x_position DECIMAL(5,2),
    y_position DECIMAL(5,2),
    width DECIMAL(5,2),
    height DECIMAL(5,2),
    title VARCHAR(255),
    description TEXT,
    color VARCHAR(7),
    status VARCHAR(50),
    reviewer_name VARCHAR(255),
    created_at TIMESTAMP WITH TIME ZONE,
    response_count BIGINT
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        fa.id,
        fa.annotation_type,
        fa.x_position,
        fa.y_position,
        fa.width,
        fa.height,
        fa.title,
        fa.description,
        fa.color,
        fa.status,
        fa.reviewer_name,
        fa.created_at,
        COUNT(ar.id) as response_count
    FROM file_annotations fa
    LEFT JOIN annotation_responses ar ON fa.id = ar.annotation_id
    WHERE fa.file_url = p_file_url
    AND (p_include_resolved OR fa.status != 'resolved')
    GROUP BY fa.id, fa.annotation_type, fa.x_position, fa.y_position, fa.width, fa.height, 
             fa.title, fa.description, fa.color, fa.status, fa.reviewer_name, fa.created_at
    ORDER BY fa.created_at DESC;
END;
$$ LANGUAGE plpgsql;

-- Get annotation statistics for quality reviews
CREATE OR REPLACE FUNCTION get_annotation_stats(
    p_quality_review_id UUID
)
RETURNS TABLE (
    total_annotations BIGINT,
    active_annotations BIGINT,
    resolved_annotations BIGINT,
    issue_count BIGINT,
    suggestion_count BIGINT,
    approval_count BIGINT
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        COUNT(*) as total_annotations,
        COUNT(*) FILTER (WHERE status = 'active') as active_annotations,
        COUNT(*) FILTER (WHERE status = 'resolved') as resolved_annotations,
        COUNT(*) FILTER (WHERE annotation_type = 'issue') as issue_count,
        COUNT(*) FILTER (WHERE annotation_type = 'suggestion') as suggestion_count,
        COUNT(*) FILTER (WHERE annotation_type = 'approval') as approval_count
    FROM file_annotations
    WHERE quality_review_id = p_quality_review_id;
END;
$$ LANGUAGE plpgsql;

-- 8. RLS POLICIES (Row Level Security)
-- Enable RLS on all tables
ALTER TABLE file_annotations ENABLE ROW LEVEL SECURITY;
ALTER TABLE annotation_responses ENABLE ROW LEVEL SECURITY;
ALTER TABLE annotation_history ENABLE ROW LEVEL SECURITY;

-- Policies for file_annotations
CREATE POLICY "Quality team can manage all annotations" ON file_annotations
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM profiles 
            WHERE profiles.id = auth.uid() 
            AND profiles.role IN ('quality_team', 'admin', 'manager')
        )
    );

CREATE POLICY "Designers can view annotations on their work" ON file_annotations
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM profiles 
            WHERE profiles.id = auth.uid() 
            AND profiles.role = 'designer'
            AND (
                -- Check if this is their submission
                EXISTS (
                    SELECT 1 FROM quality_reviews_new qr
                    WHERE qr.id = file_annotations.quality_review_id
                    AND qr.designer_id = auth.uid()
                )
            )
        )
    );

-- Policies for annotation_responses
CREATE POLICY "Users can manage responses to annotations they can see" ON annotation_responses
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM file_annotations fa
            WHERE fa.id = annotation_responses.annotation_id
            AND (
                -- Quality team can respond to all
                EXISTS (
                    SELECT 1 FROM profiles 
                    WHERE profiles.id = auth.uid() 
                    AND profiles.role IN ('quality_team', 'admin', 'manager')
                )
                OR
                -- Designers can respond to annotations on their work
                EXISTS (
                    SELECT 1 FROM profiles 
                    WHERE profiles.id = auth.uid() 
                    AND profiles.role = 'designer'
                    AND EXISTS (
                        SELECT 1 FROM quality_reviews_new qr
                        WHERE qr.id = fa.quality_review_id
                        AND qr.designer_id = auth.uid()
                    )
                )
            )
        )
    );

-- Policies for annotation_history (read-only for authorized users)
CREATE POLICY "Authorized users can view annotation history" ON annotation_history
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM profiles 
            WHERE profiles.id = auth.uid() 
            AND profiles.role IN ('quality_team', 'admin', 'manager')
        )
    );

-- 9. COMMENTS FOR DOCUMENTATION
COMMENT ON TABLE file_annotations IS 'Visual annotations on files during quality review process';
COMMENT ON TABLE annotation_responses IS 'Responses and discussions on file annotations';
COMMENT ON TABLE annotation_history IS 'Audit trail for annotation changes';

COMMENT ON COLUMN file_annotations.x_position IS 'X coordinate as percentage (0-100) of file width';
COMMENT ON COLUMN file_annotations.y_position IS 'Y coordinate as percentage (0-100) of file height';
COMMENT ON COLUMN file_annotations.submission_type IS 'Type of submission: project (main submissions), work (designer work), vision (vision requests)';

-- 10. SAMPLE DATA (for testing)
-- This will be populated by the application, but here's the structure:
/*
INSERT INTO file_annotations (
    file_url, file_name, file_type, annotation_type,
    x_position, y_position, width, height,
    title, description, quality_review_id,
    created_by, reviewer_name
) VALUES (
    'https://example.com/file.jpg',
    'design-mockup.jpg',
    'image/jpeg',
    'issue',
    25.5, 30.2, 15.0, 10.0,
    'Color contrast issue',
    'The text color doesn''t provide sufficient contrast against the background',
    'quality-review-uuid',
    'reviewer-uuid',
    'John Quality Reviewer'
);
*/
