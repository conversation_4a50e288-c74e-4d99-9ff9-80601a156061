"use client";

import React, { useState, useEffect } from "react";
import { useOptimizedAuth } from "@/hooks/useOptimizedAuth";
import { supabase } from "@/lib/supabase";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, <PERSON>Content, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>le } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import {
  FileText,
  Eye,
  Clock,
  CheckCircle,
  XCircle,
  AlertTriangle,
  Filter,
  Search,
  RefreshCw,
  User,
  Calendar,
  Folder
} from "lucide-react";

interface Submission {
  id: string;
  title?: string;
  description: string;
  project_id: string;
  project_title: string;
  designer_id: string;
  designer_name: string;
  client_name: string;
  files: any[];
  status: string;
  submitted_at: string;
  source: 'project_submissions' | 'design_submissions' | 'designer_work_submissions' | 'project' | 'design' | 'work';
  tracking_number?: string;
  request_type?: string;
  quality_review_id?: string;
  review_status?: string;
  review_priority?: string;
}

interface ProjectSubmissionData {
  id: string;
  description: string;
  project_id: string;
  files: any[];
  status: string;
  submitted_at: string;
  projects: {
    title: string;
    client_id: string;
    designer_id: string;
    profiles: { full_name: string }[];
    designer: { full_name: string }[];
  };
}

interface DesignSubmissionData {
  id: string;
  title: string;
  description: string;
  project_id: string;
  status: string;
  created_at: string;
  projects: {
    title: string;
    client_id: string;
    designer_id: string;
    profiles: { full_name: string }[];
    designer: { full_name: string }[];
  };
}

interface WorkSubmissionData {
  id: string;
  file_name: string;
  notes: string;
  status: string;
  created_at: string;
  tracking_requests: {
    tracking_number: string;
    request_type: string;
    name: string;
    email: string;
    assigned_to: string;
    profiles: { full_name: string } | null;
  };
}

export default function QualitySubmissionsPage() {
  const { user, profile } = useOptimizedAuth();
  const [submissions, setSubmissions] = useState<Submission[]>([]);
  const [loading, setLoading] = useState(true);
  const [filter, setFilter] = useState<string>('pending');
  const [searchTerm, setSearchTerm] = useState('');
  const [sourceFilter, setSourceFilter] = useState<string>('all');

  // Debug function to check what's in the database
  const debugDatabaseTables = async () => {
    console.log('🔍 [DEBUG] Checking database tables...');

    try {
      // Check project_submissions table
      const { data: projectSubs, error: projectError } = await supabase
        .from('project_submissions')
        .select('*')
        .limit(5);

      console.log('🔍 [DEBUG] project_submissions table:', { data: projectSubs, error: projectError });

      // Check design_submissions table
      const { data: designSubs, error: designError } = await supabase
        .from('design_submissions')
        .select('*')
        .limit(5);

      console.log('🔍 [DEBUG] design_submissions table:', { data: designSubs, error: designError });

      // Check designer_work_submissions table
      const { data: workSubs, error: workError } = await supabase
        .from('designer_work_submissions')
        .select('*')
        .limit(5);

      console.log('🔍 [DEBUG] designer_work_submissions table:', { data: workSubs, error: workError });

      // Check old submissions table
      const { data: oldSubs, error: oldError } = await supabase
        .from('submissions')
        .select('*')
        .limit(5);

      console.log('🔍 [DEBUG] submissions table (old):', { data: oldSubs, error: oldError });

    } catch (error) {
      console.error('❌ [DEBUG] Error checking database tables:', error);
    }
  };

  useEffect(() => {
    if (user && profile?.role === 'quality_team') {
      console.log('🔍 [QUALITY SUBMISSIONS] Component mounted, user role:', profile?.role);
      debugDatabaseTables(); // Run debug check first
      fetchSubmissions();
    } else {
      console.log('🔍 [QUALITY SUBMISSIONS] User not authorized or not loaded:', {
        hasUser: !!user,
        role: profile?.role
      });
    }
  }, [user, profile, filter, sourceFilter]);

  const fetchSubmissions = async () => {
    setLoading(true);
    console.log('🔍 [DEBUG] Starting fetchSubmissions...');
    console.log('🔍 [DEBUG] Current filters:', { filter, sourceFilter, searchTerm });
    console.log('🔍 [DEBUG] User:', { userId: user?.id, role: profile?.role });

    try {
      const allSubmissions: Submission[] = [];

      // 1. Fetch from consolidated project_submissions table (always fetch if no source filter or includes project/all)
      if (sourceFilter === 'all' || sourceFilter === 'project') {
        console.log('🔍 [DEBUG] Fetching from consolidated project_submissions table...');

        const statusFilter = filter === 'pending' ? ['submitted', 'under_review'] :
          filter === 'reviewed' ? ['approved', 'needs_revision', 'rejected'] :
            ['submitted', 'under_review', 'approved', 'needs_revision', 'rejected'];

        console.log('🔍 [DEBUG] Status filter for project_submissions:', statusFilter);

        const { data: projectSubmissions, error: projectError } = await supabase
        .from('project_submissions')
        .select(`
          id,
          title,
          description,
          project_id,
          files,
          status,
          submitted_at,
          submission_source,
          quality_review_id,
          tracking_request_id,
          projects(
            title,
            client_id,
            designer_id,
            profiles!client_id(full_name),
            designer:profiles!designer_id(full_name)
          ),
          tracking_requests(
            tracking_number,
            request_type,
            name,
            email
          ),
          quality_reviews_new!project_submissions_quality_review_id_fkey(
            id,
            status,
            priority,
            reviewer_id
          )
        `)
        .in('status', statusFilter)
        .order('submitted_at', { ascending: false });

        console.log('🔍 [DEBUG] project_submissions query result:', {
          data: projectSubmissions,
          error: projectError,
          count: projectSubmissions?.length || 0
        });

        if (projectError) {
          console.error('❌ [DEBUG] project_submissions error:', projectError);
          throw projectError;
        }

        console.log('🔍 [DEBUG] Raw project_submissions data:', projectSubmissions);

        const transformedProjectSubmissions = (projectSubmissions || []).map((sub: any) => {
          console.log('🔍 [DEBUG] Transforming consolidated submission:', sub);

          // Handle different submission sources
          let title = sub.title || 'Untitled Submission';
          let description = sub.description || '';
          let designer_name = 'Unknown Designer';
          let client_name = 'Unknown Client';
          let project_title = 'Unknown Project';

          if (sub.submission_source === 'work' && sub.tracking_requests) {
            // For work submissions from tracking requests
            title = sub.tracking_requests.tracking_number || title;
            description = `${sub.tracking_requests.request_type} - ${sub.tracking_requests.name}`;
            client_name = sub.tracking_requests.name;
            project_title = `${sub.tracking_requests.request_type} Request`;
          } else if (sub.projects) {
            // For project submissions
            if (!sub.title && sub.description) {
              title = sub.description.split('\n')[0] || 'Untitled Submission';
              description = sub.description.split('\n').slice(1).join('\n') || '';
            }
            project_title = sub.projects.title || 'Unknown Project';
            designer_name = sub.projects.designer?.[0]?.full_name || sub.projects.designer?.full_name || 'Unknown Designer';
            client_name = sub.projects.profiles?.[0]?.full_name || sub.projects.profiles?.full_name || 'Unknown Client';
          }

          return {
            id: sub.id,
            title: title,
            description: description,
            project_id: sub.project_id,
            project_title: project_title,
            designer_id: sub.projects?.designer_id || '',
            designer_name: designer_name,
            client_name: client_name,
            files: sub.files || [],
            status: sub.status,
            submitted_at: sub.submitted_at,
            source: sub.submission_source || 'project',
            tracking_number: sub.tracking_requests?.tracking_number,
            request_type: sub.tracking_requests?.request_type,
            quality_review_id: sub.quality_review_id,
            review_status: sub.quality_reviews_new?.status,
            review_priority: sub.quality_reviews_new?.priority
          };
        });

        console.log('🔍 [DEBUG] Transformed project submissions:', transformedProjectSubmissions);
        allSubmissions.push(...transformedProjectSubmissions);
      }

      // Note: design_submissions data is now consolidated in project_submissions table

      // Note: designer_work_submissions data is now consolidated in project_submissions table

      console.log('🔍 [DEBUG] All submissions before sorting:', allSubmissions);
      console.log('🔍 [DEBUG] Total submissions found:', allSubmissions.length);

      // Sort all submissions by date
      allSubmissions.sort((a: Submission, b: Submission) => new Date(b.submitted_at).getTime() - new Date(a.submitted_at).getTime());

      // Apply search filter
      const filteredSubmissions = searchTerm
        ? allSubmissions.filter((sub: Submission) =>
          sub.title?.toLowerCase().includes(searchTerm.toLowerCase()) ||
          sub.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
          sub.project_title.toLowerCase().includes(searchTerm.toLowerCase()) ||
          sub.designer_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
          sub.client_name.toLowerCase().includes(searchTerm.toLowerCase())
        )
        : allSubmissions;

      console.log('🔍 [DEBUG] Filtered submissions:', filteredSubmissions);
      console.log('🔍 [DEBUG] Final submissions count:', filteredSubmissions.length);

      setSubmissions(filteredSubmissions);
    } catch (error) {
      console.error('❌ [DEBUG] Error in fetchSubmissions:', error);
    } finally {
      setLoading(false);
    }
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'submitted':
      case 'under_review':
        return <Badge variant="outline" className="bg-yellow-50 text-yellow-700 border-yellow-200">Pending Review</Badge>;
      case 'approved':
        return <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">Approved</Badge>;
      case 'needs_revision':
        return <Badge variant="outline" className="bg-orange-50 text-orange-700 border-orange-200">Needs Revision</Badge>;
      case 'rejected':
        return <Badge variant="outline" className="bg-red-50 text-red-700 border-red-200">Rejected</Badge>;
      case 'completed':
        return <Badge variant="outline" className="bg-blue-50 text-blue-700 border-blue-200">Completed</Badge>;
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  const getSourceBadge = (source: string) => {
    switch (source) {
      case 'project_submissions':
      case 'project':
        return <Badge variant="outline" className="bg-blue-50 text-blue-700 border-blue-200">Project</Badge>;
      case 'design_submissions':
      case 'design':
        return <Badge variant="outline" className="bg-purple-50 text-purple-700 border-purple-200">Design</Badge>;
      case 'designer_work_submissions':
      case 'work':
        return <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">Work Sample</Badge>;
      default:
        return <Badge variant="outline">{source}</Badge>;
    }
  };

  const handleViewSubmission = async (submission: Submission) => {
    console.log('🔍 [QUALITY SUBMISSIONS] Finding or creating review for submission:', {
      submissionId: submission.id,
      source: submission.source
    });

    // Navigate to appropriate review page based on source
    if (submission.source === 'designer_work_submissions' || submission.source === 'work') {
      window.location.href = `/quality/tracking-reviews/${submission.tracking_number}`;
      return;
    }

    try {
      // First, try to find existing review for this submission
      // Use order and limit to handle multiple reviews (take the most recent)
      const { data: existingReviews, error: findError } = await supabase
        .from('quality_reviews_new')
        .select('id, status, created_at')
        .eq('submission_id', submission.id)
        .order('created_at', { ascending: false })
        .limit(1);

      if (findError) {
        console.error('❌ [QUALITY SUBMISSIONS] Error finding review:', findError);
        throw findError;
      }

      const existingReview = existingReviews && existingReviews.length > 0 ? existingReviews[0] : null;

      if (existingReviews && existingReviews.length > 1) {
        console.warn('⚠️ [QUALITY SUBMISSIONS] Multiple reviews found for submission, using most recent:', {
          submissionId: submission.id,
          reviewCount: existingReviews.length
        });
      }

      let reviewId: string;

      if (existingReview) {
        // Review exists, use its ID
        reviewId = existingReview.id;
        console.log('✅ [QUALITY SUBMISSIONS] Found existing review:', reviewId);
      } else {
        // No review exists, create one
        console.log('🔄 [QUALITY SUBMISSIONS] Creating new review for submission:', submission.id);

        const { data: newReview, error: createError } = await supabase
          .from('quality_reviews_new')
          .insert({
            submission_id: submission.id,
            project_id: submission.project_id,
            designer_id: submission.designer_id,
            status: 'pending',
            review_type: 'submission',
            priority: 'normal'
          })
          .select('id')
          .single();

        if (createError) {
          console.error('❌ [QUALITY SUBMISSIONS] Error creating review:', createError);
          throw createError;
        }

        reviewId = newReview.id;
        console.log('✅ [QUALITY SUBMISSIONS] Created new review:', reviewId);
      }

      // Navigate to the review page
      console.log('🔍 [QUALITY SUBMISSIONS] Navigating to review:', reviewId);
      window.location.href = `/quality/reviews/${reviewId}`;

    } catch (error) {
      console.error('❌ [QUALITY SUBMISSIONS] Error handling review navigation:', error);
      alert('Error opening review. Please try again.');
    }
  };

  if (!user || profile?.role !== 'quality_team') {
    return (
      <div className="flex items-center justify-center h-64">
        <p className="text-gray-500">Access denied. Quality team members only.</p>
      </div>
    );
  }

  return (
    <div className="max-w-7xl mx-auto p-6 space-y-8">
      {/* Header */}
      <div className="flex flex-col lg:flex-row lg:items-center justify-between gap-4">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Designer Submissions</h1>
          <p className="text-gray-600 mt-2">Review all designer submissions across projects and work samples</p>
        </div>

        <div className="flex items-center space-x-4">
          <Button
            onClick={debugDatabaseTables}
            variant="outline"
            className="flex items-center gap-2"
          >
            <AlertTriangle className="h-4 w-4" />
            Debug DB
          </Button>
          <Button
            onClick={fetchSubmissions}
            className="flex items-center gap-2"
          >
            <RefreshCw className="h-4 w-4" />
            Refresh
          </Button>
        </div>
      </div>

      {/* Filters */}
      <div className="flex flex-col sm:flex-row gap-4">
        <div className="flex items-center gap-2">
          <Filter className="h-4 w-4 text-gray-500" />
          <select
            value={filter}
            onChange={(e) => setFilter(e.target.value)}
            className="border border-gray-300 rounded-md px-3 py-2 text-sm"
          >
            <option value="pending">Pending Review</option>
            <option value="reviewed">Reviewed</option>
            <option value="all">All Submissions</option>
          </select>
        </div>

        <div className="flex items-center gap-2">
          <Folder className="h-4 w-4 text-gray-500" />
          <select
            value={sourceFilter}
            onChange={(e) => setSourceFilter(e.target.value)}
            className="border border-gray-300 rounded-md px-3 py-2 text-sm"
          >
            <option value="all">All Sources</option>
            <option value="project">Project Submissions</option>
            <option value="design">Design Submissions</option>
            <option value="work">Work Samples</option>
          </select>
        </div>

        <div className="flex items-center gap-2 flex-1">
          <Search className="h-4 w-4 text-gray-500" />
          <input
            type="text"
            placeholder="Search submissions..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="border border-gray-300 rounded-md px-3 py-2 text-sm flex-1"
          />
        </div>
      </div>

      {/* Submissions List */}
      {loading ? (
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
        </div>
      ) : submissions.length === 0 ? (
        <Card>
          <CardContent className="p-8 text-center">
            <FileText className="h-12 w-12 text-gray-300 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">No Submissions Found</h3>
            <p className="text-gray-600">
              {filter === 'pending'
                ? 'No submissions are currently pending review.'
                : 'No submissions match your current filters.'
              }
            </p>
          </CardContent>
        </Card>
      ) : (
        <div className="space-y-4">
          {submissions.map((submission) => (
            <Card key={`${submission.source}-${submission.id}`} className="hover:shadow-md transition-shadow">
              <CardContent className="p-6">
                <div className="flex items-start justify-between">
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center gap-3 mb-2">
                      <h3 className="text-lg font-medium text-gray-900 truncate">
                        {submission.title}
                      </h3>
                      {getSourceBadge(submission.source)}
                      {getStatusBadge(submission.status)}
                    </div>

                    <p className="text-gray-600 mb-3 line-clamp-2">
                      {submission.description}
                    </p>

                    <div className="flex items-center gap-6 text-sm text-gray-500">
                      <div className="flex items-center gap-1">
                        <Folder className="h-4 w-4" />
                        <span>{submission.project_title}</span>
                      </div>
                      <div className="flex items-center gap-1">
                        <User className="h-4 w-4" />
                        <span>{submission.designer_name}</span>
                      </div>
                      <div className="flex items-center gap-1">
                        <Calendar className="h-4 w-4" />
                        <span>{new Date(submission.submitted_at).toLocaleDateString()}</span>
                      </div>
                      {submission.files && submission.files.length > 0 && (
                        <div className="flex items-center gap-1">
                          <FileText className="h-4 w-4" />
                          <span>{submission.files.length} file(s)</span>
                        </div>
                      )}
                    </div>
                  </div>

                  <div className="flex items-center gap-2 ml-4">
                    <Button
                      onClick={() => handleViewSubmission(submission)}
                      className="flex items-center gap-2"
                    >
                      <Eye className="h-4 w-4" />
                      Review
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}
    </div>
  );
}
