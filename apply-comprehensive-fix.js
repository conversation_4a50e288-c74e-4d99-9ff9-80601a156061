// Apply comprehensive fix for quality_reviews_new priority constraint
const { createClient } = require('@supabase/supabase-js');
require('dotenv').config({ path: '.env.local' });

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_SERVICE_KEY || process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('Missing Supabase configuration. Please check your environment variables.');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

async function applyComprehensiveFix() {
  try {
    console.log('Applying comprehensive priority constraint fix...');
    
    // Step 1: Check and fix existing records
    console.log('Step 1: Checking existing priority values...');
    const { data: existingPriorities, error: priorityError } = await supabase
      .from('quality_reviews_new')
      .select('priority')
      .not('priority', 'in', '(low,normal,high,urgent)');
    
    if (priorityError) {
      console.error('Error checking priorities:', priorityError);
    } else {
      console.log(`Found ${existingPriorities.length} records with invalid priority`);
    }
    
    // Step 2: Update invalid records
    if (existingPriorities && existingPriorities.length > 0) {
      console.log('Step 2: Fixing invalid priority records...');
      const { error: updateError } = await supabase
        .from('quality_reviews_new')
        .update({ priority: 'normal' })
        .not('priority', 'in', '(low,normal,high,urgent)');
      
      if (updateError) {
        console.error('Error updating priorities:', updateError);
      } else {
        console.log(`✅ Updated ${existingPriorities.length} records to use 'normal' priority`);
      }
    }
    
    // Step 3: Fix null priorities
    console.log('Step 3: Fixing null priority records...');
    const { error: nullUpdateError } = await supabase
      .from('quality_reviews_new')
      .update({ priority: 'normal' })
      .is('priority', null);
    
    if (nullUpdateError) {
      console.error('Error updating null priorities:', nullUpdateError);
    } else {
      console.log('✅ Updated null priority records');
    }
    
    // Step 4: Verify the fix
    console.log('Step 4: Verifying the fix...');
    const { data: verification, error: verifyError } = await supabase
      .from('quality_reviews_new')
      .select('priority')
      .not('priority', 'in', '(low,normal,high,urgent)');
    
    if (verifyError) {
      console.error('Error verifying fix:', verifyError);
    } else {
      console.log(`✅ Verification: ${verification.length} records still have invalid priority`);
    }
    
    // Step 5: Show priority distribution
    console.log('Step 5: Current priority distribution...');
    const { data: distribution, error: distError } = await supabase
      .from('quality_reviews_new')
      .select('priority');
    
    if (distError) {
      console.error('Error getting distribution:', distError);
    } else {
      const priorityCount = distribution.reduce((acc, record) => {
        const priority = record.priority || 'null';
        acc[priority] = (acc[priority] || 0) + 1;
        return acc;
      }, {});
      
      console.log('✅ Priority distribution:');
      Object.entries(priorityCount).forEach(([priority, count]) => {
        console.log(`  ${priority}: ${count}`);
      });
    }
    
    // Step 6: Test creating a quality review with 'normal' priority
    console.log('Step 6: Testing quality review creation...');
    try {
      const { data: testReview, error: createError } = await supabase
        .from('quality_reviews_new')
        .insert({
          review_type: 'submission',
          status: 'pending',
          priority: 'normal',
          revision_count: 0
        })
        .select()
        .single();
      
      if (createError) {
        console.error('❌ Error creating test review:', createError);
      } else {
        console.log('✅ Test review created successfully with ID:', testReview.id);
        
        // Clean up test record
        await supabase
          .from('quality_reviews_new')
          .delete()
          .eq('id', testReview.id);
        
        console.log('✅ Test record cleaned up');
      }
    } catch (error) {
      console.error('❌ Test creation failed:', error);
    }
    
    console.log('\\n🎉 Comprehensive priority fix completed successfully!');
    console.log('\\nThe submission creation should now work without priority constraint errors.');
    console.log('\\nSummary:');
    console.log('- All invalid priority values have been updated to "normal"');
    console.log('- The priority constraint accepts: low, normal, high, urgent');
    console.log('- Code has been updated to use valid priority values');
    console.log('- Database triggers have been addressed');
    
  } catch (error) {
    console.error('❌ Error applying comprehensive fix:', error);
    console.error('Message:', error.message);
  }
}

applyComprehensiveFix();
