-- =====================================================
-- CONSOLIDATE SUBMISSION TABLES MIGRATION
-- This migration consolidates multiple submission tables into project_submissions
-- and improves the workflow integration
-- =====================================================

-- Step 1: Enhance project_submissions table to handle all submission types
ALTER TABLE project_submissions 
ADD COLUMN IF NOT EXISTS title TEXT,
ADD COLUMN IF NOT EXISTS submission_source VARCHAR(50) DEFAULT 'project' CHECK (submission_source IN ('project', 'design', 'work', 'tracking')),
ADD COLUMN IF NOT EXISTS tracking_request_id UUID REFERENCES tracking_requests(id),
ADD COLUMN IF NOT EXISTS file_name VARCHAR(255),
ADD COLUMN IF NOT EXISTS file_path VARCHAR(500),
ADD COLUMN IF NOT EXISTS file_type VARCHAR(100),
ADD COLUMN IF NOT EXISTS file_size BIGINT,
ADD COLUMN IF NOT EXISTS work_type VARCHAR(50),
ADD COLUMN IF NOT EXISTS version INTEGER DEFAULT 1,
ADD COLUMN IF NOT EXISTS feedback TEXT,
ADD COLUMN IF NOT EXISTS revision_requested BOOLEAN DEFAULT FALSE,
ADD COLUMN IF NOT EXISTS reviewed_by UUID REFERENCES profiles(id),
ADD COLUMN IF NOT EXISTS quality_review_id UUID REFERENCES quality_reviews_new(id);

-- Step 2: Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_project_submissions_designer_id ON project_submissions(designer_id);
CREATE INDEX IF NOT EXISTS idx_project_submissions_status ON project_submissions(status);
CREATE INDEX IF NOT EXISTS idx_project_submissions_submission_source ON project_submissions(submission_source);
CREATE INDEX IF NOT EXISTS idx_project_submissions_quality_review_id ON project_submissions(quality_review_id);

-- Step 3: Migrate data from design_submissions table (if exists)
DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'design_submissions') THEN
        INSERT INTO project_submissions (
            project_id,
            designer_id,
            title,
            description,
            status,
            submission_source,
            files,
            submitted_at,
            created_at
        )
        SELECT
            COALESCE(project_id, gen_random_uuid()), -- Handle null project_id
            designer_id,
            COALESCE(title, 'Design Submission'),
            COALESCE(description, ''),
            COALESCE(status, 'submitted'),
            'design',
            '[]'::jsonb, -- Convert to empty array, will be populated later
            COALESCE(created_at, NOW()),
            COALESCE(created_at, NOW())
        FROM design_submissions
        WHERE designer_id IS NOT NULL -- Only migrate if designer_id exists
        AND NOT EXISTS (
            SELECT 1 FROM project_submissions ps
            WHERE ps.project_id = design_submissions.project_id
            AND ps.designer_id = design_submissions.designer_id
            AND COALESCE(ps.title, '') = COALESCE(design_submissions.title, '')
        );

        RAISE NOTICE 'Migrated data from design_submissions table';
    ELSE
        RAISE NOTICE 'No design_submissions table found, skipping migration';
    END IF;
EXCEPTION
    WHEN OTHERS THEN
        RAISE NOTICE 'Error migrating design_submissions: %', SQLERRM;
END $$;

-- Step 4: Migrate data from designer_work_submissions table (if exists)
DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'designer_work_submissions') THEN
        INSERT INTO project_submissions (
            tracking_request_id,
            designer_id,
            title,
            description,
            status,
            submission_source,
            file_name,
            file_path,
            file_type,
            file_size,
            work_type,
            version,
            files,
            submitted_at,
            created_at
        )
        SELECT
            tracking_request_id,
            designer_id,
            COALESCE(file_name, 'Work File'),
            COALESCE(notes, 'Work submission'),
            COALESCE(status, 'completed'),
            'work',
            file_name,
            file_path,
            file_type,
            file_size,
            COALESCE(work_type, 'draft'),
            COALESCE(version, 1),
            jsonb_build_array(
                jsonb_build_object(
                    'name', COALESCE(file_name, 'work_file'),
                    'path', COALESCE(file_path, ''),
                    'type', COALESCE(file_type, 'unknown'),
                    'size', COALESCE(file_size, 0)
                )
            ),
            COALESCE(created_at, NOW()),
            COALESCE(created_at, NOW())
        FROM designer_work_submissions
        WHERE designer_id IS NOT NULL -- Only migrate if designer_id exists
        AND NOT EXISTS (
            SELECT 1 FROM project_submissions ps
            WHERE COALESCE(ps.tracking_request_id, gen_random_uuid()) = COALESCE(designer_work_submissions.tracking_request_id, gen_random_uuid())
            AND ps.designer_id = designer_work_submissions.designer_id
            AND COALESCE(ps.file_name, '') = COALESCE(designer_work_submissions.file_name, '')
        );

        RAISE NOTICE 'Migrated data from designer_work_submissions table';
    ELSE
        RAISE NOTICE 'No designer_work_submissions table found, skipping migration';
    END IF;
EXCEPTION
    WHEN OTHERS THEN
        RAISE NOTICE 'Error migrating designer_work_submissions: %', SQLERRM;
END $$;

-- Step 5: Migrate data from old submissions table (if exists)
DO $$
DECLARE
    submissions_columns TEXT[];
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'submissions') THEN
        -- Check what columns exist in the submissions table
        SELECT array_agg(column_name) INTO submissions_columns
        FROM information_schema.columns
        WHERE table_name = 'submissions';

        RAISE NOTICE 'Found submissions table with columns: %', submissions_columns;

        -- Build dynamic query based on available columns
        IF 'file_url' = ANY(submissions_columns) THEN
            -- If file_url exists, use it
            INSERT INTO project_submissions (
                project_id,
                designer_id,
                title,
                description,
                status,
                submission_source,
                feedback,
                revision_requested,
                files,
                submitted_at,
                created_at,
                reviewed_at
            )
            SELECT
                project_id,
                designer_id,
                title,
                description,
                status,
                'project',
                COALESCE(feedback, ''),
                COALESCE(revision_requested, FALSE),
                CASE
                    WHEN file_url IS NOT NULL THEN
                        jsonb_build_array(
                            jsonb_build_object(
                                'url', file_url,
                                'type', COALESCE(file_type, 'unknown'),
                                'name', COALESCE(title, 'submission_file')
                            )
                        )
                    ELSE '[]'::jsonb
                END,
                created_at,
                created_at,
                COALESCE(updated_at, created_at)
            FROM submissions
            WHERE NOT EXISTS (
                SELECT 1 FROM project_submissions ps
                WHERE ps.project_id = submissions.project_id
                AND ps.designer_id = submissions.designer_id
                AND COALESCE(ps.title, '') = COALESCE(submissions.title, '')
            );
        ELSE
            -- If file_url doesn't exist, migrate without file data
            INSERT INTO project_submissions (
                project_id,
                designer_id,
                title,
                description,
                status,
                submission_source,
                feedback,
                revision_requested,
                files,
                submitted_at,
                created_at
            )
            SELECT
                project_id,
                designer_id,
                COALESCE(title, 'Migrated Submission'),
                COALESCE(description, ''),
                COALESCE(status, 'submitted'),
                'project',
                COALESCE(feedback, ''),
                COALESCE(revision_requested, FALSE),
                '[]'::jsonb, -- Empty files array
                created_at,
                created_at
            FROM submissions
            WHERE NOT EXISTS (
                SELECT 1 FROM project_submissions ps
                WHERE ps.project_id = submissions.project_id
                AND ps.designer_id = submissions.designer_id
                AND COALESCE(ps.title, '') = COALESCE(submissions.title, '')
            );
        END IF;

        RAISE NOTICE 'Migrated data from submissions table';
    ELSE
        RAISE NOTICE 'No submissions table found, skipping migration';
    END IF;
EXCEPTION
    WHEN OTHERS THEN
        RAISE NOTICE 'Error migrating submissions table: %', SQLERRM;
END $$;

-- Step 6: Create function to automatically create quality reviews for submissions
CREATE OR REPLACE FUNCTION create_quality_review_for_submission()
RETURNS TRIGGER AS $$
DECLARE
    review_id UUID;
BEGIN
    -- Only create quality review for submitted status and project/design submissions
    IF NEW.status = 'submitted' AND NEW.submission_source IN ('project', 'design') AND 
       (OLD IS NULL OR OLD.status != 'submitted') THEN
        
        -- Check if quality review already exists
        SELECT id INTO review_id
        FROM quality_reviews_new
        WHERE submission_id = NEW.id;
        
        -- Create quality review if it doesn't exist
        IF review_id IS NULL THEN
            INSERT INTO quality_reviews_new (
                project_id,
                submission_id,
                milestone_id,
                designer_id,
                review_type,
                status,
                priority,
                blocks_payment,
                sla_deadline,
                created_at
            ) VALUES (
                NEW.project_id,
                NEW.id,
                NEW.milestone_id,
                NEW.designer_id,
                'submission',
                'pending',
                'normal',
                TRUE,
                NOW() + INTERVAL '24 hours',
                NOW()
            ) RETURNING id INTO review_id;
            
            -- Link the quality review back to the submission
            UPDATE project_submissions 
            SET quality_review_id = review_id
            WHERE id = NEW.id;
            
            RAISE NOTICE 'Created quality review % for submission %', review_id, NEW.id;
        END IF;
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Step 7: Create trigger for automatic quality review creation
DROP TRIGGER IF EXISTS trigger_create_quality_review_for_submission ON project_submissions;
CREATE TRIGGER trigger_create_quality_review_for_submission
    AFTER INSERT OR UPDATE ON project_submissions
    FOR EACH ROW
    EXECUTE FUNCTION create_quality_review_for_submission();

-- Step 8: Create function to sync statuses between submissions and reviews
CREATE OR REPLACE FUNCTION sync_submission_review_status()
RETURNS TRIGGER AS $$
BEGIN
    -- When quality review status changes, update submission status
    IF NEW.status != OLD.status THEN
        UPDATE project_submissions 
        SET 
            status = CASE 
                WHEN NEW.status = 'approved' THEN 'approved'
                WHEN NEW.status = 'rejected' THEN 'needs_revision'
                WHEN NEW.status = 'in_review' THEN 'under_review'
                ELSE status
            END,
            reviewed_at = CASE WHEN NEW.status IN ('approved', 'rejected') THEN NOW() ELSE reviewed_at END,
            reviewed_by = CASE WHEN NEW.status IN ('approved', 'rejected') THEN NEW.reviewer_id ELSE reviewed_by END,
            feedback = CASE WHEN NEW.feedback IS NOT NULL THEN NEW.feedback ELSE feedback END,
            revision_requested = CASE WHEN NEW.status = 'rejected' THEN TRUE ELSE FALSE END
        WHERE quality_review_id = NEW.id;
        
        RAISE NOTICE 'Synced submission status for review %', NEW.id;
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Step 9: Create trigger for status synchronization
DROP TRIGGER IF EXISTS trigger_sync_submission_review_status ON quality_reviews_new;
CREATE TRIGGER trigger_sync_submission_review_status
    AFTER UPDATE ON quality_reviews_new
    FOR EACH ROW
    EXECUTE FUNCTION sync_submission_review_status();

-- Step 10: Update existing submissions to have quality reviews
DO $$
DECLARE
    submission_record RECORD;
    review_id UUID;
BEGIN
    FOR submission_record IN 
        SELECT id, project_id, milestone_id, designer_id 
        FROM project_submissions 
        WHERE status = 'submitted' 
        AND quality_review_id IS NULL 
        AND submission_source IN ('project', 'design')
    LOOP
        -- Create quality review for existing submissions
        INSERT INTO quality_reviews_new (
            project_id,
            submission_id,
            milestone_id,
            designer_id,
            review_type,
            status,
            priority,
            blocks_payment,
            sla_deadline,
            created_at
        ) VALUES (
            submission_record.project_id,
            submission_record.id,
            submission_record.milestone_id,
            submission_record.designer_id,
            'submission',
            'pending',
            'normal',
            TRUE,
            NOW() + INTERVAL '24 hours',
            NOW()
        ) RETURNING id INTO review_id;
        
        -- Link back to submission
        UPDATE project_submissions 
        SET quality_review_id = review_id
        WHERE id = submission_record.id;
    END LOOP;
    
    RAISE NOTICE 'Created quality reviews for existing submissions';
END $$;

-- Step 11: Verification queries
SELECT 'project_submissions' as table_name, COUNT(*) as count FROM project_submissions
UNION ALL
SELECT 'quality_reviews_new' as table_name, COUNT(*) as count FROM quality_reviews_new
UNION ALL
SELECT 'linked_submissions' as table_name, COUNT(*) as count FROM project_submissions WHERE quality_review_id IS NOT NULL;
