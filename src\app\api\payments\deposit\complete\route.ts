import { type NextRequest, NextResponse } from 'next/server';
import { createServerClient } from '@supabase/ssr';
import { cookies } from 'next/headers';
import { processDepositPaymentCompletion } from '@/lib/deposit-payment';
import { getPayPalAccessToken } from '@/lib/paypal-auth';

export async function POST(request: NextRequest) {
  try {
    // Check for Bearer token authentication first
    const authHeader = request.headers.get('Authorization');
    let supabase;

    if (authHeader && authHeader.startsWith('Bearer ')) {
      // Use Bearer token authentication
      const token = authHeader.split(' ')[1];

      const { createClient } = await import('@supabase/supabase-js');
      supabase = createClient(
        process.env.NEXT_PUBLIC_SUPABASE_URL!,
        process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
        {
          auth: {
            persistSession: false,
            autoRefreshToken: false
          },
          global: {
            headers: {
              Authorization: `Bearer ${token}`
            }
          }
        }
      );
    } else {
      // Fall back to cookie-based authentication
      const cookieStore = await cookies();
      supabase = createServerClient(
        process.env.NEXT_PUBLIC_SUPABASE_URL!,
        process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
        {
          cookies: {
            getAll() {
              return cookieStore.getAll();
            },
            setAll(cookiesToSet) {
              cookiesToSet.forEach(({ name, value, options }) => {
                cookieStore.set(name, value, options);
              });
            },
          },
        }
      );
    }

    const body = await request.json();

    const { orderId, payerId } = body;

    if (!orderId) {
      return NextResponse.json(
        { error: 'Order ID is required' },
        { status: 400 }
      );
    }

    // Get payment record
    const { data: payment, error: paymentError } = await supabase
      .from('payments')
      .select(`
        id,
        project_id,
        milestone_id,
        client_id,
        designer_id,
        amount,
        status,
        transaction_id,
        metadata
      `)
      .eq('transaction_id', orderId)
      .eq('payment_type', 'deposit')
      .single();

    if (paymentError || !payment) {
      return NextResponse.json(
        { error: 'Payment record not found' },
        { status: 404 }
      );
    }

    if (payment.status === 'completed') {
      return NextResponse.json(
        { error: 'Payment already completed' },
        { status: 400 }
      );
    }

    // Use centralized payment processor
    const { processPayPalPayment } = await import('@/lib/paypal-payment-processor');
    const result = await processPayPalPayment({
      orderId,
      skipCapture: false, // Completion route should try to capture
      source: 'completion_route'
    });

    if (!result.success) {
      // Handle payment processing errors
      if (result.error?.includes('INSTRUMENT_DECLINED') || result.error?.includes('INSUFFICIENT_FUNDS')) {
        console.log('❌ Payment instrument declined by bank/processor');
        
        // Determine specific error message based on the error
        let errorMessage = 'Payment was declined. Please try a different payment method.';
        let errorCode = 'INSTRUMENT_DECLINED';
        
        if (result.error.includes('INSUFFICIENT_FUNDS')) {
          errorMessage = 'Payment failed due to insufficient funds. Please check your account balance and try again, or use a different payment method.';
          errorCode = 'INSUFFICIENT_FUNDS';
        } else if (result.error.includes('INSTRUMENT_DECLINED')) {
          errorMessage = 'Payment was declined. This usually happens due to insufficient funds, expired card, or your bank blocking the transaction. Please check your account balance and try again, or use a different payment method.';
        }
        
        // Update payment record to reflect failure
        const { error: updateError } = await supabase
          .from('payments')
          .update({
            status: 'failed',
            metadata: {
              ...payment.metadata,
              failure_reason: errorCode,
              failed_at: new Date().toISOString(),
              error_message: result.error
            }
          })
          .eq('id', payment.id);

        if (updateError) {
          console.error('Error updating payment status:', updateError);
        }

        return NextResponse.json({
          success: false,
          error: errorMessage,
          errorCode,
          projectId: payment.project_id,
          milestoneId: payment.milestone_id
        }, { status: 402 });
      }
      
      // Handle "already captured but no transaction found" as a transient issue
      if (result.error?.includes('Payment already captured but no transaction found')) {
        console.log('⚠️ Transaction created after capture check - this is expected in race conditions');
        
        // Return success since the payment was actually captured
        return NextResponse.json({
          success: true,
          message: 'Payment completed successfully (transaction created after initial check)',
          projectId: payment.project_id,
          milestoneId: payment.milestone_id,
          amount: payment.amount,
          capturedBy: 'completion_route_recovery'
        });
      }
      
      return NextResponse.json({
        success: false,
        error: result.error || 'Payment processing failed',
        projectId: payment.project_id,
        milestoneId: payment.milestone_id
      }, { status: 400 });
    }

    // Payment processed successfully
    const message = result.alreadyProcessed 
      ? 'Payment was already completed by webhook'
      : 'Payment completed successfully';
    
    const capturedBy = result.alreadyProcessed ? 'webhook' : 'completion_route';

    return NextResponse.json({
      success: true,
      message,
      projectId: payment.project_id,
      milestoneId: payment.milestone_id,
      amount: payment.amount,
      capturedBy
    });

  } catch (error) {
    console.error('Error completing deposit payment:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
