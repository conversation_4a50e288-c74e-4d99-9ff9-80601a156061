'use client';

import type React from 'react';
import { useState, useEffect } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON>nt, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  Lock, 
  Clock, 
  AlertTriangle, 
  CheckCircle,
  CreditCard,
  User,
  Calendar
} from 'lucide-react';
import { supabase } from '@/lib/supabase';

interface ProjectStatusGateProps {
  projectId: string;
  userRole: 'client' | 'designer' | 'admin' | 'manager' | 'quality_team';
  children: React.ReactNode;
  fallbackMessage?: string;
}

interface ProjectStatus {
  id: string;
  title: string;
  status: string;
  client_id: string;
  designer_id: string;
  started_at: string | null;
  created_at: string;
  client_name?: string;
  designer_name?: string;
  deposit_milestone?: {
    id: string;
    amount: number;
    status: string;
    paid_at: string | null;
  };
}

export default function ProjectStatusGate({
  projectId,
  userRole,
  children,
  fallbackMessage
}: ProjectStatusGateProps) {
  const [projectStatus, setProjectStatus] = useState<ProjectStatus | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    fetchProjectStatus();
  }, [projectId]);

  const fetchProjectStatus = async () => {
    try {
      setLoading(true);
      
      const { data: project, error: projectError } = await supabase
        .from('projects')
        .select(`
          id,
          title,
          status,
          client_id,
          designer_id,
          started_at,
          created_at,
          client:client_id (
            full_name
          ),
          designer:designer_id (
            full_name
          ),
          project_milestones!inner (
            id,
            amount,
            status,
            paid_at,
            order_index
          )
        `)
        .eq('id', projectId)
        .order('order_index', { foreignTable: 'project_milestones', ascending: true })
        .single();

      if (projectError) throw projectError;

      const depositMilestone = project.project_milestones?.[0];
      
      setProjectStatus({
        ...project,
        client_name: project.client?.full_name,
        designer_name: project.designer?.full_name,
        deposit_milestone: depositMilestone
      });
    } catch (error) {
      console.error('Error fetching project status:', error);
      setError(error instanceof Error ? error.message : 'Failed to load project status');
    } finally {
      setLoading(false);
    }
  };

  const canAccessProject = (): boolean => {
    if (!projectStatus) return false;

    // Admin and managers can always access
    if (['admin', 'manager'].includes(userRole)) {
      return true;
    }

    // Quality team can access if project is active or in progress
    if (userRole === 'quality_team') {
      return ['active', 'in_progress', 'completed'].includes(projectStatus.status);
    }

    // Clients can always access their projects
    if (userRole === 'client') {
      return true;
    }

    // Designers can only access if deposit is paid and project is active
    if (userRole === 'designer') {
      return projectStatus.status === 'active' || 
             projectStatus.status === 'in_progress' ||
             projectStatus.status === 'completed';
    }

    return false;
  };

  const getStatusInfo = () => {
    if (!projectStatus) return null;

    const { status, deposit_milestone } = projectStatus;

    switch (status) {
      case 'draft':
        return {
          icon: <Clock className="h-5 w-5 text-yellow-500" />,
          title: 'Project Draft',
          message: 'Project is still being set up',
          color: 'border-yellow-200 bg-yellow-50'
        };
      
      case 'pending_deposit':
        return {
          icon: <CreditCard className="h-5 w-5 text-orange-500" />,
          title: 'Awaiting Initial Deposit',
          message: 'Client needs to pay the initial deposit before work can begin',
          color: 'border-orange-200 bg-orange-50'
        };
      
      case 'active':
        return {
          icon: <CheckCircle className="h-5 w-5 text-green-500" />,
          title: 'Project Active',
          message: 'Deposit paid - work can begin',
          color: 'border-green-200 bg-green-50'
        };
      
      case 'in_progress':
        return {
          icon: <CheckCircle className="h-5 w-5 text-blue-500" />,
          title: 'Work in Progress',
          message: 'Project is actively being worked on',
          color: 'border-blue-200 bg-blue-50'
        };
      
      default:
        return {
          icon: <AlertTriangle className="h-5 w-5 text-gray-500" />,
          title: 'Project Status Unknown',
          message: 'Unable to determine project status',
          color: 'border-gray-200 bg-gray-50'
        };
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
      </div>
    );
  }

  if (error) {
    return (
      <Alert variant="destructive">
        <AlertTriangle className="h-4 w-4" />
        <AlertDescription>{error}</AlertDescription>
      </Alert>
    );
  }

  // If user can access the project, render children
  if (canAccessProject()) {
    return <>{children}</>;
  }

  // Otherwise, show access denied message
  const statusInfo = getStatusInfo();

  return (
    <div className="container mx-auto px-4 py-8">
      <Card className={`max-w-2xl mx-auto ${statusInfo?.color || 'border-gray-200 bg-gray-50'}`}>
        <CardHeader className="text-center">
          <div className="flex justify-center mb-4">
            {statusInfo?.icon || <Lock className="h-12 w-12 text-gray-400" />}
          </div>
          <CardTitle className="text-xl">
            {statusInfo?.title || 'Access Restricted'}
          </CardTitle>
        </CardHeader>
        <CardContent className="text-center space-y-4">
          <p className="text-gray-600">
            {statusInfo?.message || fallbackMessage || 'You cannot access this project at this time.'}
          </p>

          {projectStatus && (
            <div className="bg-white rounded-lg p-4 border space-y-3">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium text-gray-700">Project:</span>
                <span className="text-sm text-gray-900">{projectStatus.title}</span>
              </div>
              
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium text-gray-700">Status:</span>
                <Badge variant="outline" className="capitalize">
                  {projectStatus.status.replace('_', ' ')}
                </Badge>
              </div>

              {projectStatus.client_name && (
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium text-gray-700">Client:</span>
                  <span className="text-sm text-gray-900">{projectStatus.client_name}</span>
                </div>
              )}

              {projectStatus.designer_name && (
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium text-gray-700">Designer:</span>
                  <span className="text-sm text-gray-900">{projectStatus.designer_name}</span>
                </div>
              )}

              {projectStatus.deposit_milestone && (
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium text-gray-700">Deposit:</span>
                  <div className="text-right">
                    <div className="text-sm font-medium text-gray-900">
                      ${projectStatus.deposit_milestone.amount?.toFixed(2) || '0.00'}
                    </div>
                    <Badge 
                      variant={projectStatus.deposit_milestone.paid_at ? "default" : "outline"}
                      className="text-xs"
                    >
                      {projectStatus.deposit_milestone.paid_at ? 'Paid' : 'Pending'}
                    </Badge>
                  </div>
                </div>
              )}

              <div className="flex items-center justify-between">
                <span className="text-sm font-medium text-gray-700">Created:</span>
                <span className="text-sm text-gray-900">
                  {new Date(projectStatus.created_at).toLocaleDateString()}
                </span>
              </div>
            </div>
          )}

          {userRole === 'designer' && projectStatus?.status === 'pending_deposit' && (
            <Alert>
              <Clock className="h-4 w-4" />
              <AlertDescription>
                The client needs to pay the initial deposit before you can start working on this project. 
                You'll be notified once the payment is received.
              </AlertDescription>
            </Alert>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
