-- FINAL FIX FOR PRIORITY CONSTRAINT VIOLATION
-- Execute this SQL in your Supabase dashboard or database admin panel

-- Step 1: Find and drop the problematic trigger
DROP TRIGGER IF EXISTS trigger_create_quality_review ON project_submissions;
DROP TRIGGER IF EXISTS trigger_create_quality_assignment ON project_submissions;
DROP TRIGGER IF EXISTS trigger_auto_assign_quality_review ON project_submissions;
DROP TRIGGER IF EXISTS trigger_auto_assign_quality_review_new ON project_submissions;
DROP TRIGGER IF EXISTS trigger_quality_review_auto_create ON project_submissions;

-- Step 2: Fix any existing records with invalid priority
UPDATE quality_reviews_new 
SET priority = 'normal' 
WHERE priority = 'medium' OR priority NOT IN ('low', 'normal', 'high', 'urgent');

-- Step 3: Create a corrected trigger function (if you want automatic quality review creation)
CREATE OR REPLACE FUNCTION create_quality_review_on_submission_fixed()
RETURNS TRIGGER AS $$
BEGIN
    -- Only create quality review for submitted status
    IF NEW.status = 'submitted' AND (OLD.status IS NULL OR OLD.status != 'submitted') THEN
        INSERT INTO quality_reviews_new (
            project_id,
            submission_id,
            milestone_id,
            designer_id,
            review_type,
            status,
            priority,
            created_at,
            updated_at
        ) VALUES (
            NEW.project_id,
            NEW.id,
            NEW.milestone_id,
            NEW.designer_id,
            'submission',
            'pending',
            'normal',  -- FIXED: Use 'normal' instead of 'medium'
            NOW(),
            NOW()
        );
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Step 4: Recreate the trigger with the fixed function
CREATE TRIGGER trigger_create_quality_review_fixed
    AFTER INSERT OR UPDATE ON project_submissions
    FOR EACH ROW
    EXECUTE FUNCTION create_quality_review_on_submission_fixed();

-- Step 5: Verify the fix
SELECT 'Fix completed - testing priority constraint' as status;
SELECT priority, COUNT(*) FROM quality_reviews_new GROUP BY priority;
