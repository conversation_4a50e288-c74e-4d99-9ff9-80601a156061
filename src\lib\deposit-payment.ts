/**
 * Deposit Payment System
 * Handles initial deposit payments after proposal acceptance
 */

import { supabase } from './supabase';

export interface DepositPaymentData {
  projectId: string;
  proposalId: string | null; // ✅ Allow null for existing projects without proposals
  clientId: string;
  designerId: string;
  amount: number; // Amount in cents
  description: string;
}

export interface DepositPaymentResult {
  success: boolean;
  paymentUrl?: string;
  error?: string;
  transactionId?: string;
}

/**
 * Create deposit payment after proposal acceptance
 */
export async function createDepositPayment(data: DepositPaymentData): Promise<DepositPaymentResult> {
  try {
    // Get project and proposal details
    const { data: project, error: projectError } = await supabase
      .from('projects')
      .select(`
        id,
        title,
        status,
        project_milestones!inner(
          id,
          title,
          amount,
          order_index,
          status
        )
      `)
      .eq('id', data.projectId)
      .order('order_index', { foreignTable: 'project_milestones', ascending: true })
      .single();

    if (projectError || !project) {
      throw new Error('Project not found');
    }

    // Get the first milestone (deposit milestone)
    const depositMilestone = project.project_milestones[0];
    if (!depositMilestone) {
      throw new Error('No deposit milestone found');
    }

    // Check if deposit is already paid
    if (depositMilestone.status === 'paid') {
      return {
        success: false,
        error: 'Deposit has already been paid'
      };
    }

    // Get authentication token
    const { data: { session } } = await supabase.auth.getSession();
    if (!session?.access_token) {
      throw new Error('Authentication required');
    }

    // Create payment using unified API that respects user's default payment method
    const response = await fetch('/api/payments/deposit/create', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${session.access_token}`
      },
      body: JSON.stringify({
        projectId: data.projectId,
        milestoneId: depositMilestone.id,
        proposalId: data.proposalId,
        clientId: data.clientId,
        designerId: data.designerId,
        amount: Math.round(depositMilestone.amount * 100), // Convert to cents
        description: `Initial deposit for ${project.title}`,
        paymentType: 'deposit'
      }),
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || 'Failed to create deposit payment');
    }

    const paymentData = await response.json();

    // Handle different payment methods
    let paymentUrl: string;
    if (paymentData.paymentMethod === 'paypal') {
      paymentUrl = paymentData.approvalUrl;
    } else if (paymentData.paymentMethod === 'tappay' || paymentData.paymentMethod === 'clickpay') {
      paymentUrl = paymentData.redirectUrl;
    } else {
      // Fallback for backward compatibility
      paymentUrl = paymentData.approvalUrl || paymentData.redirectUrl;
    }

    return {
      success: true,
      paymentUrl,
      transactionId: paymentData.orderId || paymentData.transactionId,
      paymentMethod: paymentData.paymentMethod
    };

  } catch (error) {
    console.error('Error creating deposit payment:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Failed to create deposit payment'
    };
  }
}

/**
 * Check if project deposit has been paid
 */
export async function checkDepositStatus(projectId: string): Promise<{
  isPaid: boolean;
  depositMilestone?: any;
  error?: string;
}> {
  try {
    const { data: milestones, error } = await supabase
      .from('project_milestones')
      .select('*')
      .eq('project_id', projectId)
      .order('order_index', { ascending: true })
      .limit(1);

    if (error) throw error;

    const depositMilestone = milestones?.[0];
    if (!depositMilestone) {
      return {
        isPaid: false,
        error: 'No deposit milestone found'
      };
    }

    return {
      isPaid: depositMilestone.status === 'paid',
      depositMilestone
    };
  } catch (error) {
    console.error('Error checking deposit status:', error);
    return {
      isPaid: false,
      error: error instanceof Error ? error.message : 'Failed to check deposit status'
    };
  }
}

/**
 * Process deposit payment completion
 */
export async function processDepositPaymentCompletion(
  projectId: string,
  milestoneId: string,
  transactionId: string
): Promise<{ success: boolean; error?: string }> {
  try {
    // Update milestone status to paid
    const { error: milestoneError } = await supabase
      .from('project_milestones')
      .update({
        status: 'paid',
        paid_at: new Date().toISOString()
      })
      .eq('id', milestoneId);

    if (milestoneError) throw milestoneError;

    // Update project status to active (can now start)
    const { error: projectError } = await supabase
      .from('projects')
      .update({
        status: 'active',
        started_at: new Date().toISOString()
      })
      .eq('id', projectId);

    if (projectError) throw projectError;

    // Activate next milestone if exists
    const { data: nextMilestone, error: nextError } = await supabase
      .from('project_milestones')
      .select('id')
      .eq('project_id', projectId)
      .eq('order_index', 1) // Second milestone
      .single();

    if (!nextError && nextMilestone) {
      await supabase
        .from('project_milestones')
        .update({ status: 'active' })
        .eq('id', nextMilestone.id);
    }

    // Create notification for designer
    await supabase.from('notifications').insert({
      user_id: (await supabase
        .from('projects')
        .select('designer_id')
        .eq('id', projectId)
        .single()).data?.designer_id,
      title: 'Project Started!',
      message: 'The client has paid the initial deposit. You can now start working on the project.',
      type: 'project_started',
      related_id: projectId
    });

    return { success: true };
  } catch (error) {
    console.error('Error processing deposit payment completion:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Failed to process deposit payment'
    };
  }
}

/**
 * Get deposit payment URL for existing projects
 */
export async function getExistingProjectDepositPayment(projectId: string): Promise<DepositPaymentResult> {
  try {
    // Get project details
    const { data: project, error: projectError } = await supabase
      .from('projects')
      .select(`
        id,
        title,
        client_id,
        designer_id,
        status,
        project_milestones!inner(
          id,
          title,
          amount,
          order_index,
          status
        )
      `)
      .eq('id', projectId)
      .order('order_index', { foreignTable: 'project_milestones', ascending: true })
      .single();

    if (projectError || !project) {
      throw new Error('Project not found');
    }

    const depositMilestone = project.project_milestones[0];
    if (!depositMilestone) {
      throw new Error('No deposit milestone found');
    }

    if (depositMilestone.status === 'paid') {
      return {
        success: false,
        error: 'Deposit has already been paid'
      };
    }

    return await createDepositPayment({
      projectId,
      proposalId: null, // ✅ Use null instead of empty string for existing projects
      clientId: project.client_id,
      designerId: project.designer_id,
      amount: Math.round(depositMilestone.amount * 100),
      description: `Initial deposit for ${project.title}`
    });

  } catch (error) {
    console.error('Error getting existing project deposit payment:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Failed to create deposit payment'
    };
  }
}
