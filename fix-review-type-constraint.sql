-- Fix review_type constraint violations in quality_reviews_new table
-- The constraint only allows ('proposal', 'submission', 'revision', 'final')
-- but some code is trying to use 'milestone' which violates the constraint

-- Step 1: Update any existing records with invalid review_type
UPDATE quality_reviews_new 
SET review_type = 'submission' 
WHERE review_type NOT IN ('proposal', 'submission', 'revision', 'final') 
   OR review_type IS NULL;

-- Step 2: Fix any database functions that use 'milestone' as review_type
-- Drop the existing function first (to handle return type changes)
DROP FUNCTION IF EXISTS auto_assign_quality_review(UUID, TEXT, TEXT, TEXT);

-- Update the auto-assignment function
CREATE OR REPLACE FUNCTION auto_assign_quality_review(
  review_id UUID,
  review_type TEXT DEFAULT 'submission', -- Fixed: Use 'submission' instead of 'milestone'
  project_complexity TEXT DEFAULT 'normal',
  urgency TEXT DEFAULT 'normal'
)
RETURNS VOID AS $$
DECLARE
  selected_reviewer_id UUID;
  workload_score INTEGER;
  max_workload INTEGER := 10;
BEGIN
  -- Get available quality team members with lowest workload
  SELECT id INTO selected_reviewer_id
  FROM profiles 
  WHERE role = 'quality_team' 
    AND is_active = true
    AND COALESCE(quality_current_workload, 0) < max_workload
  ORDER BY COALESCE(quality_current_workload, 0) ASC, 
           RANDOM() -- Add randomness for equal workloads
  LIMIT 1;

  -- If reviewer found, assign the review
  IF selected_reviewer_id IS NOT NULL THEN
    UPDATE quality_reviews_new 
    SET reviewer_id = selected_reviewer_id,
        status = 'assigned'
    WHERE id = review_id;

    -- Increment reviewer workload
    UPDATE profiles 
    SET quality_current_workload = COALESCE(quality_current_workload, 0) + 1
    WHERE id = selected_reviewer_id;
  END IF;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Step 3: Fix any triggers that might create quality reviews with 'milestone' type
-- Update the escrow integration function
CREATE OR REPLACE FUNCTION create_quality_review_for_escrow_release(
  p_project_id UUID,
  p_submission_id UUID,
  p_milestone_id UUID,
  p_designer_id UUID
)
RETURNS UUID AS $$
DECLARE
  v_quality_review_id UUID;
  v_release_id UUID;
BEGIN
  -- Check if quality review already exists for this submission
  SELECT id INTO v_quality_review_id
  FROM quality_reviews_new
  WHERE submission_id = p_submission_id
    AND project_id = p_project_id;

  -- If no existing quality review, create one
  IF v_quality_review_id IS NULL THEN
    INSERT INTO quality_reviews_new (
      project_id,
      submission_id,
      milestone_id,
      designer_id,
      review_type,
      status,
      priority,
      blocks_payment,
      sla_deadline
    ) VALUES (
      p_project_id,
      p_submission_id,
      p_milestone_id,
      p_designer_id,
      'submission', -- Fixed: Use 'submission' instead of 'milestone'
      'pending',
      'high',
      TRUE,
      NOW() + INTERVAL '24 hours'
    ) RETURNING id INTO v_quality_review_id;
  END IF;

  RETURN v_quality_review_id;
END;
$$ LANGUAGE plpgsql;

-- Step 4: Verify the fix
SELECT review_type, COUNT(*) 
FROM quality_reviews_new 
GROUP BY review_type;

-- Step 5: Show the valid constraint values
SELECT 
  constraint_name,
  check_clause
FROM information_schema.check_constraints 
WHERE constraint_name LIKE '%review_type%';
