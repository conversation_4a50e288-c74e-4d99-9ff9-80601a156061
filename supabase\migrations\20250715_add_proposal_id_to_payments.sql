-- Migration: Add proposal_id to payments table and make quote_id nullable
-- Date: 2025-07-15
-- Description: Update payments table to reference proposals instead of requiring quotes for deposit payments

-- Add proposal_id column to payments table
ALTER TABLE payments 
ADD COLUMN proposal_id uuid REFERENCES project_proposals_enhanced(id);

-- Make quote_id nullable since we don't need it for deposit payments
ALTER TABLE payments 
ALTER COLUMN quote_id DROP NOT NULL;

-- Add index for better performance on proposal_id lookups
CREATE INDEX idx_payments_proposal_id ON payments(proposal_id);

-- Add comment to document the change
COMMENT ON COLUMN payments.proposal_id IS 'References the proposal that triggered this payment (especially for deposit payments)';
COMMENT ON COLUMN payments.quote_id IS 'References formal quotes when applicable (nullable for deposit payments based on proposals)';
