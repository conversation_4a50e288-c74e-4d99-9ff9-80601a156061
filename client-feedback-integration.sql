-- Client Feedback Integration for Quality-First Workflow
-- Purpose: Enable client feedback on quality-approved submissions only
-- Date: 2025-07-16
-- Workflow: Designer → Quality Team → Client → Payment

-- Create client_feedback table for tracking client feedback on quality-approved submissions
CREATE TABLE IF NOT EXISTS client_feedback (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    submission_id UUID NOT NULL,
    project_id UUID NOT NULL REFERENCES projects(id) ON DELETE CASCADE,
    client_id UUID NOT NULL REFERENCES profiles(id) ON DELETE CASCADE,
    feedback_type VARCHAR(50) NOT NULL CHECK (feedback_type IN ('approve', 'request_revision')),
    feedback_text TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Add indexes for performance
CREATE INDEX IF NOT EXISTS idx_client_feedback_submission_id ON client_feedback(submission_id);
CREATE INDEX IF NOT EXISTS idx_client_feedback_project_id ON client_feedback(project_id);
CREATE INDEX IF NOT EXISTS idx_client_feedback_client_id ON client_feedback(client_id);

-- Add RLS policy for client feedback
ALTER TABLE client_feedback ENABLE ROW LEVEL SECURITY;

-- Drop existing policies if they exist before creating new ones
DROP POLICY IF EXISTS "Clients can view their own feedback" ON client_feedback;
CREATE POLICY "Clients can view their own feedback" ON client_feedback
    FOR SELECT USING (auth.uid() = client_id);

DROP POLICY IF EXISTS "Clients can insert their own feedback" ON client_feedback;
CREATE POLICY "Clients can insert their own feedback" ON client_feedback
    FOR INSERT WITH CHECK (auth.uid() = client_id);

DROP POLICY IF EXISTS "Quality team can view all feedback" ON client_feedback;
CREATE POLICY "Quality team can view all feedback" ON client_feedback
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM profiles 
            WHERE profiles.id = auth.uid() 
            AND profiles.role IN ('quality_team', 'manager', 'admin')
        )
    );

-- Add client approval tracking columns to quality_reviews_new table if they don't exist
DO $$
BEGIN
    -- Add client_approval_date column
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'quality_reviews_new' 
                   AND column_name = 'client_approval_date') THEN
        ALTER TABLE quality_reviews_new ADD COLUMN client_approval_date TIMESTAMP WITH TIME ZONE;
    END IF;

    -- Add client_feedback_id column
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'quality_reviews_new' 
                   AND column_name = 'client_feedback_id') THEN
        ALTER TABLE quality_reviews_new ADD COLUMN client_feedback_id UUID REFERENCES client_feedback(id);
    END IF;
END $$;

-- Function to automatically handle client approval on quality-approved submissions
CREATE OR REPLACE FUNCTION handle_client_approval_quality_workflow()
RETURNS TRIGGER AS $$
DECLARE
    submission_milestone_id UUID;
    submission_project_id UUID;
    submission_designer_id UUID;
    quality_review_exists BOOLEAN := FALSE;
BEGIN
    -- Only process approval feedback
    IF NEW.feedback_type = 'approve' THEN
        -- Try to get milestone info from project_submissions first
        SELECT milestone_id, project_id, designer_id, 
               (quality_review_id IS NOT NULL) as has_quality_review
        INTO submission_milestone_id, submission_project_id, submission_designer_id, quality_review_exists
        FROM project_submissions 
        WHERE id = NEW.submission_id;
        
        -- If not found in project_submissions, try submissions table (legacy)
        IF submission_milestone_id IS NULL THEN
            SELECT project_id, designer_id
            INTO submission_project_id, submission_designer_id
            FROM submissions 
            WHERE id = NEW.submission_id 
            AND status = 'approved'; -- Only process quality-approved legacy submissions
            
            -- For legacy submissions, assume quality reviewed if status is 'approved'
            IF submission_project_id IS NOT NULL THEN
                quality_review_exists := TRUE;
            END IF;
        END IF;
        
        -- Only proceed if submission has been quality reviewed
        IF quality_review_exists THEN
            -- Update quality review with client approval info if milestone exists
            IF submission_milestone_id IS NOT NULL THEN
                UPDATE quality_reviews_new 
                SET 
                    client_approval_date = NEW.created_at,
                    client_feedback_id = NEW.id,
                    updated_at = NEW.created_at
                WHERE submission_id = NEW.submission_id;
                    
                -- Check if milestone becomes payment eligible
                PERFORM check_milestone_payment_eligibility(submission_milestone_id);
            END IF;
        ELSE
            -- Raise warning if trying to approve non-quality-reviewed submission
            RAISE WARNING 'Client attempting to approve submission % that has not been quality reviewed', NEW.submission_id;
        END IF;
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger for client approval on quality-approved submissions only
DROP TRIGGER IF EXISTS trigger_client_approval_quality_workflow ON client_feedback;
CREATE TRIGGER trigger_client_approval_quality_workflow
    AFTER INSERT ON client_feedback
    FOR EACH ROW
    EXECUTE FUNCTION handle_client_approval_quality_workflow();

-- Function to check milestone payment eligibility (only for quality + client approved)
CREATE OR REPLACE FUNCTION check_milestone_payment_eligibility(milestone_uuid UUID)
RETURNS BOOLEAN AS $$
DECLARE
    all_approved BOOLEAN := FALSE;
BEGIN
    -- Check if all submissions for this milestone are both quality-approved AND client-approved
    SELECT COUNT(*) = 0 
    INTO all_approved
    FROM project_submissions ps
    LEFT JOIN quality_reviews_new qr ON ps.quality_review_id = qr.id
    WHERE ps.milestone_id = milestone_uuid 
    AND (
        ps.status != 'approved' OR  -- Not client approved
        qr.status != 'approved' OR  -- Not quality approved
        qr.client_approval_date IS NULL  -- No client approval date
    );
    
    -- If all are both quality and client approved, mark milestone as payment eligible
    IF all_approved THEN
        UPDATE project_milestones 
        SET 
            status = 'payment_eligible',
            eligible_for_payment_at = NOW(),
            updated_at = NOW()
        WHERE id = milestone_uuid;
        
        RETURN TRUE;
    END IF;
    
    RETURN FALSE;
END;
$$ LANGUAGE plpgsql;

-- Create a view for client-visible submissions (quality-approved only)
-- FIXED: Both parts of UNION now have exactly the same number of columns
CREATE OR REPLACE VIEW client_submissions_view AS
-- Project submissions that have been quality approved
SELECT 
    ps.id,
    ps.project_id,
    ps.milestone_id,
    ps.designer_id,
    ps.title,
    ps.description,
    ps.status,
    ps.version,
    ps.submission_type,
    ps.files,
    ps.submitted_at,
    ps.reviewed_at,
    ps.approved_at,
    ps.created_at,
    ps.updated_at,
    ps.feedback,
    ps.revision_requested,
    ps.work_type,
    qr.status as quality_status,
    qr.reviewed_at as quality_reviewed_at,
    cf.feedback_type as client_feedback_type,
    cf.feedback_text as client_feedback_text,
    cf.created_at as client_feedback_date,
    'project_submissions' as source_table,
    CASE 
        WHEN ps.status = 'approved' AND qr.status = 'approved' THEN 'fully_approved'
        WHEN qr.status = 'approved' AND ps.status != 'approved' THEN 'quality_approved_pending_client'
        WHEN ps.status = 'needs_revision' THEN 'needs_revision'
        ELSE ps.status
    END as overall_status
FROM project_submissions ps
INNER JOIN quality_reviews_new qr ON ps.quality_review_id = qr.id
LEFT JOIN client_feedback cf ON cf.submission_id = ps.id
WHERE qr.status = 'approved'  -- Only quality-approved submissions visible to clients

UNION ALL

-- Legacy submissions that are quality approved (status = 'approved')
-- FIXED: Matching column count and order with project_submissions
SELECT 
    s.id,
    s.project_id,
    NULL as milestone_id,
    s.designer_id,
    s.title,
    s.description,
    s.status,
    1 as version,
    'legacy' as submission_type,
    NULL as files,
    s.created_at as submitted_at,
    s.updated_at as reviewed_at,
    CASE WHEN s.status = 'approved' THEN s.updated_at ELSE NULL END as approved_at,
    s.created_at,
    s.updated_at,
    s.feedback,
    s.revision_requested,
    NULL as work_type,
    'approved' as quality_status,  -- Assume legacy approved submissions are quality approved
    s.updated_at as quality_reviewed_at,  -- FIXED: Added this missing column
    cf.feedback_type as client_feedback_type,
    cf.feedback_text as client_feedback_text,
    cf.created_at as client_feedback_date,
    'submissions' as source_table,
    CASE 
        WHEN s.status = 'approved' THEN 'fully_approved'
        WHEN s.status = 'needs_revision' THEN 'needs_revision'
        ELSE 'quality_approved_pending_client'
    END as overall_status
FROM submissions s
LEFT JOIN client_feedback cf ON cf.submission_id = s.id
WHERE s.status IN ('approved', 'needs_revision');  -- Only quality-approved legacy submissions

COMMENT ON VIEW client_submissions_view IS 'Client-visible submissions: only those that have passed quality review';

-- Grant permissions
GRANT SELECT ON client_submissions_view TO authenticated;
GRANT ALL ON client_feedback TO authenticated;

-- Add helpful comments
COMMENT ON TABLE client_feedback IS 'Client feedback on quality-approved submissions only (Quality-First Workflow)';
COMMENT ON FUNCTION handle_client_approval_quality_workflow() IS 'Handles client approval only for quality-approved submissions';
COMMENT ON FUNCTION check_milestone_payment_eligibility(UUID) IS 'Checks payment eligibility requiring both quality AND client approval';
