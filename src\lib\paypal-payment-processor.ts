import { createClient } from '@supabase/supabase-js';
import { getPayPalAccessToken } from './paypal-auth';

interface PaymentProcessingResult {
  success: boolean;
  payment?: any;
  transaction?: any;
  error?: string;
  alreadyProcessed?: boolean;
}

interface PaymentProcessingOptions {
  orderId: string;
  skipCapture?: boolean; // If true, assume payment is already captured
  source: 'webhook' | 'completion_route';
}

// PayPal API base URL
const PAYPAL_API_BASE = process.env.NODE_ENV === 'production'
  ? 'https://api-m.paypal.com'
  : 'https://api-m.sandbox.paypal.com';

/**
 * Centralized, idempotent PayPal payment processing
 * This function ensures that payments are processed only once, regardless of whether
 * they come from webhooks or completion routes
 */
export async function processPayPalPayment(
  options: PaymentProcessingOptions
): Promise<PaymentProcessingResult> {
  const { orderId, skipCapture = false, source } = options;
  
  const supabase = createClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.SUPABASE_SERVICE_ROLE_KEY!,
    { auth: { persistSession: false } }
  );

  console.log(`🔄 Starting PayPal payment processing for order ${orderId} from ${source}`);

  try {
    // Step 1: Check if payment is already processed
    const { data: existingPayment, error: paymentError } = await supabase
      .from('payments')
      .select('*')
      .eq('transaction_id', orderId)
      .eq('payment_type', 'deposit')
      .single();

    if (paymentError && paymentError.code !== 'PGRST116') {
      console.error('Error checking existing payment:', paymentError);
      return { success: false, error: 'Database error checking payment status' };
    }

    if (existingPayment && existingPayment.status === 'completed') {
      console.log('✅ Payment already processed, returning existing data');
      return { 
        success: true, 
        payment: existingPayment,
        alreadyProcessed: true 
      };
    }

    // Step 2: Check if transaction already exists (from webhook)
    const { data: existingTransaction, error: transactionError } = await supabase
      .from('transactions')
      .select('*')
      .eq('external_transaction_id', orderId)
      .eq('status', 'completed')
      .single();

    if (transactionError && transactionError.code !== 'PGRST116') {
      console.error('Error checking existing transaction:', transactionError);
      return { success: false, error: 'Database error checking transaction status' };
    }

    let captureData: any = null;
    let captureId: string | null = null;

    // Step 3: Handle capture based on source and existing data
    if (existingTransaction) {
      console.log('✅ Transaction already exists from webhook, skipping capture');
      captureId = existingTransaction.external_transaction_id;
      // If we have existing transaction, we don't need to capture again
    } else if (!skipCapture) {
      console.log('🔄 Capturing PayPal payment...');
      
      // Try to capture the payment
      const accessToken = await getPayPalAccessToken();
      const captureResponse = await fetch(
        `${PAYPAL_API_BASE}/v2/checkout/orders/${orderId}/capture`,
        {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${accessToken}`
          }
        }
      );

      if (!captureResponse.ok) {
        const errorText = await captureResponse.text();
        console.error('PayPal capture error:', errorText);
        
        // Handle ORDER_ALREADY_CAPTURED gracefully
        if (errorText.includes('ORDER_ALREADY_CAPTURED')) {
          console.log('✅ Order already captured, checking for existing transaction...');
          
          // Try to find the transaction that was created by webhook
          // Wait a bit and retry if not found (race condition handling)
          let webhookTransaction = null;
          let retryCount = 0;
          const maxRetries = 3;
          
          while (!webhookTransaction && retryCount < maxRetries) {
            const { data } = await supabase
              .from('transactions')
              .select('*')
              .or(`external_transaction_id.eq.${orderId},notes.ilike.%${orderId}%`)
              .eq('status', 'completed')
              .single();
            
            webhookTransaction = data;
            
            if (!webhookTransaction && retryCount < maxRetries - 1) {
              console.log(`🔄 Transaction not found yet, retrying in 2 seconds... (${retryCount + 1}/${maxRetries})`);
              await new Promise(resolve => setTimeout(resolve, 2000));
            }
            
            retryCount++;
          }

          if (webhookTransaction) {
            console.log('✅ Found existing transaction from webhook');
            captureId = webhookTransaction.external_transaction_id;
            captureData = null; // We don't have fresh capture data, but that's OK
          } else {
            console.log('⚠️ Payment captured but transaction still being created - this is a race condition');
            // Don't fail here - let the process continue and the transaction will be created
            captureId = orderId; // Use the order ID as the capture ID
            captureData = null;
          }
        } else {
          // Handle other errors (like insufficient funds, etc.)
          let parsedError;
          try {
            parsedError = JSON.parse(errorText);
          } catch {
            parsedError = { message: errorText };
          }
          
          return { 
            success: false, 
            error: parsedError.details?.[0]?.description || parsedError.message || 'Payment capture failed' 
          };
        }
      } else {
        captureData = await captureResponse.json();
        captureId = captureData.id;
        console.log('✅ Payment captured successfully');
      }
    }

    // Step 4: Create or update transaction record
    let transaction = existingTransaction;
    if (!transaction && captureId) {
      console.log('🔄 Creating transaction record...');
      
      // Get payment details to extract project info
      if (!existingPayment) {
        return { success: false, error: 'Payment record not found' };
      }

      const { data: project } = await supabase
        .from('projects')
        .select('designer_id, title')
        .eq('id', existingPayment.project_id)
        .single();

      if (!project) {
        return { success: false, error: 'Project not found' };
      }

      // Calculate fees
      const amount = existingPayment.amount;
      const platformFeeRate = 0.15; // 15% platform fee
      const platformFee = Math.round(amount * platformFeeRate * 100) / 100;
      const paypalFee = amount * 0.029; // Estimated PayPal fee
      const designerPayout = Math.round((amount - platformFee - paypalFee) * 100) / 100;

      const transactionId = `paypal-${captureId}-${Date.now()}`;

      const { data: newTransaction, error: createError } = await supabase
        .from('transactions')
        .insert({
          transaction_id: transactionId,
          amount: amount,
          status: 'completed',
          type: 'payment',
          project_id: existingPayment.project_id,
          milestone_id: existingPayment.milestone_id,
          client_id: existingPayment.client_id,
          designer_id: project.designer_id,
          platform_fee: platformFee,
          processing_fee: paypalFee,
          designer_amount: designerPayout,
          notes: `PayPal payment processed via ${source} - Order: ${orderId}`,
          description: `PayPal payment for ${project.title}`,
          payment_method: 'paypal',
          external_transaction_id: captureId,
          processed_at: new Date().toISOString()
        })
        .select()
        .single();

      if (createError) {
        console.error('Error creating transaction:', createError);
        
        // Check if it's a duplicate key error (race condition)
        if (createError.code === '23505' || createError.message?.includes('duplicate')) {
          console.log('🔄 Transaction already exists (race condition), fetching existing...');
          
          // Try to fetch the existing transaction
          const { data: existingTxn } = await supabase
            .from('transactions')
            .select('*')
            .or(`external_transaction_id.eq.${captureId},transaction_id.eq.${transactionId}`)
            .single();
          
          if (existingTxn) {
            transaction = existingTxn;
            console.log('✅ Found existing transaction:', transaction.id);
          } else {
            return { success: false, error: 'Failed to create or find transaction record' };
          }
        } else {
          return { success: false, error: 'Failed to create transaction record' };
        }
      } else {
        transaction = newTransaction;
        console.log('✅ Transaction created:', transaction.id);
      }
    }

    // Step 5: Update payment record
    if (existingPayment && existingPayment.status !== 'completed') {
      console.log('🔄 Updating payment record...');
      
      const { error: updateError } = await supabase
        .from('payments')
        .update({
          status: 'completed',
          completed_at: new Date().toISOString(),
          metadata: {
            ...existingPayment.metadata,
            processed_by: source,
            processed_at: new Date().toISOString(),
            paypal_capture_id: captureId,
            transaction_id: transaction?.id
          }
        })
        .eq('id', existingPayment.id);

      if (updateError) {
        console.error('Error updating payment:', updateError);
        return { success: false, error: 'Failed to update payment record' };
      }

      console.log('✅ Payment record updated');
    }

    // Step 6: Process deposit completion (milestone updates, etc.)
    if (existingPayment && transaction) {
      console.log('🔄 Processing deposit completion...');
      
      const { processDepositPaymentCompletion } = await import('./deposit-payment');
      const completionResult = await processDepositPaymentCompletion(
        existingPayment.project_id,
        existingPayment.milestone_id,
        orderId
      );

      if (!completionResult.success) {
        console.error('Error processing deposit completion:', completionResult.error);
        // Don't fail the entire process - payment was successful
      }
    }

    // Step 7: Create escrow record for the deposit using the unified escrow system
    if (existingPayment && transaction) {
      try {
        console.log('🔄 Creating escrow hold...');
        const { EscrowManager } = await import('./escrow-manager');

        const escrowResult = await EscrowManager.createEscrowHold({
          transactionId: transaction.id, // ✅ Use the database transaction ID, not PayPal order ID
          projectId: existingPayment.project_id,
          milestoneId: existingPayment.milestone_id,
          grossAmount: existingPayment.amount,
          platformFee: existingPayment.amount * 0.15, // 15% platform fee
          processingFee: existingPayment.amount * 0.029, // 2.9% PayPal fee
          holdReason: 'paypal_deposit_payment',
          requiresManagerApproval: true,
          requiresQualityApproval: false,
          autoReleaseDays: 30
        });

        if (!escrowResult.success) {
          console.error('Failed to create escrow hold:', escrowResult.error);
        } else {
          console.log('✅ Escrow hold created successfully:', escrowResult.hold?.id);
        }
      } catch (escrowError) {
        console.error('Error creating escrow hold:', escrowError);
        // Continue execution - escrow creation failure shouldn't block payment completion
      }
    }

    console.log('✅ Payment processing completed successfully');

    return {
      success: true,
      payment: existingPayment,
      transaction: transaction
    };

  } catch (error) {
    console.error('Error in processPayPalPayment:', error);
    return { 
      success: false, 
      error: error instanceof Error ? error.message : 'Unknown error' 
    };
  }
}

/**
 * Check if a PayPal order has already been processed
 */
export async function isPayPalOrderProcessed(orderId: string): Promise<boolean> {
  const supabase = createClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.SUPABASE_SERVICE_ROLE_KEY!,
    { auth: { persistSession: false } }
  );

  // Check both payments and transactions tables
  const [paymentCheck, transactionCheck] = await Promise.all([
    supabase
      .from('payments')
      .select('status')
      .eq('transaction_id', orderId)
      .eq('status', 'completed')
      .single(),
    supabase
      .from('transactions')
      .select('status')
      .eq('external_transaction_id', orderId)
      .eq('status', 'completed')
      .single()
  ]);

  return !paymentCheck.error || !transactionCheck.error;
}
