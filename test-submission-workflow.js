// Test the specific submission workflow that was failing
const { createClient } = require('@supabase/supabase-js');
require('dotenv').config({ path: '.env.local' });

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_SERVICE_KEY || process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('Missing Supabase configuration.');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

async function testSubmissionWorkflow() {
  try {
    console.log('Testing submission workflow...');
    
    // Simulate what happens in the submission creation
    // Step 1: Create a project_submissions record (this would trigger the quality review creation)
    console.log('Step 1: Testing project_submissions insert...');
    
    const testSubmissionData = {
      project_id: '48f13612-175b-4e2f-bf78-fb40efc3e73c', // Using existing project from previous test
      description: 'Test submission for priority constraint fix',
      status: 'submitted',
      submission_type: 'milestone',
      files: []
    };
    
    const { data: submission, error: submissionError } = await supabase
      .from('project_submissions')
      .insert(testSubmissionData)
      .select()
      .single();
    
    if (submissionError) {
      console.error('❌ Error creating submission:', submissionError);
      console.error('This might be the original error you were seeing');
      return;
    }
    
    console.log('✅ Submission created successfully:', submission.id);
    
    // Step 2: Check if any quality review was created automatically
    console.log('Step 2: Checking for automatically created quality reviews...');
    
    const { data: qualityReviews, error: reviewError } = await supabase
      .from('quality_reviews_new')
      .select('*')
      .eq('submission_id', submission.id);
    
    if (reviewError) {
      console.error('Error checking quality reviews:', reviewError);
    } else {
      console.log(`Found ${qualityReviews.length} quality reviews for this submission`);
      if (qualityReviews.length > 0) {
        console.log('Quality review priorities:', qualityReviews.map(r => r.priority));
      }
    }
    
    // Step 3: Test manual quality review creation with our fixed code
    console.log('Step 3: Testing manual quality review creation...');
    
    const qualityReviewData = {
      project_id: testSubmissionData.project_id,
      submission_id: submission.id,
      review_type: 'submission',
      status: 'pending',
      priority: 'normal', // This should work now
      revision_count: 0
    };
    
    const { data: manualReview, error: manualReviewError } = await supabase
      .from('quality_reviews_new')
      .insert(qualityReviewData)
      .select()
      .single();
    
    if (manualReviewError) {
      console.error('❌ Error creating manual quality review:', manualReviewError);
      console.error('This indicates the priority constraint is still an issue');
    } else {
      console.log('✅ Manual quality review created successfully:', manualReview.id);
      console.log('Priority used:', manualReview.priority);
    }
    
    // Clean up test data
    console.log('Step 4: Cleaning up test data...');
    
    if (manualReview) {
      await supabase
        .from('quality_reviews_new')
        .delete()
        .eq('id', manualReview.id);
    }
    
    // Delete any auto-created reviews
    await supabase
      .from('quality_reviews_new')
      .delete()
      .eq('submission_id', submission.id);
    
    // Delete the test submission
    await supabase
      .from('project_submissions')
      .delete()
      .eq('id', submission.id);
    
    console.log('✅ Test data cleaned up');
    
    console.log('\\n🎉 Submission workflow test completed successfully!');
    console.log('\\nThe priority constraint issue should be resolved.');
    console.log('Users should now be able to create submissions without the 400 error.');
    
  } catch (error) {
    console.error('❌ Error in submission workflow test:', error);
  }
}

testSubmissionWorkflow();
