'use client';

import { useState } from 'react';
import { useOptimizedAuth } from '@/hooks/useOptimizedAuth';
import { Button } from '@/components/ui/button';

export default function AdminDebugPage() {
  const { user, profile, loading } = useOptimizedAuth();
  const [debugInfo, setDebugInfo] = useState<any>(null);
  const [testResults, setTestResults] = useState<any[]>([]);

  const runAuthTest = async () => {
    const results = [];
    
    try {
      // Test 1: Check current user auth
      results.push({
        test: 'Current User Auth',
        result: user ? 'PASS' : 'FAIL',
        details: { userId: user?.id, email: user?.email }
      });

      // Test 2: Check user profile
      results.push({
        test: 'User Profile',
        result: profile ? 'PASS' : 'FAIL',
        details: { role: profile?.role, fullName: profile?.full_name }
      });

      // Test 3: Test API endpoint access
      const response = await fetch('/api/admin/users/test-auth', {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      const data = await response.json();
      
      results.push({
        test: 'API Auth Test',
        result: response.ok ? 'PASS' : 'FAIL',
        details: { status: response.status, data }
      });

    } catch (error) {
      results.push({
        test: 'API Auth Test',
        result: 'ERROR',
        details: { error: error instanceof Error ? error.message : 'Unknown error' }
      });
    }

    setTestResults(results);
  };

  const testDeleteEndpoint = async (testUserId: string) => {
    try {
      const response = await fetch(`/api/admin/users/${testUserId}/delete`, {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      const data = await response.json();
      
      setDebugInfo({
        endpoint: `/api/admin/users/${testUserId}/delete`,
        status: response.status,
        statusText: response.statusText,
        headers: Object.fromEntries(response.headers.entries()),
        data,
        timestamp: new Date().toISOString()
      });

    } catch (error) {
      setDebugInfo({
        endpoint: `/api/admin/users/${testUserId}/delete`,
        error: error instanceof Error ? error.message : 'Unknown error',
        timestamp: new Date().toISOString()
      });
    }
  };

  if (loading) {
    return <div className="p-8">Loading...</div>;
  }

  return (
    <div className="p-8 max-w-4xl mx-auto">
      <h1 className="text-2xl font-bold mb-6">Admin Authorization Debug</h1>
      
      {/* Current User Info */}
      <div className="bg-gray-50 p-4 rounded-lg mb-6">
        <h2 className="text-lg font-semibold mb-3">Current User Information</h2>
        <div className="grid grid-cols-2 gap-4 text-sm">
          <div>
            <strong>User ID:</strong> {user?.id || 'Not logged in'}
          </div>
          <div>
            <strong>Email:</strong> {user?.email || 'N/A'}
          </div>
          <div>
            <strong>Role:</strong> {profile?.role || 'N/A'}
          </div>
          <div>
            <strong>Full Name:</strong> {profile?.full_name || 'N/A'}
          </div>
        </div>
      </div>

      {/* Test Buttons */}
      <div className="space-y-4 mb-6">
        <Button onClick={runAuthTest} className="mr-4">
          Run Authorization Tests
        </Button>
        
        <Button 
          onClick={() => testDeleteEndpoint('test-user-id')} 
          variant="outline"
          className="mr-4"
        >
          Test Delete Endpoint (Invalid ID)
        </Button>
        
        <Button 
          onClick={() => testDeleteEndpoint(user?.id || 'self')} 
          variant="outline"
          className="mr-4"
        >
          Test Delete Endpoint (Self)
        </Button>
      </div>

      {/* Test Results */}
      {testResults.length > 0 && (
        <div className="bg-white border rounded-lg p-4 mb-6">
          <h2 className="text-lg font-semibold mb-3">Test Results</h2>
          {testResults.map((result, index) => (
            <div key={index} className="mb-3 p-3 border rounded">
              <div className="flex items-center justify-between mb-2">
                <strong>{result.test}</strong>
                <span className={`px-2 py-1 rounded text-sm ${
                  result.result === 'PASS' ? 'bg-green-100 text-green-800' :
                  result.result === 'FAIL' ? 'bg-red-100 text-red-800' :
                  'bg-yellow-100 text-yellow-800'
                }`}>
                  {result.result}
                </span>
              </div>
              <pre className="text-xs bg-gray-100 p-2 rounded overflow-auto">
                {JSON.stringify(result.details, null, 2)}
              </pre>
            </div>
          ))}
        </div>
      )}

      {/* Debug Info */}
      {debugInfo && (
        <div className="bg-white border rounded-lg p-4">
          <h2 className="text-lg font-semibold mb-3">API Response Debug</h2>
          <pre className="text-xs bg-gray-100 p-4 rounded overflow-auto">
            {JSON.stringify(debugInfo, null, 2)}
          </pre>
        </div>
      )}
    </div>
  );
}
