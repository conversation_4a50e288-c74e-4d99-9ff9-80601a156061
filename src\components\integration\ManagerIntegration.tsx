'use client';

import React, { useState, useEffect } from 'react';
import { useOptimizedAuth } from '@/hooks/useOptimizedAuth';
import { supabase } from '@/lib/supabase';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { 
  Briefcase, 
  MessageSquare, 
  DollarSign, 
  Star,
  Eye,
  Settings,
  Calendar,
  TrendingUp,
  RefreshCw,
  Target
} from 'lucide-react';

interface ProjectAssignment {
  id: string;
  project_id: string;
  priority: string;
  status: string;
  assigned_at: string;
  projects: {
    title: string;
    status: string;
    budget: number;
    client: {
      full_name: string;
    };
    designer: {
      full_name: string;
    } | null;
  };
}

interface ManagerActivity {
  id: string;
  activity_type: string;
  description: string;
  outcome: string | null;
  created_at: string;
  projects: {
    title: string;
  };
}

interface ManagerIntegrationProps {
  role: 'client' | 'designer' | 'admin';
  projectId?: string;
  userId?: string;
  compact?: boolean;
}

export default function ManagerIntegration({ role, projectId, userId, compact = false }: ManagerIntegrationProps) {
  const { user } = useOptimizedAuth();
  const [assignments, setAssignments] = useState<ProjectAssignment[]>([]);
  const [activities, setActivities] = useState<ManagerActivity[]>([]);
  const [loading, setLoading] = useState(true);
  const [stats, setStats] = useState({
    active_projects: 0,
    pending_negotiations: 0,
    recent_activities: 0
  });

  useEffect(() => {
    if (user) {
      fetchManagerData();
    }
  }, [user, projectId, userId]);

  const fetchManagerData = async () => {
    try {
      // Fetch project assignments
      let assignmentQuery = supabase
        .from('project_assignments')
        .select(`
          id,
          project_id,
          priority,
          status,
          assigned_at,
          projects:project_id (
            title,
            status,
            budget,
            client:client_id (
              full_name
            ),
            designer:designer_id (
              full_name
            )
          )
        `);

      // Filter based on context
      if (projectId) {
        assignmentQuery = assignmentQuery.eq('project_id', projectId);
      }

      const { data: assignmentData, error: assignmentError } = await assignmentQuery
        .eq('status', 'active')
        .order('assigned_at', { ascending: false })
        .limit(5);

      if (assignmentError) throw assignmentError;

      // Fetch recent manager activities
      let activityQuery = supabase
        .from('manager_activities')
        .select(`
          id,
          activity_type,
          description,
          outcome,
          created_at,
          projects:project_id (
            title
          )
        `);

      if (projectId) {
        activityQuery = activityQuery.eq('project_id', projectId);
      }

      const { data: activityData, error: activityError } = await activityQuery
        .order('created_at', { ascending: false })
        .limit(5);

      if (activityError) throw activityError;

      setAssignments(assignmentData || []);
      setActivities(activityData || []);

      // Calculate stats
      const active_projects = assignmentData?.length || 0;
      const recent_activities = activityData?.length || 0;

      // Get pending negotiations count
      const { count: negotiationCount } = await supabase
        .from('negotiation_sessions')
        .select('*', { count: 'exact', head: true })
        .eq('status', 'active');

      setStats({
        active_projects,
        pending_negotiations: negotiationCount || 0,
        recent_activities
      });
    } catch (error) {
      console.error('Error fetching manager data:', error);
    } finally {
      setLoading(false);
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'urgent':
        return 'bg-red-100 text-red-800 border-red-200';
      case 'high':
        return 'bg-orange-100 text-orange-800 border-orange-200';
      case 'normal':
        return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'low':
        return 'bg-gray-100 text-gray-800 border-gray-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getActivityIcon = (activityType: string) => {
    switch (activityType) {
      case 'negotiation':
        return <MessageSquare className="h-4 w-4 text-blue-500" />;
      case 'milestone_approval':
        return <Target className="h-4 w-4 text-green-500" />;
      case 'issue_escalation':
        return <TrendingUp className="h-4 w-4 text-red-500" />;
      case 'client_communication':
        return <MessageSquare className="h-4 w-4 text-purple-500" />;
      default:
        return <Settings className="h-4 w-4 text-gray-500" />;
    }
  };

  if (loading) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="flex items-center justify-center">
            <RefreshCw className="h-6 w-6 animate-spin text-brown-600" />
          </div>
        </CardContent>
      </Card>
    );
  }

  if (compact) {
    return (
      <div className="space-y-3">
        {/* Compact Stats */}
        <div className="grid grid-cols-2 gap-2">
          <div className="bg-blue-50 p-3 rounded-lg border border-blue-200">
            <div className="flex items-center gap-2">
              <Briefcase className="h-4 w-4 text-blue-500" />
              <div>
                <p className="text-xs text-blue-700 font-medium">Projects</p>
                <p className="text-lg font-bold text-blue-600">{stats.active_projects}</p>
              </div>
            </div>
          </div>
          <div className="bg-orange-50 p-3 rounded-lg border border-orange-200">
            <div className="flex items-center gap-2">
              <MessageSquare className="h-4 w-4 text-orange-500" />
              <div>
                <p className="text-xs text-orange-700 font-medium">Negotiations</p>
                <p className="text-lg font-bold text-orange-600">{stats.pending_negotiations}</p>
              </div>
            </div>
          </div>
        </div>

        {/* Quick Actions - Compact */}
        <div className="space-y-2">
          <h4 className="text-sm font-medium text-gray-700">Quick Actions</h4>
          <div className="grid grid-cols-1 gap-2">
            <Button
              variant="outline"
              size="sm"
              className="flex items-center gap-2 justify-start text-xs"
              onClick={() => window.location.href = '/manager/negotiations'}
            >
              <MessageSquare className="h-3 w-3" />
              Negotiations
            </Button>
            <Button
              variant="outline"
              size="sm"
              className="flex items-center gap-2 justify-start text-xs"
              onClick={() => window.location.href = '/manager/escrow'}
            >
              <DollarSign className="h-3 w-3" />
              Escrow
            </Button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Manager Stats */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Active Projects</p>
                <p className="text-2xl font-bold text-blue-600">{stats.active_projects}</p>
              </div>
              <Briefcase className="h-6 w-6 text-blue-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Negotiations</p>
                <p className="text-2xl font-bold text-orange-600">{stats.pending_negotiations}</p>
              </div>
              <MessageSquare className="h-6 w-6 text-orange-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Activities</p>
                <p className="text-2xl font-bold text-green-600">{stats.recent_activities}</p>
              </div>
              <TrendingUp className="h-6 w-6 text-green-500" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Project Assignments */}
      {assignments.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Briefcase className="h-5 w-5 text-brown-600" />
              Manager Oversight
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {assignments.map((assignment) => (
                <div
                  key={assignment.id}
                  className="flex items-center justify-between p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors"
                >
                  <div className="flex-1">
                    <div className="flex items-center gap-3 mb-2">
                      <h4 className="font-semibold text-gray-900">
                        {assignment.projects?.title || 'Untitled Project'}
                      </h4>
                      <Badge className={`${getPriorityColor(assignment.priority)} border`}>
                        {assignment.priority.toUpperCase()}
                      </Badge>
                    </div>
                    
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm text-gray-600">
                      <div>
                        <span className="font-medium">Client:</span> {assignment.projects?.client?.full_name}
                      </div>
                      <div>
                        <span className="font-medium">Designer:</span> {assignment.projects?.designer?.full_name || 'Unassigned'}
                      </div>
                      <div>
                        <span className="font-medium">Budget:</span> ${assignment.projects?.budget?.toLocaleString()}
                      </div>
                    </div>

                    <div className="flex items-center gap-2 mt-2 text-sm text-gray-500">
                      <Calendar className="h-4 w-4" />
                      <span>Assigned: {new Date(assignment.assigned_at).toLocaleDateString()}</span>
                    </div>
                  </div>

                  <div className="flex items-center gap-2 ml-4">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => window.location.href = `/manager/projects/${assignment.project_id}`}
                      className="flex items-center gap-2"
                    >
                      <Eye className="h-4 w-4" />
                      Manage
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Recent Manager Activities */}
      {activities.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <TrendingUp className="h-5 w-5 text-brown-600" />
              Recent Activities
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {activities.map((activity) => (
                <div
                  key={activity.id}
                  className="flex items-start gap-3 p-4 border border-gray-200 rounded-lg"
                >
                  {getActivityIcon(activity.activity_type)}
                  <div className="flex-1">
                    <div className="flex items-center gap-2 mb-1">
                      <h4 className="font-semibold text-gray-900 capitalize">
                        {activity.activity_type.replace('_', ' ')}
                      </h4>
                      <span className="text-sm text-gray-500">
                        {activity.projects?.title}
                      </span>
                    </div>
                    
                    <p className="text-sm text-gray-700 mb-2">
                      {activity.description}
                    </p>

                    {activity.outcome && (
                      <p className="text-sm text-green-700 bg-green-50 p-2 rounded">
                        <strong>Outcome:</strong> {activity.outcome}
                      </p>
                    )}

                    <div className="flex items-center gap-2 mt-2 text-xs text-gray-500">
                      <Calendar className="h-3 w-3" />
                      <span>{new Date(activity.created_at).toLocaleString()}</span>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Quick Actions */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Settings className="h-5 w-5 text-brown-600" />
            Manager Actions
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Button
              variant="outline"
              className="flex items-center gap-2 justify-center p-4"
              onClick={() => window.location.href = '/manager/negotiations'}
            >
              <MessageSquare className="h-5 w-5" />
              Start Negotiation
            </Button>
            <Button
              variant="outline"
              className="flex items-center gap-2 justify-center p-4"
              onClick={() => window.location.href = '/manager/escrow'}
            >
              <DollarSign className="h-5 w-5" />
              Manage Escrow
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
