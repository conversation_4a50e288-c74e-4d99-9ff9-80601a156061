import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';
import { cookies } from 'next/headers';

export async function GET(request: NextRequest) {
  try {
    console.log('Test auth endpoint called');
    
    const supabase = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          get(name: string) {
            return cookies().get(name)?.value;
          },
        },
      }
    );
    
    // Get current user
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    
    console.log('Auth result:', { user: user?.id, authError });
    
    if (authError || !user) {
      return NextResponse.json({
        success: false,
        error: 'Authentication failed',
        details: {
          authError: authError?.message,
          hasUser: !!user,
          userId: user?.id
        }
      }, { status: 401 });
    }

    // Get user profile
    const { data: profile, error: profileError } = await supabase
      .from('profiles')
      .select('*')
      .eq('id', user.id)
      .single();

    console.log('Profile result:', { profile, profileError });

    return NextResponse.json({
      success: true,
      message: 'Authentication test successful',
      details: {
        user: {
          id: user.id,
          email: user.email,
          created_at: user.created_at,
          last_sign_in_at: user.last_sign_in_at
        },
        profile: profile || null,
        profileError: profileError?.message || null,
        isAdmin: profile?.role === 'admin',
        timestamp: new Date().toISOString()
      }
    });

  } catch (error) {
    console.error('Test auth endpoint error:', error);
    return NextResponse.json({
      success: false,
      error: 'Internal server error',
      details: {
        error: error instanceof Error ? error.message : 'Unknown error'
      }
    }, { status: 500 });
  }
}
