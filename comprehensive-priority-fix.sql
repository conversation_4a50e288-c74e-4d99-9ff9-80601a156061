-- Comprehensive fix for quality_reviews_new priority constraint issue
-- Based on the actual table structure provided

-- Step 1: Check current check constraints on priority column
SELECT 
    conname as constraint_name,
    pg_get_constraintdef(oid) as constraint_definition
FROM pg_constraint 
WHERE conrelid = 'quality_reviews_new'::regclass 
    AND contype = 'c' 
    AND pg_get_constraintdef(oid) LIKE '%priority%';

-- Step 2: Drop the existing priority check constraint if it exists
DO $$
BEGIN
    -- Drop various possible constraint names
    ALTER TABLE quality_reviews_new DROP CONSTRAINT IF EXISTS quality_reviews_new_priority_check;
    ALTER TABLE quality_reviews_new DROP CONSTRAINT IF EXISTS quality_reviews_priority_check;
    ALTER TABLE quality_reviews_new DROP CONSTRAINT IF EXISTS priority_check;
    ALTER TABLE quality_reviews_new DROP CONSTRAINT IF EXISTS check_priority;
    
    RAISE NOTICE 'Dropped existing priority constraints';
EXCEPTION
    WHEN OTHERS THEN
        RAISE NOTICE 'No existing priority constraints found or error dropping them';
END $$;

-- Step 3: Create a new constraint that allows the correct values
ALTER TABLE quality_reviews_new 
ADD CONSTRAINT quality_reviews_new_priority_check 
CHECK (priority IN ('low', 'normal', 'high', 'urgent'));

-- Step 4: Update any existing records with invalid priority
UPDATE quality_reviews_new 
SET priority = 'normal' 
WHERE priority NOT IN ('low', 'normal', 'high', 'urgent') 
   OR priority IS NULL;

-- Step 5: Drop any triggers that might be creating records with 'medium' priority
DROP TRIGGER IF EXISTS trigger_create_quality_review ON project_submissions;
DROP TRIGGER IF EXISTS trigger_create_quality_assignment ON project_submissions;
DROP TRIGGER IF EXISTS trigger_create_quality_assignment_new ON project_submissions;
DROP TRIGGER IF EXISTS trigger_quality_review_auto_create ON project_submissions;

-- Step 6: Create a corrected trigger function (if needed)
CREATE OR REPLACE FUNCTION create_quality_review_trigger_fixed()
RETURNS TRIGGER AS $$
BEGIN
    -- Only create quality review for submitted status
    IF NEW.status = 'submitted' AND (OLD.status IS NULL OR OLD.status != 'submitted') THEN
        INSERT INTO quality_reviews_new (
            project_id,
            submission_id,
            milestone_id,
            designer_id,
            review_type,
            status,
            priority,
            created_at
        ) VALUES (
            NEW.project_id,
            NEW.id,
            NEW.milestone_id,
            NEW.designer_id,
            'submission',
            'pending',
            'normal',  -- Use 'normal' instead of 'medium'
            NOW()
        );
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Step 7: Verify the fix
SELECT 'Current priority values in quality_reviews_new:' as info;
SELECT priority, COUNT(*) as count
FROM quality_reviews_new 
GROUP BY priority
ORDER BY priority;

-- Step 8: Check for any remaining invalid priorities
SELECT 'Invalid priority records:' as info;
SELECT id, priority, created_at
FROM quality_reviews_new 
WHERE priority NOT IN ('low', 'normal', 'high', 'urgent') 
   OR priority IS NULL;

-- Step 9: Test the constraint by trying to insert an invalid priority (should fail)
DO $$
BEGIN
    INSERT INTO quality_reviews_new (priority) VALUES ('medium');
    RAISE NOTICE 'ERROR: Invalid priority was accepted - constraint not working!';
EXCEPTION
    WHEN check_violation THEN
        RAISE NOTICE 'SUCCESS: Invalid priority correctly rejected by constraint';
    WHEN OTHERS THEN
        RAISE NOTICE 'UNEXPECTED ERROR: %', SQLERRM;
END $$;

SELECT 'Comprehensive priority fix completed!' as status;
