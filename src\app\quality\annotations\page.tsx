'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import FileAnnotationTool from '@/components/quality/FileAnnotationTool';
import { useOptimizedAuth } from '@/hooks/useOptimizedAuth';
import { supabase } from '@/lib/supabase';
import { 
  FileImage, 
  AlertTriangle, 
  Upload,
  RefreshCw,
  Eye
} from 'lucide-react';

interface AnnotationFile {
  id: string;
  file_url: string;
  file_name: string;
  file_type: string;
  project_title: string;
  review_id: string;
  annotations_count: number;
  created_at: string;
}

export default function FileAnnotationsPage() {
  const { user, profile, loading } = useOptimizedAuth();
  const [files, setFiles] = useState<AnnotationFile[]>([]);
  const [selectedFile, setSelectedFile] = useState<AnnotationFile | null>(null);
  const [loadingFiles, setLoadingFiles] = useState(true);

  // Helper function to get file type from URL
  const getFileTypeFromUrl = (url: string): string => {
    const extension = url.split('.').pop()?.toLowerCase() || '';
    const imageTypes = ['jpg', 'jpeg', 'png', 'gif', 'webp'];
    const documentTypes = ['pdf', 'doc', 'docx'];
    const cadTypes = ['dwg', 'dxf'];

    if (imageTypes.includes(extension)) return `image/${extension}`;
    if (documentTypes.includes(extension)) return `application/${extension}`;
    if (cadTypes.includes(extension)) return `application/${extension}`;
    return 'application/octet-stream';
  };

  useEffect(() => {
    if (user && ['quality_team', 'admin'].includes(profile?.role)) {
      fetchAnnotationFiles();
    }
  }, [user, profile]);

  const fetchAnnotationFiles = async () => {
    setLoadingFiles(true);
    try {
      // Fetch files from multiple sources that need quality review
      const annotationFiles: AnnotationFile[] = [];

      // 1. Fetch from project_submissions table (main project submissions)
      const { data: submissions, error: submissionsError } = await supabase
        .from('project_submissions')
        .select(`
          id,
          description,
          project_id,
          files,
          submitted_at,
          status,
          projects!inner(
            title,
            client_id,
            profiles!client_id(full_name)
          ),
          quality_reviews_new(
            id,
            status
          )
        `)
        .eq('status', 'submitted')
        .order('submitted_at', { ascending: false });

      if (submissionsError) throw submissionsError;

      // Process submissions
      submissions?.forEach(submission => {
        // Process files from JSONB files column
        if (submission.files && Array.isArray(submission.files)) {
          submission.files.forEach((fileUrl: string, index: number) => {
            const fileName = `File ${index + 1}`;
            const fileExtension = fileUrl.split('.').pop()?.toLowerCase() || '';
            const isAnnotatable = ['jpg', 'jpeg', 'png', 'gif', 'pdf', 'dwg', 'dxf'].includes(fileExtension);

            if (isAnnotatable) {
              annotationFiles.push({
                id: `submission-${submission.id}-${index}`,
                file_url: fileUrl, // Already a full URL from R2
                file_name: fileName,
                file_type: getFileTypeFromUrl(fileUrl),
                project_title: submission.projects?.title || 'Unknown Project',
                review_id: submission.quality_reviews_new?.[0]?.id || '',
                annotations_count: 0, // TODO: Count from annotations table
                created_at: submission.submitted_at
              });
            }
          });
        }
      });

      // 2. Fetch from designer_work_submissions (vision/sample work)
      const { data: workSubmissions, error: workError } = await supabase
        .from('designer_work_submissions')
        .select(`
          id,
          file_name,
          file_path,
          file_type,
          created_at,
          tracking_requests!inner(
            name,
            request_type
          )
        `)
        .eq('status', 'completed')
        .order('created_at', { ascending: false });

      if (workError) throw workError;

      // Process work submissions
      workSubmissions?.forEach(work => {
        const fileExtension = work.file_name.split('.').pop()?.toLowerCase() || '';
        const isAnnotatable = ['jpg', 'jpeg', 'png', 'gif', 'pdf', 'dwg', 'dxf'].includes(fileExtension);

        if (isAnnotatable) {
          // Construct R2 URL (work submissions use direct R2 paths)
          const fileUrl = `${process.env.NEXT_PUBLIC_R2_PUBLIC_URL}/${work.file_path}`;

          annotationFiles.push({
            id: `work-${work.id}`,
            file_url: fileUrl,
            file_name: work.file_name,
            file_type: work.file_type,
            project_title: `${work.tracking_requests?.request_type}: ${work.tracking_requests?.name}`,
            review_id: '', // Work submissions don't have quality reviews yet
            annotations_count: 0,
            created_at: work.created_at
          });
        }
      });

      // Sort by creation date
      annotationFiles.sort((a, b) =>
        new Date(b.created_at).getTime() - new Date(a.created_at).getTime()
      );

      setFiles(annotationFiles);
    } catch (error) {
      console.error('Error fetching annotation files:', error);
      setFiles([]);
    } finally {
      setLoadingFiles(false);
    }
  };

  const handleSaveAnnotations = async (annotations: any[]) => {
    try {
      // Save annotations to database
      console.log('Saving annotations:', annotations);
      // This would call the API to save annotations
    } catch (error) {
      console.error('Error saving annotations:', error);
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (!user || !['quality_team', 'admin'].includes(profile?.role)) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <Card className="w-96">
          <CardContent className="p-6 text-center">
            <AlertTriangle className="h-12 w-12 text-red-500 mx-auto mb-4" />
            <h2 className="text-xl font-semibold mb-2">Access Denied</h2>
            <p className="text-gray-600">You don't have permission to access this page.</p>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="mb-8">
        <div className="flex items-center gap-3 mb-2">
          <FileImage className="h-8 w-8 text-purple-500" />
          <h1 className="text-3xl font-bold">File Annotations</h1>
        </div>
        <p className="text-gray-600">
          Provide visual feedback on design files with annotations and comments
        </p>
      </div>

      <div className="grid grid-cols-1 xl:grid-cols-4 gap-6">
        {/* Files List */}
        <Card className="xl:col-span-1">
          <CardHeader className="pb-3">
            <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-2">
              <CardTitle className="flex items-center gap-2 text-sm sm:text-base">
                <Upload className="h-4 w-4 sm:h-5 sm:w-5" />
                <span className="truncate">Files to Review</span>
              </CardTitle>
              <Button
                variant="outline"
                size="sm"
                onClick={fetchAnnotationFiles}
                disabled={loadingFiles}
                className="self-start sm:self-auto"
              >
                <RefreshCw className={`h-4 w-4 ${loadingFiles ? 'animate-spin' : ''}`} />
                <span className="ml-1 hidden sm:inline">Refresh</span>
              </Button>
            </div>
          </CardHeader>
          <CardContent className="p-0">
            {loadingFiles ? (
              <div className="flex items-center justify-center py-8">
                <RefreshCw className="h-6 w-6 text-gray-400 animate-spin" />
              </div>
            ) : files.length === 0 ? (
              <div className="text-center py-8 text-gray-500">
                <FileImage className="h-12 w-12 mx-auto mb-4" />
                <p>No files to annotate</p>
                <p className="text-sm mt-2">Files will appear when designers submit work for review</p>
              </div>
            ) : (
              <div className="space-y-1">
                {files.map((file) => (
                  <button
                    key={file.id}
                    onClick={() => setSelectedFile(file)}
                    className={`w-full text-left p-3 sm:p-4 hover:bg-gray-50 border-b transition-colors ${
                      selectedFile?.id === file.id ? 'bg-purple-50 border-purple-200' : ''
                    }`}
                  >
                    <div className="flex items-start gap-2 sm:gap-3">
                      <FileImage className="h-4 w-4 sm:h-5 sm:w-5 text-purple-500 mt-0.5 flex-shrink-0" />
                      <div className="flex-1 min-w-0">
                        <h3 className="font-medium text-xs sm:text-sm truncate" title={file.file_name}>
                          {file.file_name}
                        </h3>
                        <p className="text-xs sm:text-sm text-gray-600 truncate" title={file.project_title}>
                          {file.project_title}
                        </p>
                        <div className="flex items-center gap-1 sm:gap-2 mt-1">
                          <Eye className="h-3 w-3 text-gray-400 flex-shrink-0" />
                          <span className="text-xs text-gray-500">
                            {file.annotations_count} annotations
                          </span>
                        </div>
                      </div>
                    </div>
                  </button>
                ))}
              </div>
            )}
          </CardContent>
        </Card>

        {/* Annotation Tool */}
        <div className="xl:col-span-3">
          {selectedFile ? (
            <div className="h-full">
              <FileAnnotationTool
                fileUrl={selectedFile.file_url}
                fileName={selectedFile.file_name}
                fileType={selectedFile.file_type}
                existingAnnotations={[]} // Loaded automatically by the component
                onSaveAnnotations={handleSaveAnnotations}
                readOnly={false}
                reviewerName={profile?.full_name || 'Quality Reviewer'}
                qualityReviewId={selectedFile.review_id}
                submissionId={selectedFile.id.split('-')[1]} // Extract submission ID from composite ID
                submissionType={selectedFile.id.startsWith('work-') ? 'work' : 'project'}
              />
            </div>
          ) : (
            <Card className="h-full min-h-[400px] sm:min-h-[500px]">
              <CardContent className="flex items-center justify-center h-full p-6">
                <div className="text-center text-gray-500 max-w-sm">
                  <FileImage className="h-12 w-12 sm:h-16 sm:w-16 mx-auto mb-4" />
                  <h3 className="text-base sm:text-lg font-medium mb-2">Select a File</h3>
                  <p className="text-sm sm:text-base">Choose a file from the list to start adding annotations</p>
                </div>
              </CardContent>
            </Card>
          )}
        </div>
      </div>
    </div>
  );
}
