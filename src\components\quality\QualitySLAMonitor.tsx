'use client';

import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { 
  Clock, 
  AlertTriangle, 
  CheckCircle, 
  XCircle,
  TrendingUp,
  TrendingDown,
  Users,
  BarChart,
  RefreshCw,
  ArrowUp,
  ArrowDown,
  Calendar,
  Timer,
  Target
} from 'lucide-react';
import { supabase } from '@/lib/supabase';

interface SLATracking {
  id: string;
  review_id: string;
  original_deadline: string;
  current_deadline: string;
  priority: string;
  status: 'on_track' | 'at_risk' | 'overdue' | 'completed' | 'escalated';
  assigned_to: string;
  escalation_level: number;
  time_to_completion_minutes: number | null;
  project: {
    title: string;
    client_name: string;
  };
  assignee: {
    full_name: string;
    avatar_url: string;
  };
}

interface TeamWorkload {
  team_member_id: string;
  current_assignments: number;
  max_capacity: number;
  availability_status: string;
  average_review_time_minutes: number;
  full_name: string;
  avatar_url: string;
}

interface QualitySLAMonitorProps {
  teamMemberId?: string; // If provided, shows only this member's SLAs
  showTeamOverview?: boolean;
}

export default function QualitySLAMonitor({
  teamMemberId,
  showTeamOverview = true
}: QualitySLAMonitorProps) {
  const [slaTrackings, setSlaTrackings] = useState<SLATracking[]>([]);
  const [teamWorkloads, setTeamWorkloads] = useState<TeamWorkload[]>([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [selectedPriority, setSelectedPriority] = useState<string>('all');
  const [selectedStatus, setSelectedStatus] = useState<string>('all');

  useEffect(() => {
    fetchSLAData();
    
    // Set up real-time subscription
    const channel = supabase
      .channel('quality-sla-monitor')
      .on('postgres_changes', {
        event: '*',
        schema: 'public',
        table: 'quality_sla_tracking'
      }, () => {
        fetchSLAData();
      })
      .subscribe();

    return () => {
      supabase.removeChannel(channel);
    };
  }, [teamMemberId]);

  const fetchSLAData = async () => {
    setLoading(true);
    try {
      // Fetch SLA tracking data
      let slaQuery = supabase
        .from('quality_sla_tracking')
        .select(`
          *,
          quality_reviews_new!inner(
            project_id,
            projects!inner(title, client_id, profiles!client_id(full_name))
          ),
          profiles!assigned_to(full_name, avatar_url)
        `)
        .order('current_deadline', { ascending: true });

      if (teamMemberId) {
        slaQuery = slaQuery.eq('assigned_to', teamMemberId);
      }

      const { data: slaData, error: slaError } = await slaQuery;
      if (slaError) throw slaError;

      // Transform the data
      const transformedSLA = slaData.map(item => ({
        id: item.id,
        review_id: item.review_id,
        original_deadline: item.original_deadline,
        current_deadline: item.current_deadline,
        priority: item.priority,
        status: item.status,
        assigned_to: item.assigned_to,
        escalation_level: item.escalation_level || 0,
        time_to_completion_minutes: item.time_to_completion_minutes,
        project: {
          title: item.quality_reviews_new?.projects?.title || 'Unknown Project',
          client_name: item.quality_reviews_new?.projects?.profiles?.full_name || 'Unknown Client'
        },
        assignee: {
          full_name: item.profiles?.full_name || 'Unassigned',
          avatar_url: item.profiles?.avatar_url
        }
      }));

      setSlaTrackings(transformedSLA);

      // Fetch team workload data if showing team overview
      if (showTeamOverview) {
        const { data: workloadData, error: workloadError } = await supabase
          .from('quality_team_workload')
          .select(`
            *,
            profiles!team_member_id(full_name, avatar_url)
          `)
          .eq('is_active', true);

        if (workloadError) throw workloadError;

        const transformedWorkload = workloadData.map(item => ({
          team_member_id: item.team_member_id,
          current_assignments: item.current_assignments,
          max_capacity: item.max_capacity,
          availability_status: item.availability_status,
          average_review_time_minutes: item.average_review_time_minutes || 0,
          full_name: item.profiles?.full_name || 'Unknown',
          avatar_url: item.profiles?.avatar_url
        }));

        setTeamWorkloads(transformedWorkload);
      }
    } catch (error) {
      console.error('Error fetching SLA data:', error);
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  const handleRefresh = () => {
    setRefreshing(true);
    fetchSLAData();
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'on_track': return 'text-green-600 bg-green-50 border-green-200';
      case 'at_risk': return 'text-yellow-600 bg-yellow-50 border-yellow-200';
      case 'overdue': return 'text-red-600 bg-red-50 border-red-200';
      case 'completed': return 'text-blue-600 bg-blue-50 border-blue-200';
      case 'escalated': return 'text-purple-600 bg-purple-50 border-purple-200';
      default: return 'text-gray-600 bg-gray-50 border-gray-200';
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'low': return 'text-gray-600 bg-gray-50 border-gray-200';
      case 'medium': return 'text-blue-600 bg-blue-50 border-blue-200';
      case 'high': return 'text-orange-600 bg-orange-50 border-orange-200';
      case 'urgent': return 'text-red-600 bg-red-50 border-red-200';
      case 'emergency': return 'text-red-800 bg-red-100 border-red-300';
      default: return 'text-gray-600 bg-gray-50 border-gray-200';
    }
  };

  const getTimeRemaining = (deadline: string) => {
    const now = new Date();
    const deadlineDate = new Date(deadline);
    const diffMs = deadlineDate.getTime() - now.getTime();
    const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
    const diffMinutes = Math.floor((diffMs % (1000 * 60 * 60)) / (1000 * 60));

    if (diffMs < 0) {
      const overdue = Math.abs(diffHours);
      return { text: `${overdue}h overdue`, isOverdue: true };
    } else if (diffHours < 1) {
      return { text: `${diffMinutes}m remaining`, isOverdue: false };
    } else if (diffHours < 24) {
      return { text: `${diffHours}h ${diffMinutes}m remaining`, isOverdue: false };
    } else {
      const days = Math.floor(diffHours / 24);
      const hours = diffHours % 24;
      return { text: `${days}d ${hours}h remaining`, isOverdue: false };
    }
  };

  const getWorkloadPercentage = (current: number, max: number) => {
    return Math.round((current / max) * 100);
  };

  const getWorkloadColor = (percentage: number) => {
    if (percentage >= 90) return 'bg-red-500';
    if (percentage >= 75) return 'bg-orange-500';
    if (percentage >= 50) return 'bg-yellow-500';
    return 'bg-green-500';
  };

  const filteredSLAs = slaTrackings.filter(sla => {
    const matchesPriority = selectedPriority === 'all' || sla.priority === selectedPriority;
    const matchesStatus = selectedStatus === 'all' || sla.status === selectedStatus;
    return matchesPriority && matchesStatus;
  });

  const statusCounts = {
    on_track: slaTrackings.filter(s => s.status === 'on_track').length,
    at_risk: slaTrackings.filter(s => s.status === 'at_risk').length,
    overdue: slaTrackings.filter(s => s.status === 'overdue').length,
    escalated: slaTrackings.filter(s => s.status === 'escalated').length,
    completed: slaTrackings.filter(s => s.status === 'completed').length
  };

  return (
    <div className="space-y-6">
      {/* SLA Overview Cards */}
      <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
        <Card className="border-green-200">
          <CardContent className="p-4 text-center">
            <div className="flex items-center justify-center mb-2">
              <CheckCircle className="h-8 w-8 text-green-500" />
            </div>
            <div className="text-2xl font-bold text-green-600">{statusCounts.on_track}</div>
            <div className="text-sm text-gray-600">On Track</div>
          </CardContent>
        </Card>

        <Card className="border-yellow-200">
          <CardContent className="p-4 text-center">
            <div className="flex items-center justify-center mb-2">
              <Clock className="h-8 w-8 text-yellow-500" />
            </div>
            <div className="text-2xl font-bold text-yellow-600">{statusCounts.at_risk}</div>
            <div className="text-sm text-gray-600">At Risk</div>
          </CardContent>
        </Card>

        <Card className="border-red-200">
          <CardContent className="p-4 text-center">
            <div className="flex items-center justify-center mb-2">
              <AlertTriangle className="h-8 w-8 text-red-500" />
            </div>
            <div className="text-2xl font-bold text-red-600">{statusCounts.overdue}</div>
            <div className="text-sm text-gray-600">Overdue</div>
          </CardContent>
        </Card>

        <Card className="border-purple-200">
          <CardContent className="p-4 text-center">
            <div className="flex items-center justify-center mb-2">
              <ArrowUp className="h-8 w-8 text-purple-500" />
            </div>
            <div className="text-2xl font-bold text-purple-600">{statusCounts.escalated}</div>
            <div className="text-sm text-gray-600">Escalated</div>
          </CardContent>
        </Card>

        <Card className="border-blue-200">
          <CardContent className="p-4 text-center">
            <div className="flex items-center justify-center mb-2">
              <Target className="h-8 w-8 text-blue-500" />
            </div>
            <div className="text-2xl font-bold text-blue-600">{statusCounts.completed}</div>
            <div className="text-sm text-gray-600">Completed</div>
          </CardContent>
        </Card>
      </div>

      {/* Team Workload Overview */}
      {showTeamOverview && teamWorkloads.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Users className="h-5 w-5 text-blue-500" />
              Team Workload Overview
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {teamWorkloads.map(member => {
                const workloadPercentage = getWorkloadPercentage(member.current_assignments, member.max_capacity);
                return (
                  <div key={member.team_member_id} className="border rounded-lg p-4">
                    <div className="flex items-center gap-3 mb-3">
                      <Avatar className="h-10 w-10">
                        <AvatarImage src={member.avatar_url} />
                        <AvatarFallback>{member.full_name.charAt(0)}</AvatarFallback>
                      </Avatar>
                      <div>
                        <div className="font-medium">{member.full_name}</div>
                        <Badge variant="outline" className={
                          member.availability_status === 'available' 
                            ? 'text-green-600 border-green-200' 
                            : 'text-gray-600 border-gray-200'
                        }>
                          {member.availability_status}
                        </Badge>
                      </div>
                    </div>
                    
                    <div className="space-y-2">
                      <div className="flex justify-between text-sm">
                        <span>Workload</span>
                        <span>{member.current_assignments}/{member.max_capacity}</span>
                      </div>
                      <Progress 
                        value={workloadPercentage} 
                        className={`h-2 ${getWorkloadColor(workloadPercentage)}`}
                      />
                      
                      <div className="flex justify-between text-sm text-gray-600">
                        <span>Avg. Review Time</span>
                        <span>{Math.round(member.average_review_time_minutes / 60)}h</span>
                      </div>
                    </div>
                  </div>
                );
              })}
            </div>
          </CardContent>
        </Card>
      )}

      {/* SLA Tracking List */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center gap-2">
              <Timer className="h-5 w-5 text-orange-500" />
              SLA Tracking
            </CardTitle>
            <Button
              variant="outline"
              size="sm"
              onClick={handleRefresh}
              disabled={refreshing}
              className="flex items-center gap-2"
            >
              <RefreshCw className={`h-4 w-4 ${refreshing ? 'animate-spin' : ''}`} />
              Refresh
            </Button>
          </div>
          
          {/* Filters */}
          <div className="flex gap-4 mt-4">
            <select
              value={selectedPriority}
              onChange={(e) => setSelectedPriority(e.target.value)}
              className="border border-gray-300 rounded-lg px-3 py-2 text-sm"
            >
              <option value="all">All Priorities</option>
              <option value="low">Low</option>
              <option value="medium">Medium</option>
              <option value="high">High</option>
              <option value="urgent">Urgent</option>
              <option value="emergency">Emergency</option>
            </select>
            
            <select
              value={selectedStatus}
              onChange={(e) => setSelectedStatus(e.target.value)}
              className="border border-gray-300 rounded-lg px-3 py-2 text-sm"
            >
              <option value="all">All Statuses</option>
              <option value="on_track">On Track</option>
              <option value="at_risk">At Risk</option>
              <option value="overdue">Overdue</option>
              <option value="escalated">Escalated</option>
              <option value="completed">Completed</option>
            </select>
          </div>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="flex items-center justify-center py-8">
              <RefreshCw className="h-8 w-8 text-gray-400 animate-spin" />
            </div>
          ) : filteredSLAs.length === 0 ? (
            <div className="text-center py-8 text-gray-500">
              No SLA tracking records found
            </div>
          ) : (
            <div className="space-y-4">
              {filteredSLAs.map(sla => {
                const timeRemaining = getTimeRemaining(sla.current_deadline);
                
                return (
                  <div key={sla.id} className="border rounded-lg p-4">
                    <div className="flex items-start justify-between mb-3">
                      <div>
                        <h3 className="font-semibold">{sla.project.title}</h3>
                        <p className="text-sm text-gray-600">Client: {sla.project.client_name}</p>
                      </div>
                      
                      <div className="flex items-center gap-2">
                        <Badge variant="outline" className={getPriorityColor(sla.priority)}>
                          {sla.priority.toUpperCase()}
                        </Badge>
                        <Badge variant="outline" className={getStatusColor(sla.status)}>
                          {sla.status.replace('_', ' ').toUpperCase()}
                        </Badge>
                        {sla.escalation_level > 0 && (
                          <Badge variant="outline" className="text-purple-600 bg-purple-50 border-purple-200">
                            Escalated L{sla.escalation_level}
                          </Badge>
                        )}
                      </div>
                    </div>
                    
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                      <div className="flex items-center gap-2">
                        <Avatar className="h-8 w-8">
                          <AvatarImage src={sla.assignee.avatar_url} />
                          <AvatarFallback>{sla.assignee.full_name.charAt(0)}</AvatarFallback>
                        </Avatar>
                        <div>
                          <div className="text-sm font-medium">{sla.assignee.full_name}</div>
                          <div className="text-xs text-gray-500">Assigned</div>
                        </div>
                      </div>
                      
                      <div className="flex items-center gap-2">
                        <Calendar className="h-4 w-4 text-gray-400" />
                        <div>
                          <div className="text-sm font-medium">
                            {new Date(sla.current_deadline).toLocaleDateString()}
                          </div>
                          <div className="text-xs text-gray-500">
                            {new Date(sla.current_deadline).toLocaleTimeString()}
                          </div>
                        </div>
                      </div>
                      
                      <div className="flex items-center gap-2">
                        <Timer className={`h-4 w-4 ${timeRemaining.isOverdue ? 'text-red-500' : 'text-gray-400'}`} />
                        <div>
                          <div className={`text-sm font-medium ${timeRemaining.isOverdue ? 'text-red-600' : ''}`}>
                            {timeRemaining.text}
                          </div>
                          <div className="text-xs text-gray-500">
                            {timeRemaining.isOverdue ? 'Overdue' : 'Remaining'}
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                );
              })}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
