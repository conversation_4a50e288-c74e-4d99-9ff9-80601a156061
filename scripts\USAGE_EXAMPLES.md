# Environment Sync Script - Usage Examples

## Quick Start Guide

### 1. First Time Setup

```bash
# Make sure you're logged in to Vercel
npx vercel login

# Link your project (if not already linked)
npx vercel link

# Run the sync script
npm run env:sync
```

### 2. Review Generated Files

After running the script, you'll get two files:

**`production.env`** - Ready for import:
```env
# Production Environment Variables
# Generated by env-sync script
# Generated on: 2024-01-15T10:30:00.000Z

# Variables that match between local and production
NEXT_PUBLIC_SUPABASE_URL=https://ssqemftzvxcrmwgbebnb.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...

# Variables that differ between local and production (using local values)
# Production value: https://yourapp.vercel.app
NEXT_PUBLIC_SITE_URL=http://localhost:3000

# Variables only in local environment
SKIP_RECAPTCHA=true
PAYPAL_SKIP_WEBHOOK_VERIFICATION=true
```

**`env-comparison.json`** - Detailed analysis:
```json
{
  "same": [
    { "key": "NEXT_PUBLIC_SUPABASE_URL", "value": "https://..." }
  ],
  "different": [
    {
      "key": "NEXT_PUBLIC_SITE_URL",
      "local": "http://localhost:3000",
      "production": "https://yourapp.vercel.app"
    }
  ],
  "onlyLocal": [
    { "key": "SKIP_RECAPTCHA", "value": "true" }
  ],
  "onlyProduction": []
}
```

### 3. Import to Vercel

#### Option A: Bulk Import (Recommended)
```bash
# Import all variables at once
npx vercel env add < production.env
```

#### Option B: Individual Variables
```bash
# Add specific variables
npx vercel env add NEXT_PUBLIC_SITE_URL production
npx vercel env add STRIPE_SECRET_KEY production
```

#### Option C: Using Vercel Dashboard
1. Go to your project in Vercel dashboard
2. Settings → Environment Variables
3. Click "Add New" → "Import from file"
4. Upload `production.env`

## Common Scenarios

### Scenario 1: New Project Deployment

```bash
# 1. Set up local environment
cp .env.example .env.local
# Edit .env.local with your values

# 2. Deploy to Vercel
npx vercel --prod

# 3. Sync environment variables
npm run env:sync

# 4. Import to production
npx vercel env add < production.env
```

### Scenario 2: Adding New Environment Variables

```bash
# 1. Add new variables to .env.local
echo "NEW_API_KEY=your_api_key" >> .env.local

# 2. Run sync to identify new variables
npm run env:sync

# 3. Review production.env for new variables
# 4. Import updated variables
npx vercel env add < production.env
```

### Scenario 3: Environment Audit

```bash
# Run sync to compare environments
npm run env:sync

# Check what's different
cat env-comparison.json | jq '.different'

# Check what's missing in production
cat env-comparison.json | jq '.onlyLocal'
```

### Scenario 4: Production vs Development Values

Some variables should be different between environments:

**Local (.env.local):**
```env
NEXT_PUBLIC_SITE_URL=http://localhost:3000
SKIP_RECAPTCHA=true
PAYPAL_SKIP_WEBHOOK_VERIFICATION=true
TAPPAY_ENVIRONMENT=sandbox
CLICKPAY_ENVIRONMENT=sandbox
```

**Production (should be updated):**
```env
NEXT_PUBLIC_SITE_URL=https://yourapp.vercel.app
SKIP_RECAPTCHA=false
PAYPAL_SKIP_WEBHOOK_VERIFICATION=false
TAPPAY_ENVIRONMENT=production
CLICKPAY_ENVIRONMENT=production
```

## Advanced Usage

### Custom Environment Files

```bash
# Use different local file
LOCAL_ENV_FILE=.env.production node scripts/env-sync.js

# Generate different output file
OUTPUT_FILE=staging.env node scripts/env-sync.js
```

### Filtering Variables

Edit the script to exclude certain variables:

```javascript
// In env-sync.js, add filtering logic
const EXCLUDED_VARS = ['SKIP_RECAPTCHA', 'PAYPAL_SKIP_WEBHOOK_VERIFICATION'];

function filterVariables(envVars) {
  const filtered = {};
  for (const [key, value] of Object.entries(envVars)) {
    if (!EXCLUDED_VARS.includes(key)) {
      filtered[key] = value;
    }
  }
  return filtered;
}
```

### Environment-Specific Values

Create a mapping for environment-specific values:

```javascript
const ENVIRONMENT_MAPPINGS = {
  'NEXT_PUBLIC_SITE_URL': {
    local: 'http://localhost:3000',
    production: 'https://yourapp.vercel.app'
  },
  'TAPPAY_ENVIRONMENT': {
    local: 'sandbox',
    production: 'production'
  }
};
```

## Troubleshooting

### Common Issues

1. **"Command not found: vercel"**
   ```bash
   npm i -g vercel
   ```

2. **"Not authenticated"**
   ```bash
   npx vercel login
   ```

3. **"Project not found"**
   ```bash
   npx vercel link
   ```

4. **"Permission denied"**
   - Check team access in Vercel dashboard
   - Ensure you're in the correct team/scope

### Debugging

```bash
# Check Vercel CLI status
npx vercel whoami

# List current environment variables
npx vercel env ls

# Check project link
npx vercel ls
```

## Best Practices

1. **Review before importing** - Always check `production.env` before importing
2. **Update production-specific values** - Change localhost URLs, disable debug flags
3. **Secure sensitive data** - Don't commit generated files to version control
4. **Regular audits** - Run sync periodically to catch drift
5. **Team coordination** - Share the process with your team

## Integration with CI/CD

Add to your deployment workflow:

```yaml
# .github/workflows/deploy.yml
- name: Sync Environment Variables
  run: |
    npm run env:sync
    # Review and conditionally import
    npx vercel env add < production.env
```
