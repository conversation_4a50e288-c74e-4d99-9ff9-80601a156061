'use client';

import React, { useState, useEffect } from 'react';
import { useOptimizedAuth } from '@/hooks/useOptimizedAuth';
import { supabase } from '@/lib/supabase';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { 
  MessageSquare, 
  Bell, 
  CheckCircle, 
  Clock, 
  AlertTriangle,
  Eye,
  Send,
  RefreshCw,
  Users,
  FileText
} from 'lucide-react';

interface Conversation {
  id: string;
  type: string;
  title?: string;
  project_id?: string;
  last_message_at: string;
  created_at: string;
  conversation_participants: {
    user_id: string;
    role: string;
    profiles: {
      id: string;
      full_name: string;
      avatar_url: string | null;
      role: string;
    };
  }[];
  conversation_messages: {
    id: string;
    content: string;
    created_at: string;
    sender_id: string;
  }[];
}

interface WorkflowNotification {
  id: string;
  notification_type: string;
  title: string;
  message: string;
  priority: string;
  read_at: string | null;
  created_at: string;
  action_url: string | null;
  metadata: any;
}

interface UnifiedCommunicationProps {
  projectId?: string;
  role: string;
  compact?: boolean;
}

export default function UnifiedCommunication({ projectId, role, compact = false }: UnifiedCommunicationProps) {
  const { user } = useOptimizedAuth();
  const [conversations, setConversations] = useState<Conversation[]>([]);
  const [notifications, setNotifications] = useState<WorkflowNotification[]>([]);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState<'messages' | 'notifications'>('messages');
  const [stats, setStats] = useState({
    unread_messages: 0,
    unread_notifications: 0,
    active_conversations: 0
  });

  useEffect(() => {
    if (user) {
      fetchCommunicationData();
    }
  }, [user, projectId]);

  const fetchCommunicationData = async () => {
    try {
      // Fetch conversations using the new schema
      const { data: conversationData, error: conversationError } = await supabase
        .from('conversations')
        .select(`
          id,
          type,
          title,
          project_id,
          last_message_at,
          created_at,
          conversation_participants!inner (
            user_id,
            role,
            profiles:user_id (
              id,
              full_name,
              avatar_url,
              role
            )
          ),
          conversation_messages (
            id,
            content,
            created_at,
            sender_id
          )
        `)
        .eq('conversation_participants.user_id', user?.id)
        .eq('is_active', true)
        .order('last_message_at', { ascending: false })
        .limit(compact ? 3 : 10);

      if (conversationError) throw conversationError;

      // Fetch workflow notifications
      const { data: notificationData, error: notificationError } = await supabase
        .from('workflow_notifications')
        .select('*')
        .eq('recipient_id', user?.id)
        .order('created_at', { ascending: false })
        .limit(compact ? 3 : 10);

      if (notificationError) throw notificationError;

      setConversations(conversationData || []);
      setNotifications(notificationData || []);

      // Calculate stats (simplified without is_read column)
      const unread_messages = (conversationData || []).reduce((total, conv) => {
        return total + conv.conversation_messages.filter(msg =>
          msg.sender_id !== user?.id
        ).length;
      }, 0);

      const unread_notifications = (notificationData || []).filter(n => !n.read_at).length;
      const active_conversations = (conversationData || []).length;

      setStats({
        unread_messages,
        unread_notifications,
        active_conversations
      });
    } catch (error) {
      console.error('Error fetching communication data:', error);
    } finally {
      setLoading(false);
    }
  };

  const markNotificationAsRead = async (notificationId: string) => {
    try {
      await supabase
        .from('workflow_notifications')
        .update({ read_at: new Date().toISOString() })
        .eq('id', notificationId);
      
      fetchCommunicationData();
    } catch (error) {
      console.error('Error marking notification as read:', error);
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'urgent':
        return 'bg-red-100 text-red-800 border-red-200';
      case 'high':
        return 'bg-orange-100 text-orange-800 border-orange-200';
      case 'normal':
        return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'low':
        return 'bg-gray-100 text-gray-800 border-gray-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getNotificationIcon = (type: string) => {
    switch (type) {
      case 'quality_review_assigned':
      case 'quality_review_completed':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'milestone_completed':
      case 'project_assigned':
        return <FileText className="h-4 w-4 text-blue-500" />;
      case 'sla_deadline_approaching':
      case 'revision_required':
        return <AlertTriangle className="h-4 w-4 text-amber-500" />;
      default:
        return <Bell className="h-4 w-4 text-gray-500" />;
    }
  };

  const getOtherParticipant = (conversation: Conversation) => {
    // Find the participant who is not the current user
    const otherParticipant = conversation.conversation_participants.find(
      participant => participant.user_id !== user?.id
    );
    return otherParticipant?.profiles || null;
  };

  const getLastMessage = (conversation: Conversation) => {
    return conversation.conversation_messages?.[0];
  };

  if (loading) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="flex items-center justify-center">
            <RefreshCw className="h-6 w-6 animate-spin text-brown-600" />
          </div>
        </CardContent>
      </Card>
    );
  }

  if (compact) {
    return (
      <div className="space-y-3">
        {/* Compact Stats - Vertical Layout */}
        <div className="space-y-2">
          <div className="flex items-center justify-between p-2 bg-blue-50 rounded-lg border border-blue-200">
            <div className="flex items-center gap-2">
              <MessageSquare className="h-4 w-4 text-blue-500" />
              <span className="text-xs font-medium text-blue-700">Messages</span>
            </div>
            <span className="text-sm font-bold text-blue-600">{stats.unread_messages}</span>
          </div>

          <div className="flex items-center justify-between p-2 bg-orange-50 rounded-lg border border-orange-200">
            <div className="flex items-center gap-2">
              <Bell className="h-4 w-4 text-orange-500" />
              <span className="text-xs font-medium text-orange-700">Alerts</span>
            </div>
            <span className="text-sm font-bold text-orange-600">{stats.unread_notifications}</span>
          </div>

          <div className="flex items-center justify-between p-2 bg-green-50 rounded-lg border border-green-200">
            <div className="flex items-center gap-2">
              <Users className="h-4 w-4 text-green-500" />
              <span className="text-xs font-medium text-green-700">Active Chats</span>
            </div>
            <span className="text-sm font-bold text-green-600">{stats.active_conversations}</span>
          </div>
        </div>

        {/* Quick Actions */}
        <div className="grid grid-cols-2 gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => window.location.href = '/messages'}
            className="flex items-center gap-1 text-xs"
          >
            <MessageSquare className="h-3 w-3" />
            Messages
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => window.location.href = '/notifications'}
            className="flex items-center gap-1 text-xs"
          >
            <Bell className="h-3 w-3" />
            Alerts
          </Button>
        </div>
      </div>
    );
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2">
            <MessageSquare className="h-5 w-5 text-brown-600" />
            Communication Center
          </CardTitle>
          <div className="flex items-center gap-2">
            <Button
              variant={activeTab === 'messages' ? 'default' : 'outline'}
              size="sm"
              onClick={() => setActiveTab('messages')}
              className="flex items-center gap-2"
            >
              <MessageSquare className="h-4 w-4" />
              Messages ({stats.unread_messages})
            </Button>
            <Button
              variant={activeTab === 'notifications' ? 'default' : 'outline'}
              size="sm"
              onClick={() => setActiveTab('notifications')}
              className="flex items-center gap-2"
            >
              <Bell className="h-4 w-4" />
              Alerts ({stats.unread_notifications})
            </Button>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        {activeTab === 'messages' ? (
          <div className="space-y-4">
            {conversations.length === 0 ? (
              <div className="text-center py-8">
                <MessageSquare className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <p className="text-gray-500">No conversations found</p>
              </div>
            ) : (
              conversations.map((conversation) => {
                const otherParticipant = getOtherParticipant(conversation);
                const lastMessage = getLastMessage(conversation);
                const unreadCount = conversation.conversation_messages.filter(msg =>
                  msg.sender_id !== user?.id
                ).length;

                return (
                  <div
                    key={conversation.id}
                    className="flex items-center justify-between p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors cursor-pointer"
                    onClick={() => window.location.href = `/messages/${conversation.id}`}
                  >
                    <div className="flex items-center gap-3 flex-1">
                      <div className="w-10 h-10 bg-brown-100 rounded-full flex items-center justify-center">
                        <span className="text-brown-600 font-semibold">
                          {otherParticipant?.full_name?.charAt(0) || '?'}
                        </span>
                      </div>
                      
                      <div className="flex-1">
                        <div className="flex items-center gap-2 mb-1">
                          <h4 className="font-semibold text-gray-900">
                            {otherParticipant?.full_name || 'Unknown User'}
                          </h4>
                          <Badge className="bg-gray-100 text-gray-800 text-xs">
                            {otherParticipant?.role || 'user'}
                          </Badge>
                          {unreadCount > 0 && (
                            <Badge className="bg-red-100 text-red-800 text-xs">
                              {unreadCount} new
                            </Badge>
                          )}
                        </div>
                        
                        {lastMessage && (
                          <p className="text-sm text-gray-600 line-clamp-1">
                            {lastMessage.content}
                          </p>
                        )}
                        
                        <p className="text-xs text-gray-500 mt-1">
                          {new Date(conversation.last_message_at).toLocaleDateString()}
                        </p>
                      </div>
                    </div>

                    <Button
                      variant="outline"
                      size="sm"
                      className="flex items-center gap-2"
                    >
                      <Send className="h-4 w-4" />
                      Open
                    </Button>
                  </div>
                );
              })
            )}
          </div>
        ) : (
          <div className="space-y-4">
            {notifications.length === 0 ? (
              <div className="text-center py-8">
                <Bell className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <p className="text-gray-500">No notifications found</p>
              </div>
            ) : (
              notifications.map((notification) => (
                <div
                  key={notification.id}
                  className={`p-4 border rounded-lg transition-colors ${
                    notification.read_at ? 'border-gray-200 bg-gray-50' : 'border-blue-200 bg-blue-50'
                  }`}
                >
                  <div className="flex items-start justify-between gap-3">
                    <div className="flex items-start gap-3 flex-1">
                      {getNotificationIcon(notification.notification_type)}
                      
                      <div className="flex-1">
                        <div className="flex items-center gap-2 mb-1">
                          <h4 className="font-semibold text-gray-900">
                            {notification.title}
                          </h4>
                          <Badge className={`${getPriorityColor(notification.priority)} border text-xs`}>
                            {notification.priority.toUpperCase()}
                          </Badge>
                          {!notification.read_at && (
                            <Badge className="bg-blue-100 text-blue-800 text-xs">
                              NEW
                            </Badge>
                          )}
                        </div>
                        
                        <p className="text-sm text-gray-700 mb-2">
                          {notification.message}
                        </p>
                        
                        <p className="text-xs text-gray-500">
                          {new Date(notification.created_at).toLocaleString()}
                        </p>
                      </div>
                    </div>

                    <div className="flex items-center gap-2">
                      {notification.action_url && (
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => window.location.href = notification.action_url!}
                          className="flex items-center gap-2"
                        >
                          <Eye className="h-4 w-4" />
                          View
                        </Button>
                      )}
                      
                      {!notification.read_at && (
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => markNotificationAsRead(notification.id)}
                          className="flex items-center gap-2"
                        >
                          <CheckCircle className="h-4 w-4" />
                          Mark Read
                        </Button>
                      )}
                    </div>
                  </div>
                </div>
              ))
            )}
          </div>
        )}
      </CardContent>
    </Card>
  );
}
