"use client";

import React, { useState } from "react";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { <PERSON>, CardContent, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import {
  Eye,
  CheckCircle,
  XCircle,
  Clock,
  AlertTriangle,
  FileText,
  Star,
  MessageSquare,
  ArrowRight,
  ExternalLink
} from "lucide-react";
import Link from "next/link";

interface SubmissionReviewIntegrationProps {
  submission: {
    id: string;
    title: string;
    description: string;
    status: string;
    submitted_at: string;
    quality_review_id?: string;
    review_status?: string;
    review_priority?: string;
    project_title: string;
    designer_name: string;
    client_name: string;
    files: any[];
    source: string;
  };
  onStartReview?: (submissionId: string) => void;
  onViewReview?: (reviewId: string) => void;
}

export default function SubmissionReviewIntegration({
  submission,
  onStartReview,
  onViewReview
}: SubmissionReviewIntegrationProps) {
  const [loading, setLoading] = useState(false);

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'submitted':
        return <Clock className="h-4 w-4 text-amber-500" />;
      case 'under_review':
        return <Eye className="h-4 w-4 text-blue-500" />;
      case 'approved':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'needs_revision':
        return <XCircle className="h-4 w-4 text-red-500" />;
      default:
        return <AlertTriangle className="h-4 w-4 text-gray-500" />;
    }
  };

  const getStatusBadge = (status: string) => {
    const baseClasses = "inline-flex items-center px-2 py-1 text-xs font-medium rounded-full";
    switch (status) {
      case 'submitted':
        return `${baseClasses} bg-amber-100 text-amber-800`;
      case 'under_review':
        return `${baseClasses} bg-blue-100 text-blue-800`;
      case 'approved':
        return `${baseClasses} bg-green-100 text-green-800`;
      case 'needs_revision':
        return `${baseClasses} bg-red-100 text-red-800`;
      default:
        return `${baseClasses} bg-gray-100 text-gray-800`;
    }
  };

  const getReviewStatusBadge = (reviewStatus?: string) => {
    if (!reviewStatus) return null;
    
    const baseClasses = "inline-flex items-center px-2 py-1 text-xs font-medium rounded-full";
    switch (reviewStatus) {
      case 'pending':
        return <span className={`${baseClasses} bg-yellow-100 text-yellow-800`}>Review Pending</span>;
      case 'in_review':
        return <span className={`${baseClasses} bg-blue-100 text-blue-800`}>In Review</span>;
      case 'approved':
        return <span className={`${baseClasses} bg-green-100 text-green-800`}>Review Approved</span>;
      case 'rejected':
        return <span className={`${baseClasses} bg-red-100 text-red-800`}>Review Rejected</span>;
      default:
        return <span className={`${baseClasses} bg-gray-100 text-gray-800`}>{reviewStatus}</span>;
    }
  };

  const getPriorityBadge = (priority?: string) => {
    if (!priority) return null;
    
    const baseClasses = "inline-flex items-center px-1.5 py-0.5 text-xs font-medium rounded";
    switch (priority) {
      case 'urgent':
        return <span className={`${baseClasses} bg-red-100 text-red-800`}>Urgent</span>;
      case 'high':
        return <span className={`${baseClasses} bg-orange-100 text-orange-800`}>High</span>;
      case 'normal':
        return <span className={`${baseClasses} bg-blue-100 text-blue-800`}>Normal</span>;
      case 'low':
        return <span className={`${baseClasses} bg-gray-100 text-gray-800`}>Low</span>;
      default:
        return null;
    }
  };

  const handleStartReview = async () => {
    if (!onStartReview) return;
    
    setLoading(true);
    try {
      await onStartReview(submission.id);
    } catch (error) {
      console.error('Error starting review:', error);
    } finally {
      setLoading(false);
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  return (
    <Card className="hover:shadow-md transition-shadow">
      <CardHeader className="pb-3">
        <div className="flex items-start justify-between">
          <div className="flex-1">
            <CardTitle className="text-lg font-semibold text-gray-900 mb-1">
              {submission.title}
            </CardTitle>
            <div className="flex items-center gap-2 text-sm text-gray-600 mb-2">
              <span>{submission.project_title}</span>
              <span>•</span>
              <span>{submission.designer_name}</span>
              <span>•</span>
              <span>{formatDate(submission.submitted_at)}</span>
            </div>
            <div className="flex items-center gap-2">
              <span className={getStatusBadge(submission.status)}>
                {getStatusIcon(submission.status)}
                <span className="ml-1 capitalize">{submission.status.replace('_', ' ')}</span>
              </span>
              {getReviewStatusBadge(submission.review_status)}
              {getPriorityBadge(submission.review_priority)}
              <Badge variant="outline" className="text-xs">
                {submission.source}
              </Badge>
            </div>
          </div>
        </div>
      </CardHeader>

      <CardContent className="pt-0">
        <p className="text-sm text-gray-600 mb-4 line-clamp-2">
          {submission.description}
        </p>

        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2 text-sm text-gray-500">
            <FileText className="h-4 w-4" />
            <span>{submission.files?.length || 0} files</span>
          </div>

          <div className="flex items-center gap-2">
            {/* View Submission Button */}
            <Link href={`/quality/submissions/${submission.id}`}>
              <Button variant="outline" size="sm" className="flex items-center gap-1">
                <Eye className="h-3 w-3" />
                View
              </Button>
            </Link>

            {/* Quality Review Actions */}
            {submission.quality_review_id ? (
              <Link href={`/quality/reviews/${submission.quality_review_id}`}>
                <Button size="sm" className="flex items-center gap-1">
                  <Star className="h-3 w-3" />
                  Review
                  <ExternalLink className="h-3 w-3" />
                </Button>
              </Link>
            ) : (
              <Button 
                size="sm" 
                onClick={handleStartReview}
                disabled={loading}
                className="flex items-center gap-1"
              >
                <Star className="h-3 w-3" />
                {loading ? 'Starting...' : 'Start Review'}
                <ArrowRight className="h-3 w-3" />
              </Button>
            )}
          </div>
        </div>

        {/* Workflow Status */}
        {submission.quality_review_id && (
          <div className="mt-3 pt-3 border-t border-gray-100">
            <div className="flex items-center justify-between text-xs text-gray-500">
              <span>Quality Review: {submission.quality_review_id.slice(0, 8)}...</span>
              <div className="flex items-center gap-1">
                <MessageSquare className="h-3 w-3" />
                <span>Linked to review process</span>
              </div>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
