-- =====================================================
-- CLEANUP QUALITY FEEDBACK RLS POLICIES
-- This removes duplicate policies and fixes the null qual issue
-- =====================================================

-- Step 1: Drop all existing policies to start fresh
DROP POLICY IF EXISTS "Designers can view their feedback" ON quality_feedback;
DROP POLICY IF EXISTS "Quality Team can manage feedback" ON quality_feedback;
DROP POLICY IF EXISTS "Quality team can create feedback" ON quality_feedback;
DROP POLICY IF EXISTS "Quality team can delete feedback" ON quality_feedback;
DROP POLICY IF EXISTS "Quality team can update feedback" ON quality_feedback;
DROP POLICY IF EXISTS "Quality team can view all feedback" ON quality_feedback;

-- Step 2: Create clean, non-duplicate policies

-- Policy 1: Quality team can view all feedback
CREATE POLICY "quality_team_view_all_feedback" ON quality_feedback
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM profiles 
            WHERE profiles.id = auth.uid() 
            AND profiles.role IN ('quality_team', 'admin', 'manager')
        )
    );

-- Policy 2: Designers can view their own feedback
CREATE POLICY "designers_view_own_feedback" ON quality_feedback
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM quality_reviews_new qr
            WHERE qr.id = quality_feedback.review_id 
            AND qr.designer_id = auth.uid()
        )
    );

-- Policy 3: Quality team can insert feedback
CREATE POLICY "quality_team_insert_feedback" ON quality_feedback
    FOR INSERT WITH CHECK (
        EXISTS (
            SELECT 1 FROM profiles 
            WHERE profiles.id = auth.uid() 
            AND profiles.role IN ('quality_team', 'admin', 'manager')
        )
    );

-- Policy 4: Quality team can update feedback
CREATE POLICY "quality_team_update_feedback" ON quality_feedback
    FOR UPDATE USING (
        EXISTS (
            SELECT 1 FROM profiles 
            WHERE profiles.id = auth.uid() 
            AND profiles.role IN ('quality_team', 'admin', 'manager')
        )
    );

-- Policy 5: Quality team can delete feedback
CREATE POLICY "quality_team_delete_feedback" ON quality_feedback
    FOR DELETE USING (
        EXISTS (
            SELECT 1 FROM profiles 
            WHERE profiles.id = auth.uid() 
            AND profiles.role IN ('quality_team', 'admin', 'manager')
        )
    );

-- Step 3: Verify the cleanup worked
SELECT 
    'Cleaned Policies' as status,
    schemaname,
    tablename,
    policyname,
    permissive,
    cmd,
    CASE 
        WHEN qual IS NULL THEN '❌ NULL QUAL'
        WHEN length(qual) > 50 THEN '✅ HAS QUAL'
        ELSE '⚠️ SHORT QUAL'
    END as qual_status
FROM pg_policies 
WHERE tablename = 'quality_feedback'
ORDER BY policyname;

-- Step 4: Test that policies work correctly
DO $$
BEGIN
    RAISE NOTICE '✅ RLS Policies cleaned up successfully';
    RAISE NOTICE 'Total policies: %', (
        SELECT COUNT(*) 
        FROM pg_policies 
        WHERE tablename = 'quality_feedback'
    );
END $$;
