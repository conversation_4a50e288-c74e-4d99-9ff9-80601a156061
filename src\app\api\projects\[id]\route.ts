import { type NextRequest, NextResponse } from 'next/server';
import { supabase } from '@/lib/supabase';

/**
 * GET /api/projects/[id]
 * Gets a specific project with all related data
 */
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    const projectId = id;

    const { data: project, error } = await supabase
      .from('projects')
      .select(`
        *,
        client:profiles!client_id(id, full_name, email, avatar_url),
        designer:profiles!designer_id(id, full_name, email, avatar_url, specialization)
      `)
      .eq('id', projectId)
      .single();

    if (error) {
      console.error('Error fetching project:', error);
      return NextResponse.json(
        { error: 'Project not found' },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      project
    }, { status: 200 });

  } catch (error) {
    console.error('Error in get project API:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

/**
 * PATCH /api/projects/[id]
 * Updates a project
 */
export async function PATCH(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const projectId = params.id;
    const updateData = await request.json();

    // Validate required fields if they're being updated
    if (updateData.title !== undefined && !updateData.title.trim()) {
      return NextResponse.json(
        { error: 'Project title is required' },
        { status: 400 }
      );
    }

    if (updateData.description !== undefined && !updateData.description.trim()) {
      return NextResponse.json(
        { error: 'Project description is required' },
        { status: 400 }
      );
    }

    // Prepare update data with timestamp
    const finalUpdateData = {
      ...updateData,
      updated_at: new Date().toISOString()
    };

    // Remove undefined values
    Object.keys(finalUpdateData).forEach(key => {
      if (finalUpdateData[key] === undefined) {
        delete finalUpdateData[key];
      }
    });

    const { data: project, error } = await supabase
      .from('projects')
      .update(finalUpdateData)
      .eq('id', projectId)
      .select(`
        *,
        client:profiles!client_id(id, full_name, email, avatar_url),
        designer:profiles!designer_id(id, full_name, email, avatar_url, specialization)
      `)
      .single();

    if (error) {
      console.error('Error updating project:', error);
      return NextResponse.json(
        { error: 'Failed to update project' },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      project,
      message: 'Project updated successfully'
    }, { status: 200 });

  } catch (error) {
    console.error('Error in update project API:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

/**
 * DELETE /api/projects/[id]
 * Deletes a project (admin only)
 */
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const projectId = params.id;

    // Get auth header to verify admin access
    const authHeader = request.headers.get('authorization');
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Verify project exists
    const { data: project, error: fetchError } = await supabase
      .from('projects')
      .select('id, title')
      .eq('id', projectId)
      .single();

    if (fetchError || !project) {
      return NextResponse.json(
        { error: 'Project not found' },
        { status: 404 }
      );
    }

    // Delete the project (cascade will handle related records)
    const { error: deleteError } = await supabase
      .from('projects')
      .delete()
      .eq('id', projectId);

    if (deleteError) {
      console.error('Error deleting project:', deleteError);
      return NextResponse.json(
        { error: 'Failed to delete project' },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      message: 'Project deleted successfully'
    }, { status: 200 });

  } catch (error) {
    console.error('Error in delete project API:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
