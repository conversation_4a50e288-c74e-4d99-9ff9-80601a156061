// Disable the trigger that's causing the priority constraint violation
const { createClient } = require('@supabase/supabase-js');
require('dotenv').config({ path: '.env.local' });

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_SERVICE_KEY || process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('Missing Supabase configuration.');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

async function disableTrigger() {
  try {
    console.log('Disabling problematic triggers...');
    
    // The most direct approach is to temporarily disable the trigger
    // by executing raw SQL through a function call
    
    // Try to execute the SQL to drop triggers
    const sqlCommands = [
      'DROP TRIGGER IF EXISTS trigger_create_quality_review ON project_submissions;',
      'DROP TRIGGER IF EXISTS trigger_create_quality_assignment ON project_submissions;',
      'DROP TRIGGER IF EXISTS trigger_auto_assign_quality_review ON project_submissions;',
      'DROP TRIGGER IF EXISTS trigger_auto_assign_quality_review_new ON project_submissions;',
      'DROP TRIGGER IF EXISTS trigger_quality_review_auto_create ON project_submissions;'
    ];
    
    for (const sql of sqlCommands) {
      try {
        console.log(`Executing: ${sql}`);
        // Note: This might not work directly with Supabase client
        // The actual fix should be applied through the Supabase dashboard or direct database access
      } catch (error) {
        console.log(`Command failed (expected): ${error.message}`);
      }
    }
    
    console.log('\\n⚠️  Direct trigger modification requires database admin access.');
    console.log('\\nTo fix this issue, you need to execute the following SQL commands');
    console.log('through the Supabase dashboard or direct database access:');
    console.log('\\n--- SQL Commands to Execute ---');
    
    sqlCommands.forEach((cmd, index) => {
      console.log(`${index + 1}. ${cmd}`);
    });
    
    console.log('\\n--- Alternative Fix ---');
    console.log('If you can\'t access the database directly, you can:');
    console.log('1. Modify the trigger function to use "normal" instead of "medium" priority');
    console.log('2. Or temporarily disable auto-creation of quality reviews');
    console.log('3. Create quality reviews manually through the application code');
    
    // Test if the trigger is still active
    console.log('\\n--- Testing Trigger Status ---');
    console.log('Attempting to create a test submission to check if trigger is still active...');
    
    const testSubmissionData = {
      project_id: '48f13612-175b-4e2f-bf78-fb40efc3e73c',
      description: 'Test submission to check trigger',
      status: 'draft', // Use draft status to avoid triggering
      submission_type: 'milestone',
      files: []
    };
    
    const { data: submission, error: submissionError } = await supabase
      .from('project_submissions')
      .insert(testSubmissionData)
      .select()
      .single();
    
    if (submissionError) {
      console.error('❌ Error creating test submission:', submissionError);
    } else {
      console.log('✅ Test submission created successfully (draft status)');
      
      // Now update to submitted status to trigger the quality review creation
      console.log('Updating to submitted status to test trigger...');
      
      const { error: updateError } = await supabase
        .from('project_submissions')
        .update({ status: 'submitted' })
        .eq('id', submission.id);
      
      if (updateError) {
        console.log('❌ Trigger still active - update failed:', updateError.message);
        if (updateError.message.includes('quality_reviews_new_priority_check')) {
          console.log('\\n🎯 CONFIRMED: The trigger is still creating quality reviews with "medium" priority');
          console.log('\\nIMPORTANT: You need to fix the trigger function in the database');
          console.log('The fix requires changing "medium" to "normal" in the trigger function');
        }
      } else {
        console.log('✅ Update successful - trigger might be fixed or disabled');
      }
      
      // Clean up
      await supabase
        .from('project_submissions')
        .delete()
        .eq('id', submission.id);
      
      console.log('✅ Test submission cleaned up');
    }
    
  } catch (error) {
    console.error('❌ Error in trigger test:', error);
  }
}

disableTrigger();
