-- =====================================================
-- FIX SLA ALERTS TABLE - Create missing table to fix SLA monitor error
-- Run this in Supabase SQL Editor
-- =====================================================

-- Create sla_alerts table if it doesn't exist
CREATE TABLE IF NOT EXISTS sla_alerts (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    review_id UUID NOT NULL REFERENCES quality_reviews_new(id) ON DELETE CASCADE,
    escalation_level INTEGER NOT NULL CHECK (escalation_level IN (1, 2, 3)),
    alert_type VARCHAR(50) NOT NULL CHECK (alert_type IN ('approaching', 'overdue', 'critical')),
    sent_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    sent_to UUID REFERENCES profiles(id),
    message TEXT,
    acknowledged BOOLEAN DEFAULT FALSE,
    acknowledged_at TIMESTAMP WITH TIME ZONE,
    acknowledged_by UUID REFERENCES profiles(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_sla_alerts_review_id ON sla_alerts(review_id);
CREATE INDEX IF NOT EXISTS idx_sla_alerts_escalation_level ON sla_alerts(escalation_level);
CREATE INDEX IF NOT EXISTS idx_sla_alerts_sent_at ON sla_alerts(sent_at);
CREATE INDEX IF NOT EXISTS idx_sla_alerts_acknowledged ON sla_alerts(acknowledged);

-- Create RLS policies
ALTER TABLE sla_alerts ENABLE ROW LEVEL SECURITY;

-- Policy for quality team and admins to view all alerts
CREATE POLICY "Quality team can view all SLA alerts" ON sla_alerts
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM profiles 
            WHERE profiles.id = auth.uid() 
            AND profiles.role IN ('quality_team', 'admin', 'manager')
        )
    );

-- Policy for quality team and admins to insert alerts
CREATE POLICY "Quality team can create SLA alerts" ON sla_alerts
    FOR INSERT WITH CHECK (
        EXISTS (
            SELECT 1 FROM profiles 
            WHERE profiles.id = auth.uid() 
            AND profiles.role IN ('quality_team', 'admin', 'manager')
        )
    );

-- Policy for quality team and admins to update alerts (acknowledge)
CREATE POLICY "Quality team can update SLA alerts" ON sla_alerts
    FOR UPDATE USING (
        EXISTS (
            SELECT 1 FROM profiles 
            WHERE profiles.id = auth.uid() 
            AND profiles.role IN ('quality_team', 'admin', 'manager')
        )
    );

-- Create function to automatically create SLA alerts
CREATE OR REPLACE FUNCTION create_sla_alert(
    p_review_id UUID,
    p_escalation_level INTEGER,
    p_alert_type VARCHAR(50),
    p_message TEXT DEFAULT NULL
) RETURNS UUID AS $$
DECLARE
    alert_id UUID;
    quality_team_member UUID;
BEGIN
    -- Get a quality team member to send alert to (could be improved with assignment logic)
    SELECT id INTO quality_team_member
    FROM profiles 
    WHERE role = 'quality_team' 
    LIMIT 1;
    
    -- Insert the alert
    INSERT INTO sla_alerts (
        review_id,
        escalation_level,
        alert_type,
        sent_to,
        message
    ) VALUES (
        p_review_id,
        p_escalation_level,
        p_alert_type,
        quality_team_member,
        COALESCE(p_message, 'SLA alert for review ' || p_review_id)
    ) RETURNING id INTO alert_id;
    
    RETURN alert_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create function to check and create SLA alerts for overdue reviews
CREATE OR REPLACE FUNCTION check_and_create_sla_alerts() RETURNS INTEGER AS $$
DECLARE
    review_record RECORD;
    alert_count INTEGER := 0;
    hours_remaining NUMERIC;
    existing_alert_count INTEGER;
BEGIN
    -- Loop through active reviews
    FOR review_record IN 
        SELECT id, sla_deadline, priority, created_at
        FROM quality_reviews_new 
        WHERE status IN ('pending', 'in_review')
    LOOP
        -- Calculate hours remaining
        hours_remaining := EXTRACT(EPOCH FROM (review_record.sla_deadline - NOW())) / 3600;
        
        -- Check for approaching deadline (2 hours before)
        IF hours_remaining <= 2 AND hours_remaining > 0 THEN
            -- Check if we already sent this type of alert recently
            SELECT COUNT(*) INTO existing_alert_count
            FROM sla_alerts 
            WHERE review_id = review_record.id 
            AND escalation_level = 1 
            AND sent_at > NOW() - INTERVAL '2 hours';
            
            IF existing_alert_count = 0 THEN
                PERFORM create_sla_alert(
                    review_record.id, 
                    1, 
                    'approaching',
                    'Review deadline approaching in ' || ROUND(hours_remaining, 1) || ' hours'
                );
                alert_count := alert_count + 1;
            END IF;
        END IF;
        
        -- Check for overdue (0-24 hours overdue)
        IF hours_remaining <= 0 AND hours_remaining > -24 THEN
            SELECT COUNT(*) INTO existing_alert_count
            FROM sla_alerts 
            WHERE review_id = review_record.id 
            AND escalation_level = 2 
            AND sent_at > NOW() - INTERVAL '4 hours';
            
            IF existing_alert_count = 0 THEN
                PERFORM create_sla_alert(
                    review_record.id, 
                    2, 
                    'overdue',
                    'Review is overdue by ' || ROUND(ABS(hours_remaining), 1) || ' hours'
                );
                alert_count := alert_count + 1;
            END IF;
        END IF;
        
        -- Check for critical overdue (more than 24 hours overdue)
        IF hours_remaining <= -24 THEN
            SELECT COUNT(*) INTO existing_alert_count
            FROM sla_alerts 
            WHERE review_id = review_record.id 
            AND escalation_level = 3 
            AND sent_at > NOW() - INTERVAL '8 hours';
            
            IF existing_alert_count = 0 THEN
                PERFORM create_sla_alert(
                    review_record.id, 
                    3, 
                    'critical',
                    'Review is critically overdue by ' || ROUND(ABS(hours_remaining)/24, 1) || ' days'
                );
                alert_count := alert_count + 1;
            END IF;
        END IF;
    END LOOP;
    
    RETURN alert_count;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create a simple view for SLA dashboard data
CREATE OR REPLACE VIEW sla_dashboard_view AS
SELECT 
    COUNT(*) as total_reviews,
    COUNT(*) FILTER (WHERE status IN ('pending', 'in_review')) as active_reviews,
    COUNT(*) FILTER (WHERE sla_deadline > NOW()) as on_time,
    COUNT(*) FILTER (WHERE sla_deadline <= NOW() AND sla_deadline > NOW() - INTERVAL '2 hours') as approaching,
    COUNT(*) FILTER (WHERE sla_deadline < NOW()) as overdue,
    COUNT(*) FILTER (WHERE sla_deadline < NOW() - INTERVAL '24 hours') as critical,
    COALESCE(AVG(EXTRACT(EPOCH FROM (reviewed_at - created_at))/3600), 0) as avg_completion_hours
FROM quality_reviews_new 
WHERE created_at > NOW() - INTERVAL '30 days';

-- Grant permissions
GRANT SELECT ON sla_dashboard_view TO authenticated;
GRANT EXECUTE ON FUNCTION create_sla_alert TO authenticated;
GRANT EXECUTE ON FUNCTION check_and_create_sla_alerts TO authenticated;

-- Insert some sample data to test (optional)
DO $$
BEGIN
    -- Only insert if there are quality reviews but no alerts
    IF EXISTS (SELECT 1 FROM quality_reviews_new LIMIT 1) AND 
       NOT EXISTS (SELECT 1 FROM sla_alerts LIMIT 1) THEN
        
        -- Create a sample alert for testing
        INSERT INTO sla_alerts (
            review_id,
            escalation_level,
            alert_type,
            message,
            sent_at
        )
        SELECT 
            id,
            1,
            'approaching',
            'Sample SLA alert for testing',
            NOW() - INTERVAL '1 hour'
        FROM quality_reviews_new 
        WHERE status IN ('pending', 'in_review')
        LIMIT 1;
        
        RAISE NOTICE 'Created sample SLA alert for testing';
    END IF;
END $$;

-- Verify the table was created
SELECT 'sla_alerts table created successfully' as status,
       COUNT(*) as alert_count 
FROM sla_alerts;
