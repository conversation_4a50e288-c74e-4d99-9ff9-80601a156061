import { type NextRequest, NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';
import { cookies } from 'next/headers';

// Create a Supabase client for the API route
function createSupabaseClient() {
  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;
  const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!;
  
  return createClient(supabaseUrl, supabaseServiceKey, {
    auth: {
      autoRefreshToken: false,
      persistSession: false
    }
  });
}

export async function GET(request: NextRequest) {
  try {
    const supabase = createSupabaseClient();
    const { searchParams } = new URL(request.url);
    const fileUrl = searchParams.get('fileUrl');
    const includeResolved = searchParams.get('includeResolved') === 'true';

    if (!fileUrl) {
      return NextResponse.json({ error: 'File URL is required' }, { status: 400 });
    }

    // For API routes, we need to get the authorization header for user context
    const authorization = request.headers.get('authorization');
    if (!authorization) {
      return NextResponse.json({ error: 'Authorization header required' }, { status: 401 });
    }

    // Extract the JWT token from the Authorization header
    const token = authorization.replace('Bearer ', '');
    
    // Verify the JWT token and get user info
    const { data: { user }, error: authError } = await supabase.auth.getUser(token);
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get user profile to check permissions
    const { data: profile } = await supabase
      .from('profiles')
      .select('role')
      .eq('id', user.id)
      .single();

    if (!profile || !['quality_team', 'admin', 'manager', 'designer'].includes(profile.role)) {
      return NextResponse.json({ error: 'Insufficient permissions' }, { status: 403 });
    }

    // Fetch annotations for the file
    let query = supabase
      .from('file_annotations')
      .select(`
        id,
        annotation_type,
        x_position,
        y_position,
        width,
        height,
        title,
        description,
        color,
        status,
        reviewer_name,
        created_at,
        annotation_responses(
          id,
          response_text,
          response_type,
          responder_name,
          responder_role,
          created_at
        )
      `)
      .eq('file_url', fileUrl);

    // Only filter by status if we're not including resolved annotations
    if (!includeResolved) {
      query = query.eq('status', 'active');
    }

    const { data: annotations, error } = await query
      .order('created_at', { ascending: false });

    if (error) {
      console.error('Error fetching annotations:', error);
      return NextResponse.json({ error: 'Failed to fetch annotations' }, { status: 500 });
    }

    return NextResponse.json({ annotations: annotations || [] });
  } catch (error) {
    console.error('Error in annotations GET:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

export async function POST(request: NextRequest) {
  try {
    const supabase = createSupabaseClient();
    const body = await request.json();

    const {
      fileUrl,
      fileName,
      fileType,
      annotationType,
      xPosition,
      yPosition,
      width,
      height,
      title,
      description,
      color,
      qualityReviewId,
      submissionId,
      submissionType
    } = body;

    // Validate required fields
    if (!fileUrl || !annotationType || xPosition === undefined || yPosition === undefined) {
      return NextResponse.json({ error: 'Missing required fields' }, { status: 400 });
    }

    // For API routes, we need to get the authorization header for user context
    const authorization = request.headers.get('authorization');
    if (!authorization) {
      return NextResponse.json({ error: 'Authorization header required' }, { status: 401 });
    }

    // Extract the JWT token from the Authorization header
    const token = authorization.replace('Bearer ', '');
    
    // Verify the JWT token and get user info
    const { data: { user }, error: userError } = await supabase.auth.getUser(token);
    if (userError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get user profile
    const { data: profile } = await supabase
      .from('profiles')
      .select('role, full_name')
      .eq('id', user.id)
      .single();

    if (!profile || !['quality_team', 'admin', 'manager'].includes(profile.role)) {
      return NextResponse.json({ error: 'Only quality team can create annotations' }, { status: 403 });
    }

    // Create annotation
    const { data: annotation, error } = await supabase
      .from('file_annotations')
      .insert({
        file_url: fileUrl,
        file_name: fileName,
        file_type: fileType,
        annotation_type: annotationType,
        x_position: xPosition,
        y_position: yPosition,
        width: width || null,
        height: height || null,
        title: title || null,
        description: description || null,
        color: color || '#ff0000',
        quality_review_id: qualityReviewId || null,
        submission_id: submissionId || null,
        submission_type: submissionType || 'project',
        created_by: user.id,
        reviewer_name: profile.full_name,
        status: 'active'
      })
      .select()
      .single();

    if (error) {
      console.error('Error creating annotation:', error);
      return NextResponse.json({ error: 'Failed to create annotation' }, { status: 500 });
    }

    return NextResponse.json({ annotation }, { status: 201 });
  } catch (error) {
    console.error('Error in annotations POST:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

export async function PUT(request: NextRequest) {
  try {
    const supabase = createSupabaseClient();
    const body = await request.json();

    const {
      id,
      title,
      description,
      status,
      annotationType,
      color
    } = body;

    if (!id) {
      return NextResponse.json({ error: 'Annotation ID is required' }, { status: 400 });
    }

    // For API routes, we need to get the authorization header for user context
    const authorization = request.headers.get('authorization');
    if (!authorization) {
      return NextResponse.json({ error: 'Authorization header required' }, { status: 401 });
    }

    // Extract the JWT token from the Authorization header
    const token = authorization.replace('Bearer ', '');
    
    // Verify the JWT token and get user info
    const { data: { user }, error: userError } = await supabase.auth.getUser(token);
    if (userError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get user profile
    const { data: profile } = await supabase
      .from('profiles')
      .select('role, full_name')
      .eq('id', user.id)
      .single();

    if (!profile || !['quality_team', 'admin', 'manager'].includes(profile.role)) {
      return NextResponse.json({ error: 'Only quality team can update annotations' }, { status: 403 });
    }

    // Update annotation
    const updateData: any = {};
    if (title !== undefined) updateData.title = title;
    if (description !== undefined) updateData.description = description;
    if (status !== undefined) {
      updateData.status = status;
      if (status === 'resolved') {
        updateData.resolved_at = new Date().toISOString();
        updateData.resolved_by = user.id;
      }
    }
    if (annotationType !== undefined) updateData.annotation_type = annotationType;
    if (color !== undefined) updateData.color = color;

    const { data: annotation, error } = await supabase
      .from('file_annotations')
      .update(updateData)
      .eq('id', id)
      .select()
      .single();

    if (error) {
      console.error('Error updating annotation:', error);
      return NextResponse.json({ error: 'Failed to update annotation' }, { status: 500 });
    }

    return NextResponse.json({ annotation });
  } catch (error) {
    console.error('Error in annotations PUT:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

export async function DELETE(request: NextRequest) {
  try {
    const supabase = createSupabaseClient();
    const { searchParams } = new URL(request.url);
    const id = searchParams.get('id');

    if (!id) {
      return NextResponse.json({ error: 'Annotation ID is required' }, { status: 400 });
    }

    // For API routes, we need to get the authorization header for user context
    const authorization = request.headers.get('authorization');
    if (!authorization) {
      return NextResponse.json({ error: 'Authorization header required' }, { status: 401 });
    }

    // Extract the JWT token from the Authorization header
    const token = authorization.replace('Bearer ', '');
    
    // Verify the JWT token and get user info
    const { data: { user }, error: userError } = await supabase.auth.getUser(token);
    if (userError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get user profile
    const { data: profile } = await supabase
      .from('profiles')
      .select('role')
      .eq('id', user.id)
      .single();

    if (!profile || !['quality_team', 'admin', 'manager'].includes(profile.role)) {
      return NextResponse.json({ error: 'Only quality team can delete annotations' }, { status: 403 });
    }

    // Delete annotation
    const { error } = await supabase
      .from('file_annotations')
      .delete()
      .eq('id', id);

    if (error) {
      console.error('Error deleting annotation:', error);
      return NextResponse.json({ error: 'Failed to delete annotation' }, { status: 500 });
    }

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Error in annotations DELETE:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
