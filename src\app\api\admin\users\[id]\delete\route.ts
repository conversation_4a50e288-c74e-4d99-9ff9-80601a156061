import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';
import { supabase } from '@/lib/supabase';

// Create admin client for user deletion
const supabaseAdmin = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!,
  {
    auth: {
      autoRefreshToken: false,
      persistSession: false
    }
  }
);

export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Await params in Next.js 15
    const { id } = await params;
    console.log('Delete user API called for user ID:', id);

    // Get authorization header
    const authHeader = request.headers.get('authorization');
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return NextResponse.json(
        { error: 'Authorization header required' },
        { status: 401 }
      );
    }

    const token = authHeader.split(' ')[1];

    // Get current user and verify admin role
    const { data: { user }, error: authError } = await supabase.auth.getUser(token);

    console.log('Auth check result:', { user: user?.id, authError });

    if (authError || !user) {
      console.log('Authentication failed:', authError);
      return NextResponse.json(
        { error: 'Authentication required - Please log in again' },
        { status: 401 }
      );
    }

    // Check if user has admin role
    const { data: profile, error: profileError } = await supabase
      .from('profiles')
      .select('role')
      .eq('id', user.id)
      .single();

    console.log('Profile check result:', { profile, profileError });

    if (profileError) {
      console.log('Profile fetch error:', profileError);
      return NextResponse.json(
        { error: 'Failed to verify user permissions' },
        { status: 500 }
      );
    }

    if (!profile || profile.role !== 'admin') {
      console.log('Access denied - user role:', profile?.role);
      return NextResponse.json(
        { error: 'Access denied - Admin privileges required' },
        { status: 403 }
      );
    }

    const userIdToDelete = id;

    // Validate user ID
    if (!userIdToDelete) {
      return NextResponse.json(
        { error: 'Invalid user ID provided' },
        { status: 400 }
      );
    }

    if (userIdToDelete === user.id) {
      return NextResponse.json(
        { error: 'Cannot delete your own account' },
        { status: 400 }
      );
    }

    // Get user details before deletion
    const { data: userToDelete, error: getUserError } = await supabase
      .from('profiles')
      .select('id, full_name, email, role')
      .eq('id', userIdToDelete)
      .single();

    console.log('User to delete:', { userToDelete, getUserError });

    if (getUserError || !userToDelete) {
      console.log('User not found:', getUserError);
      return NextResponse.json(
        { error: 'User not found in database' },
        { status: 404 }
      );
    }

    // Prevent deletion of admin users by other admins
    if (userToDelete.role === 'admin') {
      console.log('Attempted to delete admin user:', userToDelete.id);
      return NextResponse.json(
        { error: 'Cannot delete admin users for security reasons' },
        { status: 403 }
      );
    }

    // Prevent deletion of other admin users (optional safety check)
    if (userToDelete.role === 'admin') {
      return NextResponse.json(
        { error: 'Cannot delete admin users' },
        { status: 400 }
      );
    }

    // Start transaction-like operations
    try {
      console.log('Starting user deletion process...');

      // Step 1: Handle related data that might prevent deletion
      console.log('Step 1: Cleaning up related data...');

      // Update foreign key references to NULL where possible
      const cleanupOperations = [
        // Update invitations
        supabaseAdmin.from('profiles').update({ invited_by: null }).eq('invited_by', userIdToDelete),
        supabaseAdmin.from('profiles').update({ approved_by: null }).eq('approved_by', userIdToDelete),
        supabaseAdmin.from('profiles').update({ rejected_by: null }).eq('rejected_by', userIdToDelete),

        // Update file annotations
        supabaseAdmin.from('file_annotations').update({ created_by: null }).eq('created_by', userIdToDelete),
        supabaseAdmin.from('file_annotations').update({ resolved_by: null }).eq('resolved_by', userIdToDelete),

        // Update annotation responses
        supabaseAdmin.from('annotation_responses').update({ created_by: null }).eq('created_by', userIdToDelete),

        // Update annotation history
        supabaseAdmin.from('annotation_history').update({ changed_by: null }).eq('changed_by', userIdToDelete),
      ];

      // Execute cleanup operations
      const cleanupResults = await Promise.allSettled(cleanupOperations);
      cleanupResults.forEach((result, index) => {
        if (result.status === 'rejected') {
          console.warn(`Cleanup operation ${index} failed:`, result.reason);
        }
      });

      // Step 2: Delete records that should be removed (CASCADE behavior)
      console.log('Step 2: Deleting cascade records...');

      const cascadeDeletes = [
        // Delete user's projects (if they're the client or designer)
        supabaseAdmin.from('projects').delete().eq('client_id', userIdToDelete),
        supabaseAdmin.from('projects').delete().eq('designer_id', userIdToDelete),

        // Delete portfolio projects
        supabaseAdmin.from('portfolio_projects').delete().eq('designer_id', userIdToDelete),

        // Delete connections
        supabaseAdmin.from('connections').delete().eq('designer_id', userIdToDelete),
        supabaseAdmin.from('connections').delete().eq('client_id', userIdToDelete),

        // Delete manager dashboard cache
        supabaseAdmin.from('manager_dashboard_cache').delete().eq('manager_id', userIdToDelete),
      ];

      const cascadeResults = await Promise.allSettled(cascadeDeletes);
      cascadeResults.forEach((result, index) => {
        if (result.status === 'rejected') {
          console.warn(`Cascade delete ${index} failed:`, result.reason);
        }
      });

      // Step 3: Delete the profile record
      console.log('Step 3: Deleting profile...');
      const { error: profileDeleteError } = await supabaseAdmin
        .from('profiles')
        .delete()
        .eq('id', userIdToDelete);

      if (profileDeleteError) {
        console.error('Profile deletion error:', profileDeleteError);
        return NextResponse.json(
          { error: 'Failed to delete user profile from database' },
          { status: 500 }
        );
      }

      // Step 4: Delete user from Auth (after database cleanup)
      console.log('Step 4: Deleting from auth...');
      const { error: authDeleteError } = await supabaseAdmin.auth.admin.deleteUser(userIdToDelete);

      if (authDeleteError) {
        console.error('Auth deletion error:', authDeleteError);
        // Don't fail here - profile is already deleted, auth cleanup can be manual if needed
        console.warn('User profile deleted but auth cleanup failed - may need manual intervention');
      }

      // Log the deletion activity
      await supabaseAdmin
        .from('admin_activity_logs')
        .insert({
          admin_id: user.id,
          action: 'delete_user',
          target_type: 'user',
          target_id: userIdToDelete,
          details: {
            deleted_user: {
              id: userToDelete.id,
              name: userToDelete.full_name,
              email: userToDelete.email,
              role: userToDelete.role
            }
          }
        });

      return NextResponse.json({
        success: true,
        message: `User ${userToDelete.full_name} (${userToDelete.email}) has been successfully deleted`
      });

    } catch (error) {
      console.error('User deletion error:', error);
      return NextResponse.json(
        { error: 'Failed to delete user - transaction error' },
        { status: 500 }
      );
    }

  } catch (error) {
    console.error('Delete user API error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
