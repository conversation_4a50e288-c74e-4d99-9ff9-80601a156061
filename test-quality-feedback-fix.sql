-- =====================================================
-- TEST QUALITY FEEDBACK FIX
-- Run this after the constraint fix to verify everything works
-- =====================================================

-- Test 1: Check constraint is pointing to correct table
SELECT 
    'Constraint Check' as test_name,
    tc.constraint_name,
    tc.table_name as source_table,
    ccu.table_name as target_table,
    CASE 
        WHEN ccu.table_name = 'quality_reviews_new' THEN '✅ CORRECT'
        WHEN ccu.table_name = 'quality_reviews' THEN '❌ WRONG TABLE'
        ELSE '❓ UNKNOWN'
    END as status
FROM information_schema.table_constraints tc
JOIN information_schema.constraint_column_usage ccu 
    ON tc.constraint_name = ccu.constraint_name
WHERE tc.constraint_name = 'quality_feedback_review_id_fkey'
AND tc.table_name = 'quality_feedback';

-- Test 2: Check if we can insert test data
DO $$
DECLARE
    test_review_id UUID;
    test_standard_id UUID;
    test_feedback_id UUID;
BEGIN
    -- Get a real review ID from quality_reviews_new
    SELECT id INTO test_review_id 
    FROM quality_reviews_new 
    LIMIT 1;
    
    -- Get a real standard ID
    SELECT id INTO test_standard_id 
    FROM quality_standards 
    LIMIT 1;
    
    IF test_review_id IS NOT NULL AND test_standard_id IS NOT NULL THEN
        -- Try to insert test feedback
        INSERT INTO quality_feedback (
            review_id,
            standard_id,
            passed,
            score,
            comments,
            suggestions
        ) VALUES (
            test_review_id,
            test_standard_id,
            true,
            4,
            'Test feedback comment',
            'Test suggestions'
        ) RETURNING id INTO test_feedback_id;
        
        RAISE NOTICE '✅ Test Insert SUCCESS: Created feedback record %', test_feedback_id;
        
        -- Clean up test data
        DELETE FROM quality_feedback WHERE id = test_feedback_id;
        RAISE NOTICE '✅ Test cleanup completed';
        
    ELSE
        RAISE NOTICE '❌ Test SKIPPED: No review or standard data found';
        RAISE NOTICE 'Review ID: %, Standard ID: %', test_review_id, test_standard_id;
    END IF;
    
EXCEPTION
    WHEN OTHERS THEN
        RAISE NOTICE '❌ Test Insert FAILED: %', SQLERRM;
END $$;

-- Test 3: Check table relationships
SELECT 
    'Table Relationships' as test_name,
    COUNT(qr.id) as quality_reviews_new_count,
    COUNT(qf.id) as quality_feedback_count,
    COUNT(qs.id) as quality_standards_count
FROM quality_reviews_new qr
FULL OUTER JOIN quality_feedback qf ON qr.id = qf.review_id
FULL OUTER JOIN quality_standards qs ON qf.standard_id = qs.id;

-- Test 4: Show sample data structure
SELECT 
    'Sample Data' as test_name,
    qr.id as review_id,
    qr.status as review_status,
    COUNT(qf.id) as feedback_count
FROM quality_reviews_new qr
LEFT JOIN quality_feedback qf ON qr.id = qf.review_id
GROUP BY qr.id, qr.status
LIMIT 5;

-- Test 5: Check RLS policies
SELECT 
    'RLS Policies' as test_name,
    schemaname,
    tablename,
    policyname,
    permissive,
    roles,
    cmd,
    qual
FROM pg_policies 
WHERE tablename = 'quality_feedback'
ORDER BY policyname;
