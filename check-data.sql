-- Simple data check script
-- Run this to see what data exists in the database

SELECT 'Projects Count:' as info, COUNT(*) as count FROM projects;
SELECT 'Profiles Count:' as info, COUNT(*) as count FROM profiles;
SELECT 'Legacy Submissions Count:' as info, COUNT(*) as count FROM submissions;
SELECT 'Project Submissions Count:' as info, COUNT(*) as count FROM project_submissions;
SELECT 'Quality Reviews Count:' as info, COUNT(*) as count FROM quality_reviews_new;

-- Show actual data
SELECT 'Sample Projects:' as info;
SELECT id, title, client_id, designer_id, status FROM projects LIMIT 3;

SELECT 'Sample Profiles:' as info;
SELECT id, email, role FROM profiles WHERE role IN ('client', 'designer') LIMIT 5;

SELECT 'Sample Legacy Submissions:' as info;
SELECT id, project_id, designer_id, title, status, created_at FROM submissions LIMIT 5;

SELECT 'Sample Project Submissions:' as info;
SELECT id, project_id, designer_id, title, status, quality_review_id, created_at FROM project_submissions LIMIT 5;

SELECT 'Sample Quality Reviews:' as info;
SELECT id, submission_id, project_id, status, created_at FROM quality_reviews_new LIMIT 5;
