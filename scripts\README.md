# Environment Variables Sync Script

This script helps you synchronize environment variables between your local development environment and Vercel production environment.

## Features

- 🔍 **Fetch** production environment variables from Vercel
- 📊 **Compare** local vs production environment variables
- 📝 **Generate** production-ready environment file for import
- 🎯 **Identify** missing, different, or extra variables
- 📋 **Export** comparison data for analysis

## Prerequisites

1. **Vercel CLI** installed and authenticated:
   ```bash
   npm i -g vercel
   npx vercel login
   ```

2. **Project linked** to Vercel:
   ```bash
   npx vercel link
   ```

## Usage

### Quick Start

```bash
# Run the sync script
npm run env:sync

# Or run directly
node scripts/env-sync.js
```

### What the script does:

1. **Reads** your local `.env.local` file
2. **Fetches** production environment variables from Vercel
3. **Compares** the two sets of variables
4. **Generates** two output files:
   - `production.env` - Ready for Vercel import
   - `env-comparison.json` - Detailed comparison data

### Output Files

#### `production.env`
A clean environment file that you can import directly to Vercel:
- Contains all your local variables
- Includes comments showing production differences
- Ready for `vercel env add` command

#### `env-comparison.json`
Detailed comparison data in JSON format:
- Variables that are the same
- Variables with different values
- Variables only in local environment
- Variables only in production environment

## Importing to Vercel

### Method 1: Using Vercel CLI (Recommended)

```bash
# Import all variables from the generated file
npx vercel env add < production.env

# Or add variables one by one
npx vercel env add VARIABLE_NAME production
```

### Method 2: Using Vercel Dashboard

1. Go to your project settings in Vercel dashboard
2. Navigate to Environment Variables
3. Use the "Import" feature to upload `production.env`

## Example Output

```
🚀 Starting environment variables sync...

📖 Reading local environment file...
✓ Found 45 local environment variables

Fetching production environment variables from Vercel...
✓ Fetched 38 production environment variables

🔍 Comparing environments...

=== Environment Variables Comparison ===

✓ Same in both environments: 35
⚠ Different values: 3
📝 Only in local: 7
🔍 Only in production: 0

📝 Generating production environment file...
✓ Production environment file saved to: production.env
✓ Comparison data saved to: env-comparison.json
```

## Common Use Cases

### 1. Initial Production Setup
When deploying to production for the first time:
```bash
npm run env:sync
# Review production.env
# Import to Vercel
```

### 2. Environment Audit
Check what's different between environments:
```bash
npm run env:sync
# Check env-comparison.json for detailed analysis
```

### 3. Adding New Variables
After adding new environment variables locally:
```bash
npm run env:sync
# The script will identify new local variables
# Import the updated production.env to Vercel
```

## Script Configuration

You can modify these constants in `env-sync.js`:

```javascript
const LOCAL_ENV_FILE = '.env.local';     // Local env file to read
const OUTPUT_FILE = 'production.env';    // Output file for Vercel
const COMPARISON_FILE = 'env-comparison.json'; // Comparison data
```

## Troubleshooting

### "Vercel CLI not found"
```bash
npm i -g vercel
```

### "Not logged in to Vercel"
```bash
npx vercel login
```

### "Project not linked"
```bash
npx vercel link
```

### "Permission denied"
Make sure you have access to the Vercel project and the correct team/scope.

## Security Notes

- The script only reads environment variables, it doesn't modify them
- Generated files contain sensitive data - don't commit them to version control
- Add `production.env` and `env-comparison.json` to your `.gitignore`

## Files Generated

- `production.env` - Environment file ready for Vercel import
- `env-comparison.json` - Detailed comparison data
- Both files are automatically added to `.gitignore` (recommended)
