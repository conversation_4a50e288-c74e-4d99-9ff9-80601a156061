'use client';

import React, { useState, useEffect } from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Textarea } from '@/components/ui/textarea';
import { 
  MessageSquare, 
  AlertTriangle, 
  CheckCircle, 
  Info,
  Lightbulb,
  Eye,
  EyeOff,
  Send,
  Clock
} from 'lucide-react';

interface Annotation {
  id: string;
  annotation_type: 'issue' | 'suggestion' | 'approval' | 'info';
  x_position: number;
  y_position: number;
  width?: number;
  height?: number;
  title?: string;
  description: string;
  color: string;
  status: 'active' | 'resolved' | 'archived';
  reviewer_name: string;
  created_at: string;
  annotation_responses?: AnnotationResponse[];
}

interface AnnotationResponse {
  id: string;
  response_text: string;
  response_type: 'comment' | 'question' | 'clarification' | 'resolution';
  responder_name: string;
  responder_role: string;
  created_at: string;
}

interface AnnotationViewerProps {
  fileUrl: string;
  fileName: string;
  fileType: string;
  submissionId?: string;
  qualityReviewId?: string;
  userRole: 'designer' | 'client' | 'quality_team' | 'admin' | 'manager';
  userName?: string;
}

export default function AnnotationViewer({
  fileUrl,
  fileName,
  fileType,
  submissionId,
  qualityReviewId,
  userRole,
  userName = 'User'
}: AnnotationViewerProps) {
  const [annotations, setAnnotations] = useState<Annotation[]>([]);
  const [loading, setLoading] = useState(true);
  const [showAnnotations, setShowAnnotations] = useState(true);
  const [selectedAnnotation, setSelectedAnnotation] = useState<string | null>(null);
  const [responseText, setResponseText] = useState('');
  const [submittingResponse, setSubmittingResponse] = useState(false);

  const isImageFile = fileType.startsWith('image/');

  useEffect(() => {
    loadAnnotations();
  }, [fileUrl]);

  const loadAnnotations = async () => {
    if (!fileUrl) return;
    
    setLoading(true);
    try {
      const response = await fetch(`/api/quality/annotations?fileUrl=${encodeURIComponent(fileUrl)}&includeResolved=true`);
      if (response.ok) {
        const data = await response.json();
        setAnnotations(data.annotations || []);
      }
    } catch (error) {
      console.error('Error loading annotations:', error);
    } finally {
      setLoading(false);
    }
  };

  const submitResponse = async (annotationId: string) => {
    if (!responseText.trim()) return;

    setSubmittingResponse(true);
    try {
      const response = await fetch('/api/quality/annotations/responses', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          annotationId,
          responseText: responseText.trim(),
          responseType: 'comment'
        }),
      });

      if (response.ok) {
        setResponseText('');
        await loadAnnotations(); // Reload to get updated responses
      }
    } catch (error) {
      console.error('Error submitting response:', error);
    } finally {
      setSubmittingResponse(false);
    }
  };

  const getAnnotationIcon = (type: string) => {
    switch (type) {
      case 'issue':
        return <AlertTriangle className="h-4 w-4 text-red-500" />;
      case 'suggestion':
        return <Lightbulb className="h-4 w-4 text-yellow-500" />;
      case 'approval':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'info':
        return <Info className="h-4 w-4 text-blue-500" />;
      default:
        return <MessageSquare className="h-4 w-4 text-gray-500" />;
    }
  };

  const getAnnotationBadge = (type: string) => {
    const baseClasses = "text-xs font-medium px-2 py-1 rounded-full";
    switch (type) {
      case 'issue':
        return `${baseClasses} bg-red-100 text-red-800`;
      case 'suggestion':
        return `${baseClasses} bg-yellow-100 text-yellow-800`;
      case 'approval':
        return `${baseClasses} bg-green-100 text-green-800`;
      case 'info':
        return `${baseClasses} bg-blue-100 text-blue-800`;
      default:
        return `${baseClasses} bg-gray-100 text-gray-800`;
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  if (!isImageFile) {
    return (
      <Card>
        <CardContent className="p-8 text-center">
          <div className="text-gray-500">
            <MessageSquare className="h-12 w-12 mx-auto mb-4" />
            <p>Annotations are only available for image files.</p>
            <p className="text-sm mt-2">File type: {fileType}</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-4">
      {/* Toolbar */}
      <Card>
        <CardContent className="p-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <h3 className="font-medium text-gray-900">Quality Annotations</h3>
              <Badge variant="outline">
                {annotations.length} annotation{annotations.length !== 1 ? 's' : ''}
              </Badge>
            </div>
            <Button
              variant="outline"
              size="sm"
              onClick={() => setShowAnnotations(!showAnnotations)}
              className="flex items-center gap-2"
            >
              {showAnnotations ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
              {showAnnotations ? 'Hide' : 'Show'} Annotations
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Image with Annotations */}
      <Card>
        <CardContent className="p-4">
          <div className="relative overflow-auto border rounded-lg bg-gray-50">
            <div className="relative inline-block">
              <img
                src={fileUrl}
                alt={fileName}
                className="max-w-full h-auto"
                style={{ maxHeight: '600px' }}
              />

              {/* Annotation Overlays */}
              {showAnnotations && annotations.map((annotation) => (
                <div
                  key={annotation.id}
                  className={`absolute border-2 cursor-pointer transition-all ${
                    selectedAnnotation === annotation.id 
                      ? 'border-blue-500 bg-blue-100 bg-opacity-30' 
                      : 'border-red-500 bg-red-100 bg-opacity-20'
                  } ${annotation.status === 'resolved' ? 'opacity-50' : ''}`}
                  style={{
                    left: `${annotation.x_position}%`,
                    top: `${annotation.y_position}%`,
                    width: annotation.width ? `${annotation.width}%` : '20px',
                    height: annotation.height ? `${annotation.height}%` : '20px',
                    borderColor: annotation.color
                  }}
                  onClick={() => setSelectedAnnotation(
                    selectedAnnotation === annotation.id ? null : annotation.id
                  )}
                >
                  <div className="absolute -top-6 left-0 bg-white border rounded px-2 py-1 text-xs font-medium shadow-sm">
                    {getAnnotationIcon(annotation.annotation_type)}
                  </div>
                </div>
              ))}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Annotations List */}
      {annotations.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <MessageSquare className="h-5 w-5" />
              Annotations ({annotations.length})
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {annotations.map((annotation) => (
                <div
                  key={annotation.id}
                  className={`border rounded-lg p-4 transition-all ${
                    selectedAnnotation === annotation.id 
                      ? 'border-blue-300 bg-blue-50' 
                      : 'border-gray-200'
                  } ${annotation.status === 'resolved' ? 'opacity-75' : ''}`}
                >
                  <div className="flex items-start justify-between mb-2">
                    <div className="flex items-center gap-2">
                      {getAnnotationIcon(annotation.annotation_type)}
                      <span className={getAnnotationBadge(annotation.annotation_type)}>
                        {annotation.annotation_type.toUpperCase()}
                      </span>
                      {annotation.status === 'resolved' && (
                        <Badge variant="outline" className="text-green-600 border-green-300">
                          Resolved
                        </Badge>
                      )}
                    </div>
                    <div className="text-xs text-gray-500 flex items-center gap-1">
                      <Clock className="h-3 w-3" />
                      {formatDate(annotation.created_at)}
                    </div>
                  </div>

                  {annotation.title && (
                    <h4 className="font-medium text-gray-900 mb-1">{annotation.title}</h4>
                  )}
                  
                  <p className="text-gray-700 mb-2">{annotation.description}</p>
                  
                  <div className="text-sm text-gray-500 mb-3">
                    By {annotation.reviewer_name}
                  </div>

                  {/* Responses */}
                  {annotation.annotation_responses && annotation.annotation_responses.length > 0 && (
                    <div className="border-t pt-3 mt-3">
                      <h5 className="text-sm font-medium text-gray-900 mb-2">Responses:</h5>
                      <div className="space-y-2">
                        {annotation.annotation_responses.map((response) => (
                          <div key={response.id} className="bg-gray-50 rounded p-3">
                            <div className="flex items-center justify-between mb-1">
                              <span className="text-sm font-medium text-gray-900">
                                {response.responder_name}
                              </span>
                              <span className="text-xs text-gray-500">
                                {formatDate(response.created_at)}
                              </span>
                            </div>
                            <p className="text-sm text-gray-700">{response.response_text}</p>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}

                  {/* Response Form (for designers) */}
                  {userRole === 'designer' && annotation.status === 'active' && (
                    <div className="border-t pt-3 mt-3">
                      <div className="flex gap-2">
                        <Textarea
                          value={responseText}
                          onChange={(e) => setResponseText(e.target.value)}
                          placeholder="Add your response..."
                          className="flex-1"
                          rows={2}
                        />
                        <Button
                          onClick={() => submitResponse(annotation.id)}
                          disabled={!responseText.trim() || submittingResponse}
                          size="sm"
                          className="self-end"
                        >
                          <Send className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  )}
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {loading && (
        <div className="text-center py-8">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-purple-600 mx-auto"></div>
          <p className="text-gray-500 mt-2">Loading annotations...</p>
        </div>
      )}

      {!loading && annotations.length === 0 && (
        <Card>
          <CardContent className="p-8 text-center">
            <MessageSquare className="h-12 w-12 mx-auto mb-4 text-gray-400" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">No Annotations Yet</h3>
            <p className="text-gray-500">
              Quality team annotations will appear here when available.
            </p>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
