import { NextRequest, NextResponse } from 'next/server';
import { supabase } from '@/lib/supabase';
import { createClient } from '@supabase/supabase-js';

// Server-side Supabase client for database operations
const supabaseAdmin = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
);

export async function POST(request: NextRequest) {
  try {
    // Get authorization header
    const authHeader = request.headers.get('authorization');
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const token = authHeader.split(' ')[1];

    // Verify the JWT token
    const { data: user, error: authError } = await supabase.auth.getUser(token);
    if (authError || !user.user) {
      console.error('Authentication error:', authError);
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body = await request.json();
    const { annotations, fileUrl, fileName, qualityReviewId, submissionId, submissionType } = body;

    if (!annotations || !Array.isArray(annotations)) {
      return NextResponse.json({ error: 'Invalid annotations data' }, { status: 400 });
    }

    if (!fileUrl || !fileName) {
      return NextResponse.json({ error: 'File URL and name are required' }, { status: 400 });
    }

    // Get user profile for reviewer name
    const { data: profile } = await supabaseAdmin
      .from('profiles')
      .select('full_name, role')
      .eq('id', user.user.id)
      .single();

    const reviewerName = profile?.full_name || 'Unknown Reviewer';

    // Prepare bulk insert data
    const annotationsToInsert = annotations.map((annotation: any) => ({
      file_url: fileUrl,
      file_name: fileName,
      file_type: 'image',
      annotation_type: annotation.type,
      x_position: annotation.x,
      y_position: annotation.y,
      width: annotation.width || null,
      height: annotation.height || null,
      title: annotation.comment.split('\n')[0] || 'Annotation',
      description: annotation.comment,
      reviewer_id: user.user.id,
      reviewer_name: reviewerName,
      quality_review_id: qualityReviewId || null,
      submission_id: submissionId || null,
      submission_type: submissionType || 'project',
      created_at: new Date().toISOString()
    }));

    // First, delete existing annotations for this file to avoid duplicates
    await supabaseAdmin
      .from('file_annotations')
      .delete()
      .eq('file_url', fileUrl)
      .eq('reviewer_id', user.user.id);

    // Insert new annotations
    const { data: insertedAnnotations, error: insertError } = await supabaseAdmin
      .from('file_annotations')
      .insert(annotationsToInsert)
      .select();

    if (insertError) {
      console.error('Database error:', insertError);
      return NextResponse.json({ error: 'Failed to save annotations' }, { status: 500 });
    }

    console.log('✅ Bulk saved annotations:', insertedAnnotations?.length || 0, 'annotations');

    return NextResponse.json({
      success: true,
      message: `Successfully saved ${insertedAnnotations?.length || 0} annotations`,
      annotations: insertedAnnotations
    });

  } catch (error) {
    console.error('Bulk save error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
