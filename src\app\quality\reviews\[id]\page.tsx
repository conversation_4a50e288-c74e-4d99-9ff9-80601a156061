"use client";

import React, { useState, useEffect } from "react";
import { useOptimizedAuth } from "@/hooks/useOptimizedAuth";
import { supabase } from "@/lib/supabase";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import {
  CheckCircle,
  XCircle,
  ArrowLeft,
  Star,
  FileText,
  User,
  Calendar,
  AlertTriangle,
  MessageSquare,
  Save,
  Send,
  RefreshCw
} from "lucide-react";
import FileAnnotationTool from "@/components/quality/FileAnnotationTool";

interface QualityStandard {
  id: string;
  title: string;
  description: string;
  category: string;
  weight: number;
  criteria: string[];
}

interface QualityReview {
  id: string;
  project_id: string;
  designer_id: string;
  review_type: string;
  status: 'pending' | 'in_review' | 'approved' | 'needs_revision' | 'rejected';
  overall_score?: number;
  feedback?: string;
  revision_notes?: string;
  created_at: string;
  reviewed_at?: string;
  submission_id?: string;
  project: {
    title: string;
    description: string;
    budget: number;
    client: {
      full_name: string;
    };
  };
  designer: {
    full_name: string;
    email: string;
  };
}

interface QualityFeedback {
  standard_id: string;
  passed: boolean;
  score: number;
  comments: string;
  suggestions: string;
}

interface AnnotationFile {
  id: string;
  file_url: string;
  file_name: string;
  file_type: 'image' | 'document' | 'cad';
  project_title: string;
  review_id: string;
  annotations_count: number;
  created_at: string;
}

// Helper function to determine file type from URL
const getFileTypeFromUrl = (url: string): 'image' | 'document' | 'cad' => {
  const extension = url.split('.').pop()?.toLowerCase() || '';
  
  if (['jpg', 'jpeg', 'png', 'gif', 'webp'].includes(extension)) {
    return 'image';
  } else if (['dwg', 'dxf', 'step', 'iges'].includes(extension)) {
    return 'cad';
  } else {
    return 'document';
  }
};

export default function QualityReviewPage({ params }: { params: Promise<{ id: string }> }) {
  const { user, profile } = useOptimizedAuth();
  const resolvedParams = React.use(params);
  const [review, setReview] = useState<QualityReview | null>(null);
  const [standards, setStandards] = useState<QualityStandard[]>([]);
  const [feedback, setFeedback] = useState<Record<string, QualityFeedback>>({});
  const [overallScore, setOverallScore] = useState<number>(3);
  const [generalFeedback, setGeneralFeedback] = useState<string>('');
  const [revisionNotes, setRevisionNotes] = useState<string>('');
  const [loading, setLoading] = useState(true);
  const [submitting, setSubmitting] = useState(false);
  const [activeTab, setActiveTab] = useState("standards");
  const [files, setFiles] = useState<AnnotationFile[]>([]);
  const [selectedFile, setSelectedFile] = useState<AnnotationFile | null>(null);
  const [loadingFiles, setLoadingFiles] = useState(false);

  useEffect(() => {
    if (user && profile?.role === 'quality_team') {
      fetchReview();
      fetchStandards();
    }
  }, [user, profile, resolvedParams.id]);

  // Fetch files when review is loaded
  useEffect(() => {
    if (review && activeTab === 'files') {
      fetchAnnotationFiles();
    }
  }, [review, activeTab]);

  const fetchReview = async () => {
    try {
      console.log('🔍 [QUALITY REVIEW] Fetching review:', resolvedParams.id);

      // First, check if the review exists in quality_reviews_new
      const { data, error } = await supabase
        .from('quality_reviews_new')
        .select(`
          *,
          project:projects(title, description, budget, client:profiles!client_id(full_name)),
          designer:profiles!quality_reviews_new_designer_id_fkey(full_name, email)
        `)
        .eq('id', resolvedParams.id)
        .maybeSingle(); // Use maybeSingle() instead of single() to handle no results gracefully

      console.log('🔍 [QUALITY REVIEW] Query result:', { data, error });

      if (error) {
        console.error('❌ [QUALITY REVIEW] Database error:', error);
        throw error;
      }

      if (!data) {
        console.error('❌ [QUALITY REVIEW] Review not found with ID:', resolvedParams.id);
        throw new Error(`Quality review not found with ID: ${resolvedParams.id}`);
      }

      console.log('✅ [QUALITY REVIEW] Review found:', data.id);
      setReview(data);

      if (data.feedback) setGeneralFeedback(data.feedback);
      if (data.revision_notes) setRevisionNotes(data.revision_notes);
      if (data.overall_score) setOverallScore(data.overall_score);

      // Fetch existing feedback
      const { data: existingFeedback } = await supabase
        .from('quality_feedback')
        .select('*')
        .eq('review_id', resolvedParams.id);

      if (existingFeedback) {
        const feedbackMap: Record<string, QualityFeedback> = {};
        existingFeedback.forEach(item => {
          feedbackMap[item.standard_id] = {
            standard_id: item.standard_id,
            passed: item.passed,
            score: item.score,
            comments: item.comments || '',
            suggestions: item.suggestions || ''
          };
        });
        setFeedback(feedbackMap);
      }
    } catch (error) {
      console.error('❌ [QUALITY REVIEW] Error fetching review:', error);

      // Set a user-friendly error message
      if (error instanceof Error) {
        if (error.message.includes('not found')) {
          console.error('Review not found - this review may not exist in quality_reviews_new table');
          // Optionally redirect to quality dashboard
          // window.location.href = '/quality/dashboard';
        }
      }

      setLoading(false);
    }
  };

  const fetchStandards = async () => {
    try {
      const { data, error } = await supabase
        .from('quality_standards')
        .select('*')
        .order('category', { ascending: true })
        .order('weight', { ascending: false });

      if (error) throw error;

      // Parse criteria JSON
      const parsedData = (data || []).map(standard => {
        let criteria = [];
        try {
          if (typeof standard.criteria === 'string') {
            criteria = JSON.parse(standard.criteria);
          } else if (Array.isArray(standard.criteria)) {
            criteria = standard.criteria;
          }
          
          // Ensure criteria are strings or objects with proper structure
          criteria = criteria.map((item: any) => {
            if (typeof item === 'string') {
              return item;
            } else if (typeof item === 'object' && item.criterion) {
              return item.criterion; // Extract the criterion string from object
            } else {
              return String(item); // Convert to string as fallback
            }
          });
        } catch (e) {
          console.error('Error parsing criteria for standard:', standard.id, e);
          criteria = [];
        }

        return {
          ...standard,
          criteria
        };
      });

      setStandards(parsedData);
    } catch (error) {
      console.error('Error fetching standards:', error);
    } finally {
      setLoading(false);
    }
  };

  const fetchAnnotationFiles = async () => {
    if (!review) return;

    setLoadingFiles(true);
    try {
      console.log('🔍 [FILE ANNOTATIONS] Fetching files for review:', {
        reviewId: review.id,
        submissionId: review.submission_id,
        projectId: review.project_id
      });

      const annotationFiles: AnnotationFile[] = [];

      // The relationship is: quality_reviews_new.submission_id -> project_submissions.id
      // So we need to fetch submissions using the submission_id from the review
      if (review.submission_id) {
        const { data: submissions, error: submissionsError } = await supabase
          .from('project_submissions')
          .select(`
            id,
            description,
            project_id,
            files,
            submitted_at,
            status,
            projects!inner(
              title,
              client_id,
              profiles!client_id(full_name)
            )
          `)
          .eq('id', review.submission_id)
          .order('submitted_at', { ascending: false });

        if (submissionsError) {
          console.error('❌ [FILE ANNOTATIONS] Error fetching submissions:', submissionsError);
          throw submissionsError;
        }

        console.log('✅ [FILE ANNOTATIONS] Submissions fetched:', submissions?.length || 0);

        // Process submissions and extract files
        submissions?.forEach(submission => {
          if (submission.files && Array.isArray(submission.files)) {
            console.log(`🔍 [FILE ANNOTATIONS] Processing ${submission.files.length} files from submission ${submission.id}`);
            console.log(`🔍 [FILE ANNOTATIONS] Files data:`, submission.files);

            submission.files.forEach((file: any, index: number) => {
              // Use the actual property names from your JSONB data
              const fileName = file.file_name || file.name || file.fileName || file.filename || `file-${index}`;
              
              // Get file URL - your data already has file_url
              let fileUrl = file.file_url;
              
              // If file_url is a proxy URL, use it as is
              // Otherwise, try to construct the full URL
              if (!fileUrl) {
                if (file.url) {
                  fileUrl = file.url;
                } else if (file.file_path) {
                  // Construct URL from file_path
                  const r2BaseUrl = process.env.NEXT_PUBLIC_CLOUDFLARE_R2_PUBLIC_URL;
                  if (r2BaseUrl) {
                    fileUrl = `${r2BaseUrl}/${file.file_path}`;
                  } else {
                    // Use the proxy API if no direct R2 URL is configured
                    fileUrl = `/api/r2-proxy?bucket=project-files&key=${encodeURIComponent(file.file_path)}`;
                  }
                } else if (file.key) {
                  const r2BaseUrl = process.env.NEXT_PUBLIC_CLOUDFLARE_R2_PUBLIC_URL;
                  if (r2BaseUrl) {
                    fileUrl = `${r2BaseUrl}/${file.key}`;
                  } else {
                    fileUrl = `/api/r2-proxy?bucket=project-files&key=${encodeURIComponent(file.key)}`;
                  }
                }
              }

              console.log(`🔍 [FILE ANNOTATIONS] File ${index}:`, {
                fileName,
                fileUrl,
                file_type: file.file_type,
                file_size: file.file_size,
                originalFile: file
              });

              if (fileUrl && fileName) {
                const fileExtension = fileName.split('.').pop()?.toLowerCase() || '';
                const isAnnotatable = ['jpg', 'jpeg', 'png', 'gif', 'pdf', 'dwg', 'dxf'].includes(fileExtension);

                console.log(`🔍 [FILE ANNOTATIONS] File ${fileName}: extension=${fileExtension}, annotatable=${isAnnotatable}`);

                if (isAnnotatable) {
                  annotationFiles.push({
                    id: `submission-${submission.id}-${index}`,
                    file_url: fileUrl,
                    file_name: fileName,
                    file_type: getFileTypeFromUrl(fileUrl),
                    project_title: (submission.projects as any)?.title || 'Unknown Project',
                    review_id: review.id,
                    annotations_count: 0, // TODO: Count from annotations table
                    created_at: submission.submitted_at
                  });
                  console.log(`✅ [FILE ANNOTATIONS] Added annotatable file: ${fileName}`);
                } else {
                  console.log(`⚠️ [FILE ANNOTATIONS] Skipped non-annotatable file: ${fileName} (${fileExtension})`);
                }
              } else {
                console.warn(`⚠️ [FILE ANNOTATIONS] Missing fileUrl or fileName for file ${index}:`, { fileUrl, fileName });
              }
            });
          } else {
            console.log(`⚠️ [FILE ANNOTATIONS] No files array found for submission ${submission.id}`);
          }
        });
      } else {
        // Fallback: If no submission_id, try to find submissions by project_id
        console.log('⚠️ [FILE ANNOTATIONS] No submission_id in review, trying project_id fallback');
        
        const { data: submissions, error: submissionsError } = await supabase
          .from('project_submissions')
          .select(`
            id,
            description,
            project_id,
            files,
            submitted_at,
            status,
            projects!inner(
              title,
              client_id,
              profiles!client_id(full_name)
            )
          `)
          .eq('project_id', review.project_id)
          .order('submitted_at', { ascending: false })
          .limit(10); // Limit to recent submissions

        if (submissionsError) {
          console.error('❌ [FILE ANNOTATIONS] Error fetching submissions by project:', submissionsError);
          throw submissionsError;
        }

        console.log('✅ [FILE ANNOTATIONS] Fallback submissions fetched:', submissions?.length || 0);

        // Process submissions and extract files (similar logic as above)
        submissions?.forEach(submission => {
          if (submission.files && Array.isArray(submission.files)) {
            console.log(`🔍 [FILE ANNOTATIONS] Fallback - Processing ${submission.files.length} files from submission ${submission.id}`);
            
            submission.files.forEach((file: any, index: number) => {
              // Use the actual property names from your JSONB data
              const fileName = file.file_name || file.name || file.fileName || file.filename || `file-${index}`;
              
              // Get file URL - your data already has file_url
              let fileUrl = file.file_url;
              
              // If file_url is a proxy URL, use it as is
              // Otherwise, try to construct the full URL
              if (!fileUrl) {
                if (file.url) {
                  fileUrl = file.url;
                } else if (file.file_path) {
                  // Construct URL from file_path
                  const r2BaseUrl = process.env.NEXT_PUBLIC_CLOUDFLARE_R2_PUBLIC_URL;
                  if (r2BaseUrl) {
                    fileUrl = `${r2BaseUrl}/${file.file_path}`;
                  } else {
                    // Use the proxy API if no direct R2 URL is configured
                    fileUrl = `/api/r2-proxy?bucket=project-files&key=${encodeURIComponent(file.file_path)}`;
                  }
                } else if (file.key) {
                  const r2BaseUrl = process.env.NEXT_PUBLIC_CLOUDFLARE_R2_PUBLIC_URL;
                  if (r2BaseUrl) {
                    fileUrl = `${r2BaseUrl}/${file.key}`;
                  } else {
                    fileUrl = `/api/r2-proxy?bucket=project-files&key=${encodeURIComponent(file.key)}`;
                  }
                }
              }

              if (fileUrl && fileName) {
                const fileExtension = fileName.split('.').pop()?.toLowerCase() || '';
                const isAnnotatable = ['jpg', 'jpeg', 'png', 'gif', 'pdf', 'dwg', 'dxf'].includes(fileExtension);

                if (isAnnotatable) {
                  annotationFiles.push({
                    id: `submission-${submission.id}-${index}`,
                    file_url: fileUrl,
                    file_name: fileName,
                    file_type: getFileTypeFromUrl(fileUrl),
                    project_title: (submission.projects as any)?.title || 'Unknown Project',
                    review_id: review.id,
                    annotations_count: 0,
                    created_at: submission.submitted_at
                  });
                }
              }
            });
          }
        });
      }

      console.log('✅ [FILE ANNOTATIONS] Total annotation files found:', annotationFiles.length);
      setFiles(annotationFiles);

      // Select first file if available
      if (annotationFiles.length > 0 && !selectedFile) {
        setSelectedFile(annotationFiles[0]);
      }

    } catch (error) {
      console.error('❌ [FILE ANNOTATIONS] Error fetching annotation files:', error);
    } finally {
      setLoadingFiles(false);
    }
  };

  const updateFeedback = (standardId: string, updates: Partial<QualityFeedback>) => {
    setFeedback(prev => ({
      ...prev,
      [standardId]: {
        ...prev[standardId],
        standard_id: standardId,
        passed: prev[standardId]?.passed ?? true,
        score: prev[standardId]?.score ?? 3,
        comments: prev[standardId]?.comments ?? '',
        suggestions: prev[standardId]?.suggestions ?? '',
        ...updates
      }
    }));
  };

  const saveReview = async (status: 'approved' | 'rejected' | 'needs_revision') => {
    if (!review) return;
    
    setSubmitting(true);
    try {
      // Update review
      const { error: reviewError } = await supabase
        .from('quality_reviews_new')
        .update({
          status,
          overall_score: overallScore,
          feedback: generalFeedback,
          revision_notes: revisionNotes,
          reviewed_at: new Date().toISOString(),
          reviewer_id: user?.id,
        })
        .eq('id', review.id);

      if (reviewError) throw reviewError;

      // Save individual feedback
      for (const [standardId, feedbackData] of Object.entries(feedback)) {
        const { error: feedbackError } = await supabase
          .from('quality_feedback')
          .upsert({
            review_id: review.id,
            standard_id: standardId,
            passed: feedbackData.passed,
            score: feedbackData.score,
            comments: feedbackData.comments,
            suggestions: feedbackData.suggestions,
          }, {
            onConflict: 'review_id,standard_id'
          });

        if (feedbackError) {
          console.error('Error saving feedback for standard:', standardId, feedbackError);
        }
      }

      // Navigate back to dashboard
      window.location.href = '/quality/dashboard';

    } catch (error) {
      console.error('Error saving review:', error);
      alert('Error saving review. Please try again.');
    } finally {
      setSubmitting(false);
    }
  };

  const calculateOverallProgress = () => {
    const totalStandards = standards.length;
    const reviewedStandards = Object.keys(feedback).length;
    return totalStandards > 0 ? (reviewedStandards / totalStandards) * 100 : 0;
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <RefreshCw className="h-8 w-8 animate-spin text-brown-600" />
      </div>
    );
  }

  if (!review) {
    return (
      <div className="max-w-4xl mx-auto p-6">
        <div className="text-center">
          <AlertTriangle className="h-12 w-12 text-red-500 mx-auto mb-4" />
          <h1 className="text-2xl font-bold text-gray-900 mb-2">Review Not Found</h1>
          <p className="text-gray-600 mb-4">The quality review you're looking for doesn't exist.</p>
          <Button onClick={() => window.location.href = '/quality/dashboard'}>
            Back to Dashboard
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-6xl mx-auto p-6 space-y-8">
      {/* Header */}
      <div className="flex items-center gap-4">
        <Button
          variant="outline"
          onClick={() => window.location.href = '/quality/dashboard'}
          className="flex items-center gap-2"
        >
          <ArrowLeft className="h-4 w-4" />
          Back
        </Button>
        <div className="flex-1">
          <h1 className="text-3xl font-bold text-gray-900">Quality Review</h1>
          <p className="text-gray-600 mt-1">Review submission for quality standards compliance</p>
        </div>
      </div>

      {/* Review Info */}
      <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-200">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <div>
            <h2 className="text-xl font-semibold text-gray-900 mb-4">Project Information</h2>
            <div className="space-y-3">
              <div className="flex items-center gap-2">
                <FileText className="h-4 w-4 text-gray-400" />
                <span className="font-medium">Project:</span> {review.project.title}
              </div>
              <div className="flex items-center gap-2">
                <User className="h-4 w-4 text-gray-400" />
                <span className="font-medium">Client:</span> {review.project.client.full_name}
              </div>
              <div className="flex items-center gap-2">
                <User className="h-4 w-4 text-gray-400" />
                <span className="font-medium">Designer:</span> {review.designer.full_name}
              </div>
              <div className="flex items-center gap-2">
                <Calendar className="h-4 w-4 text-gray-400" />
                <span className="font-medium">Submitted:</span> {new Date(review.created_at).toLocaleDateString()}
              </div>
            </div>
          </div>
          <div>
            <div className="bg-gray-50 rounded-lg p-4">
              <h4 className="font-medium text-gray-900 mb-2">Review Progress</h4>
              <div className="w-full bg-gray-200 rounded-full h-2 mb-2">
                <div 
                  className="bg-brown-600 h-2 rounded-full transition-all duration-300"
                  style={{ width: `${calculateOverallProgress()}%` }}
                ></div>
              </div>
              <p className="text-sm text-gray-600">
                {Object.keys(feedback).length} of {standards.length} standards reviewed
              </p>
            </div>
          </div>
        </div>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="standards">Quality Standards</TabsTrigger>
          <TabsTrigger value="files">Files & Annotations</TabsTrigger>
          <TabsTrigger value="review">Overall Review</TabsTrigger>
        </TabsList>

        <TabsContent value="standards">
          {/* Quality Standards Review */}
          <div className="space-y-6">
            {standards.map((standard) => (
              <div key={standard.id} className="bg-white rounded-xl p-6 shadow-sm border border-gray-200">
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900 mb-2">{standard.title}</h3>
                    <p className="text-gray-600 mb-4">{standard.description}</p>
                    
                    {standard.criteria && standard.criteria.length > 0 && (
                      <div>
                        <h4 className="font-medium text-gray-900 mb-2">Criteria:</h4>
                        <ul className="list-disc list-inside space-y-1">
                          {standard.criteria.map((criterion, index) => (
                            <li key={index} className="text-gray-600 text-sm">{criterion}</li>
                          ))}
                        </ul>
                      </div>
                    )}

                    <div className="mt-4">
                      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                        standard.category === 'critical' ? 'bg-red-100 text-red-800' :
                        standard.category === 'major' ? 'bg-orange-100 text-orange-800' :
                        'bg-blue-100 text-blue-800'
                      }`}>
                        {standard.category.toUpperCase()} - Weight: {standard.weight}
                      </span>
                    </div>

                    <div className="mt-4">
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Pass/Fail
                      </label>
                      <div className="flex gap-2">
                        <Button
                          variant={feedback[standard.id]?.passed === true ? "default" : "outline"}
                          size="sm"
                          onClick={() => updateFeedback(standard.id, { passed: true })}
                          className="flex items-center gap-2"
                        >
                          <CheckCircle className="h-4 w-4" />
                          Pass
                        </Button>
                        <Button
                          variant={feedback[standard.id]?.passed === false ? "default" : "outline"}
                          size="sm"
                          onClick={() => updateFeedback(standard.id, { passed: false })}
                          className="flex items-center gap-2"
                        >
                          <XCircle className="h-4 w-4" />
                          Fail
                        </Button>
                      </div>

                      <label className="block text-sm font-medium text-gray-700 mt-4 mb-2">
                        Score (1-5)
                      </label>
                      <div className="flex gap-2">
                        {[1, 2, 3, 4, 5].map((score) => (
                          <Button
                            key={score}
                            variant={feedback[standard.id]?.score === score ? "default" : "outline"}
                            size="sm"
                            onClick={() => updateFeedback(standard.id, { score })}
                            className="w-10 h-10 p-0"
                          >
                            {score}
                          </Button>
                        ))}
                      </div>
                    </div>
                  </div>

                  <div className="space-y-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Comments
                      </label>
                      <textarea
                        value={feedback[standard.id]?.comments || ''}
                        onChange={(e) => updateFeedback(standard.id, { comments: e.target.value })}
                        placeholder="Add specific feedback about this standard..."
                        className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-brown-500 focus:border-brown-500"
                        rows={3}
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Suggestions for Improvement
                      </label>
                      <textarea
                        value={feedback[standard.id]?.suggestions || ''}
                        onChange={(e) => updateFeedback(standard.id, { suggestions: e.target.value })}
                        placeholder="Suggest specific improvements..."
                        className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-brown-500 focus:border-brown-500"
                        rows={2}
                      />
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </TabsContent>

        <TabsContent value="files">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <FileText className="h-5 w-5" />
                Files & Annotations
              </CardTitle>
            </CardHeader>
            <CardContent>
              {loadingFiles ? (
                <div className="flex items-center justify-center py-8">
                  <RefreshCw className="h-6 w-6 animate-spin text-brown-600 mr-2" />
                  Loading files...
                </div>
              ) : files.length === 0 ? (
                <div className="text-center py-8">
                  <FileText className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <p className="text-gray-500">No files available for annotation</p>
                  <p className="text-sm text-gray-400 mt-2">
                    Files from the project submission will appear here for review and annotation.
                  </p>
                </div>
              ) : (
                <div className="space-y-4">
                  {/* File Selector */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Select file to review:
                    </label>
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                      {files.map((file) => (
                        <Button
                          key={file.id}
                          variant={selectedFile?.id === file.id ? "default" : "outline"}
                          onClick={() => setSelectedFile(file)}
                          className="p-4 h-auto flex flex-col items-start gap-2"
                        >
                          <div className="flex items-center gap-2 w-full">
                            <FileText className="h-4 w-4 shrink-0" />
                            <span className="text-sm font-medium truncate">{file.file_name}</span>
                          </div>
                          <span className="text-xs text-gray-500">{file.file_type}</span>
                        </Button>
                      ))}
                    </div>
                  </div>

                  {/* File Annotation Tool */}
                  {selectedFile && (
                    <div>
                      <FileAnnotationTool
                        fileUrl={selectedFile.file_url}
                        fileName={selectedFile.file_name}
                        fileType={selectedFile.file_type}
                        qualityReviewId={review.id}
                        submissionId={review.submission_id}
                        submissionType="project"
                        reviewerName={profile?.full_name || 'Quality Reviewer'}
                        onSaveAnnotations={(annotations: any) => {
                          console.log('Annotations saved:', annotations);
                        }}
                      />
                    </div>
                  )}
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="review">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Star className="h-5 w-5" />
                Overall Review
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* Review Progress Card */}
              <div className="bg-gray-50 rounded-lg p-6">
                <h3 className="font-medium text-gray-900 mb-4">Review Progress</h3>
                <div className="w-full bg-gray-200 rounded-full h-3 mb-3">
                  <div 
                    className="bg-brown-600 h-3 rounded-full transition-all duration-300"
                    style={{ width: `${calculateOverallProgress()}%` }}
                  ></div>
                </div>
                <p className="text-sm text-gray-600">
                  {Object.keys(feedback).length} of {standards.length} standards reviewed ({Math.round(calculateOverallProgress())}% complete)
                </p>
              </div>

              {/* Overall Review Form */}
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <div className="space-y-6">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-3">
                      Overall Score (1-5)
                    </label>
                    <div className="flex gap-2">
                      {[1, 2, 3, 4, 5].map((score) => (
                        <Button
                          key={score}
                          variant={overallScore === score ? "default" : "outline"}
                          size="lg"
                          onClick={() => setOverallScore(score)}
                          className="w-14 h-14 p-0 flex items-center justify-center"
                        >
                          <Star className={`h-6 w-6 ${overallScore >= score ? 'fill-current' : ''}`} />
                        </Button>
                      ))}
                    </div>
                    <p className="text-sm text-gray-500 mt-2">
                      Current score: {overallScore}/5
                    </p>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      General Feedback
                    </label>
                    <textarea
                      value={generalFeedback}
                      onChange={(e) => setGeneralFeedback(e.target.value)}
                      placeholder="Provide overall feedback about the submission..."
                      className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-brown-500 focus:border-brown-500"
                      rows={5}
                    />
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Revision Notes (if applicable)
                  </label>
                  <textarea
                    value={revisionNotes}
                    onChange={(e) => setRevisionNotes(e.target.value)}
                    placeholder="Specific notes for revision requirements..."
                    className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-brown-500 focus:border-brown-500"
                    rows={8}
                  />
                  <p className="text-sm text-gray-500 mt-2">
                    These notes will be sent to the designer if revision is requested.
                  </p>
                </div>
              </div>

              {/* Action Buttons */}
              <div className="border-t pt-6">
                <div className="flex flex-col sm:flex-row gap-4 justify-end">
                  <Button
                    variant="outline"
                    onClick={() => saveReview('needs_revision')}
                    disabled={submitting}
                    className="flex items-center gap-2 border-orange-200 text-orange-600 hover:bg-orange-50"
                  >
                    {submitting ? (
                      <RefreshCw className="h-4 w-4 animate-spin" />
                    ) : (
                      <MessageSquare className="h-4 w-4" />
                    )}
                    Request Revision
                  </Button>
                  
                  <Button
                    variant="outline"
                    onClick={() => saveReview('rejected')}
                    disabled={submitting}
                    className="flex items-center gap-2 border-red-200 text-red-600 hover:bg-red-50"
                  >
                    {submitting ? (
                      <RefreshCw className="h-4 w-4 animate-spin" />
                    ) : (
                      <XCircle className="h-4 w-4" />
                    )}
                    Reject Submission
                  </Button>
                  
                  <Button
                    onClick={() => saveReview('approved')}
                    disabled={submitting}
                    className="flex items-center gap-2 bg-green-600 hover:bg-green-700"
                  >
                    {submitting ? (
                      <RefreshCw className="h-4 w-4 animate-spin" />
                    ) : (
                      <CheckCircle className="h-4 w-4" />
                    )}
                    Approve Submission
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
