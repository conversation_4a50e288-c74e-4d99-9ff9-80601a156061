-- Fix the database trigger that's causing the priority constraint violation
-- This trigger is automatically creating quality reviews with 'medium' priority
-- which violates the constraint that only allows ('low', 'normal', 'high', 'urgent')

-- Option 1: Drop the problematic trigger (recommended)
DROP TRIGGER IF EXISTS trigger_create_quality_review ON project_submissions;

-- Option 2: Fix the trigger function to use 'normal' instead of 'medium'
CREATE OR REPLACE FUNCTION create_quality_review_on_submission()
RETURNS TRIGGER AS $$
BEGIN
    -- Only create quality review for submitted status
    IF NEW.status = 'submitted' AND (OLD.status IS NULL OR OLD.status != 'submitted') THEN
        INSERT INTO quality_reviews_new (
            project_id,
            submission_id,
            milestone_id,
            designer_id,
            review_type,
            status,
            priority,
            created_at
        ) VALUES (
            NEW.project_id,
            NEW.id,
            NEW.milestone_id,
            NEW.designer_id,
            'submission',
            'pending',
            'normal',  -- ✅ Fixed: Use 'normal' instead of 'medium'
            NOW()
        );
        
        -- Update submission with quality review status
        NEW.status = 'under_quality_review';
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Recreate the trigger with the fixed function (optional - only if you want automatic quality review creation)
-- CREATE TRIGGER trigger_create_quality_review
--     BEFORE INSERT OR UPDATE ON project_submissions
--     FOR EACH ROW
--     EXECUTE FUNCTION create_quality_review_on_submission();

-- Also fix any other triggers that might use 'medium' priority
-- Update the auto-assignment trigger to use 'normal' instead of 'medium'
CREATE OR REPLACE FUNCTION trigger_auto_assign_review()
RETURNS TRIGGER AS $$
BEGIN
  -- Only auto-assign if no reviewer is already assigned
  IF NEW.reviewer_id IS NULL AND NEW.status = 'pending' THEN
    PERFORM auto_assign_quality_review(
      NEW.id,
      NEW.review_type,
      'normal', -- ✅ Fixed: Use 'normal' instead of 'medium'
      NEW.priority
    );
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Check if there are any existing quality reviews with 'medium' priority and fix them
UPDATE quality_reviews_new 
SET priority = 'normal' 
WHERE priority = 'medium';

-- Verify the fix
SELECT priority, COUNT(*) 
FROM quality_reviews_new 
GROUP BY priority;
