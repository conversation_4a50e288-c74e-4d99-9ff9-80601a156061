# Quality Reviews Fixes Summary

## Issues Fixed

### 1. Reviews List Page - Project Titles Showing as "Unknown"

**Problem**: The QualityReview interface in `/src/app/quality/reviews/page.tsx` was expecting a `project` property, but the API was returning `projects` (with the PostgreSQL foreign key relationship structure).

**Solution**: 
- Updated the `QualityReview` interface to match the API response structure:
  - Changed `project` to `projects`
  - Updated nested client reference from `project.client` to `projects.profiles`
- Updated all template references to use the correct property names
- Fixed the search filter to use the new property structure

**Files Changed**:
- `/src/app/quality/reviews/page.tsx`

### 2. File Annotations Not Loading in Individual Review Page

**Problem**: The `fetchAnnotationFiles` function was using an incorrect database relationship query. It was trying to use a non-existent foreign key relationship `project_submissions_quality_review_id_fkey`.

**Solution**: 
- Updated the query to use the correct relationship: `quality_reviews_new.submission_id -> project_submissions.id`
- Added fallback logic to fetch files by `project_id` if no `submission_id` exists
- Added proper error handling and logging
- Updated the `QualityReview` interface to include the `submission_id` property

**Files Changed**:
- `/src/app/quality/reviews/[id]/page.tsx`

## Database Relationships Clarified

The correct relationships are:
1. **Primary**: `quality_reviews_new.submission_id` → `project_submissions.id` (one-to-one)
2. **Fallback**: `quality_reviews_new.project_id` → `project_submissions.project_id` (one-to-many)

## API Structure

The quality reviews API returns data in this structure:
```typescript
{
  id: string;
  projects: {
    title: string;
    profiles: {
      full_name: string;
    };
  } | null;
  designer: {
    full_name: string;
    email: string;
  } | null;
  submission_id?: string;
  // ... other fields
}
```

## Testing Recommendations

1. **Test Reviews List**: Verify that project titles now display correctly instead of "Unknown Project"
2. **Test File Annotations**: 
   - Navigate to an individual review page
   - Switch to the "Files & Annotations" tab
   - Verify that files are loaded from the related submission
   - Test the annotation functionality

## Debug Script

Created `debug-quality-reviews.sql` to help investigate data relationships and identify any orphaned reviews or missing submissions.

## Next Steps

1. Run the debug script to verify data integrity
2. Test the fixes in the application
3. Ensure that the FileAnnotationTool component works correctly with the loaded files
4. Consider adding better error handling for cases where submissions have no files

## Notes

- The FileAnnotationTool component appears to be well-implemented and should work correctly once files are properly fetched
- Added proper TypeScript types to handle nullable relationships
- Included fallback mechanisms for edge cases where data relationships might be incomplete
  };
  } | null;
  designer: {
    full_name: string;
    email: string;
  } | null;
  submission_id?: string;
  // ... other fields
}
```

## Testing Recommendations

1. **Test Reviews List**: Verify that project titles now display correctly instead of "Unknown Project"
2. **Test File Annotations**: 
   - Navigate to an individual review page
   - Switch to the "Files & Annotations" tab
   - Verify that files are loaded from the related submission
   - Test the annotation functionality

## Debug Script

Created `debug-quality-reviews.sql` to help investigate data relationships and identify any orphaned reviews or missing submissions.

## Next Steps

1. Run the debug script to verify data integrity
2. Test the fixes in the application
3. Ensure that the FileAnnotationTool component works correctly with the loaded files
4. Consider adding better error handling for cases where submissions have no files

## Notes

- The FileAnnotationTool component appears to be well-implemented and should work correctly once files are properly fetched
- Added proper TypeScript types to handle nullable relationships
- Included fallback mechanisms for edge cases where data relationships might be incomplete
  };
  } | null;
  designer: {
    full_name: string;
    email: string;
  } | null;
  submission_id?: string;
  // ... other fields
}
```

## Testing Recommendations

1. **Test Reviews List**: Verify that project titles now display correctly instead of "Unknown Project"
2. **Test File Annotations**: 
   - Navigate to an individual review page
   - Switch to the "Files & Annotations" tab
   - Verify that files are loaded from the related submission
   - Test the annotation functionality

## Debug Script

Created `debug-quality-reviews.sql` to help investigate data relationships and identify any orphaned reviews or missing submissions.

## Next Steps

1. Run the debug script to verify data integrity
2. Test the fixes in the application
3. Ensure that the FileAnnotationTool component works correctly with the loaded files
4. Consider adding better error handling for cases where submissions have no files

## Notes

- The FileAnnotationTool component appears to be well-implemented and should work correctly once files are properly fetched
- Added proper TypeScript types to handle nullable relationships
- Included fallback mechanisms for edge cases where data relationships might be incomplete
  };
  } | null;
  designer: {
    full_name: string;
    email: string;
  } | null;
  submission_id?: string;
  // ... other fields
}
```

## Testing Recommendations

1. **Test Reviews List**: Verify that project titles now display correctly instead of "Unknown Project"
2. **Test File Annotations**: 
   - Navigate to an individual review page
   - Switch to the "Files & Annotations" tab
   - Verify that files are loaded from the related submission
   - Test the annotation functionality

## Debug Script

Created `debug-quality-reviews.sql` to help investigate data relationships and identify any orphaned reviews or missing submissions.

## Next Steps

1. Run the debug script to verify data integrity
2. Test the fixes in the application
3. Ensure that the FileAnnotationTool component works correctly with the loaded files
4. Consider adding better error handling for cases where submissions have no files

## Notes

- The FileAnnotationTool component appears to be well-implemented and should work correctly once files are properly fetched
- Added proper TypeScript types to handle nullable relationships
- Included fallback mechanisms for edge cases where data relationships might be incomplete
  };
  } | null;
  designer: {
    full_name: string;
    email: string;
  } | null;
  submission_id?: string;
  // ... other fields
}
```

## Testing Recommendations

1. **Test Reviews List**: Verify that project titles now display correctly instead of "Unknown Project"
2. **Test File Annotations**: 
   - Navigate to an individual review page
   - Switch to the "Files & Annotations" tab
   - Verify that files are loaded from the related submission
   - Test the annotation functionality

## Debug Script

Created `debug-quality-reviews.sql` to help investigate data relationships and identify any orphaned reviews or missing submissions.

## Next Steps

1. Run the debug script to verify data integrity
2. Test the fixes in the application
3. Ensure that the FileAnnotationTool component works correctly with the loaded files
4. Consider adding better error handling for cases where submissions have no files

## Notes

- The FileAnnotationTool component appears to be well-implemented and should work correctly once files are properly fetched
- Added proper TypeScript types to handle nullable relationships
- Included fallback mechanisms for edge cases where data relationships might be incomplete
  };
  } | null;
  designer: {
    full_name: string;
    email: string;
  } | null;
  submission_id?: string;
  // ... other fields
}
```

## Testing Recommendations

1. **Test Reviews List**: Verify that project titles now display correctly instead of "Unknown Project"
2. **Test File Annotations**: 
   - Navigate to an individual review page
   - Switch to the "Files & Annotations" tab
   - Verify that files are loaded from the related submission
   - Test the annotation functionality

## Debug Script

Created `debug-quality-reviews.sql` to help investigate data relationships and identify any orphaned reviews or missing submissions.

## Next Steps

1. Run the debug script to verify data integrity
2. Test the fixes in the application
3. Ensure that the FileAnnotationTool component works correctly with the loaded files
4. Consider adding better error handling for cases where submissions have no files

## Notes

- The FileAnnotationTool component appears to be well-implemented and should work correctly once files are properly fetched
- Added proper TypeScript types to handle nullable relationships
- Included fallback mechanisms for edge cases where data relationships might be incomplete
  };
  } | null;
  designer: {
    full_name: string;
    email: string;
  } | null;
  submission_id?: string;
  // ... other fields
}
```

## Testing Recommendations

1. **Test Reviews List**: Verify that project titles now display correctly instead of "Unknown Project"
2. **Test File Annotations**: 
   - Navigate to an individual review page
   - Switch to the "Files & Annotations" tab
   - Verify that files are loaded from the related submission
   - Test the annotation functionality

## Debug Script

Created `debug-quality-reviews.sql` to help investigate data relationships and identify any orphaned reviews or missing submissions.

## Next Steps

1. Run the debug script to verify data integrity
2. Test the fixes in the application
3. Ensure that the FileAnnotationTool component works correctly with the loaded files
4. Consider adding better error handling for cases where submissions have no files

## Notes

- The FileAnnotationTool component appears to be well-implemented and should work correctly once files are properly fetched
- Added proper TypeScript types to handle nullable relationships
- Included fallback mechanisms for edge cases where data relationships might be incomplete
  };
  } | null;
  designer: {
    full_name: string;
    email: string;
  } | null;
  submission_id?: string;
  // ... other fields
}
```

## Testing Recommendations

1. **Test Reviews List**: Verify that project titles now display correctly instead of "Unknown Project"
2. **Test File Annotations**: 
   - Navigate to an individual review page
   - Switch to the "Files & Annotations" tab
   - Verify that files are loaded from the related submission
   - Test the annotation functionality

## Debug Script

Created `debug-quality-reviews.sql` to help investigate data relationships and identify any orphaned reviews or missing submissions.

## Next Steps

1. Run the debug script to verify data integrity
2. Test the fixes in the application
3. Ensure that the FileAnnotationTool component works correctly with the loaded files
4. Consider adding better error handling for cases where submissions have no files

## Notes

- The FileAnnotationTool component appears to be well-implemented and should work correctly once files are properly fetched
- Added proper TypeScript types to handle nullable relationships
- Included fallback mechanisms for edge cases where data relationships might be incomplete
  };
  } | null;
  designer: {
    full_name: string;
    email: string;
  } | null;
  submission_id?: string;
  // ... other fields
}
```

## Testing Recommendations

1. **Test Reviews List**: Verify that project titles now display correctly instead of "Unknown Project"
2. **Test File Annotations**: 
   - Navigate to an individual review page
   - Switch to the "Files & Annotations" tab
   - Verify that files are loaded from the related submission
   - Test the annotation functionality

## Debug Script

Created `debug-quality-reviews.sql` to help investigate data relationships and identify any orphaned reviews or missing submissions.

## Next Steps

1. Run the debug script to verify data integrity
2. Test the fixes in the application
3. Ensure that the FileAnnotationTool component works correctly with the loaded files
4. Consider adding better error handling for cases where submissions have no files

## Notes

- The FileAnnotationTool component appears to be well-implemented and should work correctly once files are properly fetched
- Added proper TypeScript types to handle nullable relationships
- Included fallback mechanisms for edge cases where data relationships might be incomplete
  };
  } | null;
  designer: {
    full_name: string;
    email: string;
  } | null;
  submission_id?: string;
  // ... other fields
}
```

## Testing Recommendations

1. **Test Reviews List**: Verify that project titles now display correctly instead of "Unknown Project"
2. **Test File Annotations**: 
   - Navigate to an individual review page
   - Switch to the "Files & Annotations" tab
   - Verify that files are loaded from the related submission
   - Test the annotation functionality

## Debug Script

Created `debug-quality-reviews.sql` to help investigate data relationships and identify any orphaned reviews or missing submissions.

## Next Steps

1. Run the debug script to verify data integrity
2. Test the fixes in the application
3. Ensure that the FileAnnotationTool component works correctly with the loaded files
4. Consider adding better error handling for cases where submissions have no files

## Notes

- The FileAnnotationTool component appears to be well-implemented and should work correctly once files are properly fetched
- Added proper TypeScript types to handle nullable relationships
- Included fallback mechanisms for edge cases where data relationships might be incomplete
  };
  } | null;
  designer: {
    full_name: string;
    email: string;
  } | null;
  submission_id?: string;
  // ... other fields
}
```

## Testing Recommendations

1. **Test Reviews List**: Verify that project titles now display correctly instead of "Unknown Project"
2. **Test File Annotations**: 
   - Navigate to an individual review page
   - Switch to the "Files & Annotations" tab
   - Verify that files are loaded from the related submission
   - Test the annotation functionality

## Debug Script

Created `debug-quality-reviews.sql` to help investigate data relationships and identify any orphaned reviews or missing submissions.

## Next Steps

1. Run the debug script to verify data integrity
2. Test the fixes in the application
3. Ensure that the FileAnnotationTool component works correctly with the loaded files
4. Consider adding better error handling for cases where submissions have no files

## Notes

- The FileAnnotationTool component appears to be well-implemented and should work correctly once files are properly fetched
- Added proper TypeScript types to handle nullable relationships
- Included fallback mechanisms for edge cases where data relationships might be incomplete
  };
  } | null;
  designer: {
    full_name: string;
    email: string;
  } | null;
  submission_id?: string;
  // ... other fields
}
```

## Testing Recommendations

1. **Test Reviews List**: Verify that project titles now display correctly instead of "Unknown Project"
2. **Test File Annotations**: 
   - Navigate to an individual review page
   - Switch to the "Files & Annotations" tab
   - Verify that files are loaded from the related submission
   - Test the annotation functionality

## Debug Script

Created `debug-quality-reviews.sql` to help investigate data relationships and identify any orphaned reviews or missing submissions.

## Next Steps

1. Run the debug script to verify data integrity
2. Test the fixes in the application
3. Ensure that the FileAnnotationTool component works correctly with the loaded files
4. Consider adding better error handling for cases where submissions have no files

## Notes

- The FileAnnotationTool component appears to be well-implemented and should work correctly once files are properly fetched
- Added proper TypeScript types to handle nullable relationships
- Included fallback mechanisms for edge cases where data relationships might be incomplete
  };
  } | null;
  designer: {
    full_name: string;
    email: string;
  } | null;
  submission_id?: string;
  // ... other fields
}
```

## Testing Recommendations

1. **Test Reviews List**: Verify that project titles now display correctly instead of "Unknown Project"
2. **Test File Annotations**: 
   - Navigate to an individual review page
   - Switch to the "Files & Annotations" tab
   - Verify that files are loaded from the related submission
   - Test the annotation functionality

## Debug Script

Created `debug-quality-reviews.sql` to help investigate data relationships and identify any orphaned reviews or missing submissions.

## Next Steps

1. Run the debug script to verify data integrity
2. Test the fixes in the application
3. Ensure that the FileAnnotationTool component works correctly with the loaded files
4. Consider adding better error handling for cases where submissions have no files

## Notes

- The FileAnnotationTool component appears to be well-implemented and should work correctly once files are properly fetched
- Added proper TypeScript types to handle nullable relationships
- Included fallback mechanisms for edge cases where data relationships might be incomplete
  };
  } | null;
  designer: {
    full_name: string;
    email: string;
  } | null;
  submission_id?: string;
  // ... other fields
}
```

## Testing Recommendations

1. **Test Reviews List**: Verify that project titles now display correctly instead of "Unknown Project"
2. **Test File Annotations**: 
   - Navigate to an individual review page
   - Switch to the "Files & Annotations" tab
   - Verify that files are loaded from the related submission
   - Test the annotation functionality

## Debug Script

Created `debug-quality-reviews.sql` to help investigate data relationships and identify any orphaned reviews or missing submissions.

## Next Steps

1. Run the debug script to verify data integrity
2. Test the fixes in the application
3. Ensure that the FileAnnotationTool component works correctly with the loaded files
4. Consider adding better error handling for cases where submissions have no files

## Notes

- The FileAnnotationTool component appears to be well-implemented and should work correctly once files are properly fetched
- Added proper TypeScript types to handle nullable relationships
- Included fallback mechanisms for edge cases where data relationships might be incomplete
  };
  } | null;
  designer: {
    full_name: string;
    email: string;
  } | null;
  submission_id?: string;
  // ... other fields
}
```

## Testing Recommendations

1. **Test Reviews List**: Verify that project titles now display correctly instead of "Unknown Project"
2. **Test File Annotations**: 
   - Navigate to an individual review page
   - Switch to the "Files & Annotations" tab
   - Verify that files are loaded from the related submission
   - Test the annotation functionality

## Debug Script

Created `debug-quality-reviews.sql` to help investigate data relationships and identify any orphaned reviews or missing submissions.

## Next Steps

1. Run the debug script to verify data integrity
2. Test the fixes in the application
3. Ensure that the FileAnnotationTool component works correctly with the loaded files
4. Consider adding better error handling for cases where submissions have no files

## Notes

- The FileAnnotationTool component appears to be well-implemented and should work correctly once files are properly fetched
- Added proper TypeScript types to handle nullable relationships
- Included fallback mechanisms for edge cases where data relationships might be incomplete
  };
  } | null;
  designer: {
    full_name: string;
    email: string;
  } | null;
  submission_id?: string;
  // ... other fields
}
```

## Testing Recommendations

1. **Test Reviews List**: Verify that project titles now display correctly instead of "Unknown Project"
2. **Test File Annotations**: 
   - Navigate to an individual review page
   - Switch to the "Files & Annotations" tab
   - Verify that files are loaded from the related submission
   - Test the annotation functionality

## Debug Script

Created `debug-quality-reviews.sql` to help investigate data relationships and identify any orphaned reviews or missing submissions.

## Next Steps

1. Run the debug script to verify data integrity
2. Test the fixes in the application
3. Ensure that the FileAnnotationTool component works correctly with the loaded files
4. Consider adding better error handling for cases where submissions have no files

## Notes

- The FileAnnotationTool component appears to be well-implemented and should work correctly once files are properly fetched
- Added proper TypeScript types to handle nullable relationships
- Included fallback mechanisms for edge cases where data relationships might be incomplete
  };
  } | null;
  designer: {
    full_name: string;
    email: string;
  } | null;
  submission_id?: string;
  // ... other fields
}
```

## Testing Recommendations

1. **Test Reviews List**: Verify that project titles now display correctly instead of "Unknown Project"
2. **Test File Annotations**: 
   - Navigate to an individual review page
   - Switch to the "Files & Annotations" tab
   - Verify that files are loaded from the related submission
   - Test the annotation functionality

## Debug Script

Created `debug-quality-reviews.sql` to help investigate data relationships and identify any orphaned reviews or missing submissions.

## Next Steps

1. Run the debug script to verify data integrity
2. Test the fixes in the application
3. Ensure that the FileAnnotationTool component works correctly with the loaded files
4. Consider adding better error handling for cases where submissions have no files

## Notes

- The FileAnnotationTool component appears to be well-implemented and should work correctly once files are properly fetched
- Added proper TypeScript types to handle nullable relationships
- Included fallback mechanisms for edge cases where data relationships might be incomplete
  };
  } | null;
  designer: {
    full_name: string;
    email: string;
  } | null;
  submission_id?: string;
  // ... other fields
}
```

## Testing Recommendations

1. **Test Reviews List**: Verify that project titles now display correctly instead of "Unknown Project"
2. **Test File Annotations**: 
   - Navigate to an individual review page
   - Switch to the "Files & Annotations" tab
   - Verify that files are loaded from the related submission
   - Test the annotation functionality

## Debug Script

Created `debug-quality-reviews.sql` to help investigate data relationships and identify any orphaned reviews or missing submissions.

## Next Steps

1. Run the debug script to verify data integrity
2. Test the fixes in the application
3. Ensure that the FileAnnotationTool component works correctly with the loaded files
4. Consider adding better error handling for cases where submissions have no files

## Notes

- The FileAnnotationTool component appears to be well-implemented and should work correctly once files are properly fetched
- Added proper TypeScript types to handle nullable relationships
- Included fallback mechanisms for edge cases where data relationships might be incomplete
  };
  } | null;
  designer: {
    full_name: string;
    email: string;
  } | null;
  submission_id?: string;
  // ... other fields
}
```

## Testing Recommendations

1. **Test Reviews List**: Verify that project titles now display correctly instead of "Unknown Project"
2. **Test File Annotations**: 
   - Navigate to an individual review page
   - Switch to the "Files & Annotations" tab
   - Verify that files are loaded from the related submission
   - Test the annotation functionality

## Debug Script

Created `debug-quality-reviews.sql` to help investigate data relationships and identify any orphaned reviews or missing submissions.

## Next Steps

1. Run the debug script to verify data integrity
2. Test the fixes in the application
3. Ensure that the FileAnnotationTool component works correctly with the loaded files
4. Consider adding better error handling for cases where submissions have no files

## Notes

- The FileAnnotationTool component appears to be well-implemented and should work correctly once files are properly fetched
- Added proper TypeScript types to handle nullable relationships
- Included fallback mechanisms for edge cases where data relationships might be incomplete
  };
  } | null;
  designer: {
    full_name: string;
    email: string;
  } | null;
  submission_id?: string;
  // ... other fields
}
```

## Testing Recommendations

1. **Test Reviews List**: Verify that project titles now display correctly instead of "Unknown Project"
2. **Test File Annotations**: 
   - Navigate to an individual review page
   - Switch to the "Files & Annotations" tab
   - Verify that files are loaded from the related submission
   - Test the annotation functionality

## Debug Script

Created `debug-quality-reviews.sql` to help investigate data relationships and identify any orphaned reviews or missing submissions.

## Next Steps

1. Run the debug script to verify data integrity
2. Test the fixes in the application
3. Ensure that the FileAnnotationTool component works correctly with the loaded files
4. Consider adding better error handling for cases where submissions have no files

## Notes

- The FileAnnotationTool component appears to be well-implemented and should work correctly once files are properly fetched
- Added proper TypeScript types to handle nullable relationships
- Included fallback mechanisms for edge cases where data relationships might be incomplete
  };
  } | null;
  designer: {
    full_name: string;
    email: string;
  } | null;
  submission_id?: string;
  // ... other fields
}
```

## Testing Recommendations

1. **Test Reviews List**: Verify that project titles now display correctly instead of "Unknown Project"
2. **Test File Annotations**: 
   - Navigate to an individual review page
   - Switch to the "Files & Annotations" tab
   - Verify that files are loaded from the related submission
   - Test the annotation functionality

## Debug Script

Created `debug-quality-reviews.sql` to help investigate data relationships and identify any orphaned reviews or missing submissions.

## Next Steps

1. Run the debug script to verify data integrity
2. Test the fixes in the application
3. Ensure that the FileAnnotationTool component works correctly with the loaded files
4. Consider adding better error handling for cases where submissions have no files

## Notes

- The FileAnnotationTool component appears to be well-implemented and should work correctly once files are properly fetched
- Added proper TypeScript types to handle nullable relationships
- Included fallback mechanisms for edge cases where data relationships might be incomplete
  };
  } | null;
  designer: {
    full_name: string;
    email: string;
  } | null;
  submission_id?: string;
  // ... other fields
}
```

## Testing Recommendations

1. **Test Reviews List**: Verify that project titles now display correctly instead of "Unknown Project"
2. **Test File Annotations**: 
   - Navigate to an individual review page
   - Switch to the "Files & Annotations" tab
   - Verify that files are loaded from the related submission
   - Test the annotation functionality

## Debug Script

Created `debug-quality-reviews.sql` to help investigate data relationships and identify any orphaned reviews or missing submissions.

## Next Steps

1. Run the debug script to verify data integrity
2. Test the fixes in the application
3. Ensure that the FileAnnotationTool component works correctly with the loaded files
4. Consider adding better error handling for cases where submissions have no files

## Notes

- The FileAnnotationTool component appears to be well-implemented and should work correctly once files are properly fetched
- Added proper TypeScript types to handle nullable relationships
- Included fallback mechanisms for edge cases where data relationships might be incomplete
  };
  } | null;
  designer: {
    full_name: string;
    email: string;
  } | null;
  submission_id?: string;
  // ... other fields
}
```

## Testing Recommendations

1. **Test Reviews List**: Verify that project titles now display correctly instead of "Unknown Project"
2. **Test File Annotations**: 
   - Navigate to an individual review page
   - Switch to the "Files & Annotations" tab
   - Verify that files are loaded from the related submission
   - Test the annotation functionality

## Debug Script

Created `debug-quality-reviews.sql` to help investigate data relationships and identify any orphaned reviews or missing submissions.

## Next Steps

1. Run the debug script to verify data integrity
2. Test the fixes in the application
3. Ensure that the FileAnnotationTool component works correctly with the loaded files
4. Consider adding better error handling for cases where submissions have no files

## Notes

- The FileAnnotationTool component appears to be well-implemented and should work correctly once files are properly fetched
- Added proper TypeScript types to handle nullable relationships
- Included fallback mechanisms for edge cases where data relationships might be incomplete
  };
  } | null;
  designer: {
    full_name: string;
    email: string;
  } | null;
  submission_id?: string;
  // ... other fields
}
```

## Testing Recommendations

1. **Test Reviews List**: Verify that project titles now display correctly instead of "Unknown Project"
2. **Test File Annotations**: 
   - Navigate to an individual review page
   - Switch to the "Files & Annotations" tab
   - Verify that files are loaded from the related submission
   - Test the annotation functionality

## Debug Script

Created `debug-quality-reviews.sql` to help investigate data relationships and identify any orphaned reviews or missing submissions.

## Next Steps

1. Run the debug script to verify data integrity
2. Test the fixes in the application
3. Ensure that the FileAnnotationTool component works correctly with the loaded files
4. Consider adding better error handling for cases where submissions have no files

## Notes

- The FileAnnotationTool component appears to be well-implemented and should work correctly once files are properly fetched
- Added proper TypeScript types to handle nullable relationships
- Included fallback mechanisms for edge cases where data relationships might be incomplete
  };
  } | null;
  designer: {
    full_name: string;
    email: string;
  } | null;
  submission_id?: string;
  // ... other fields
}
```

## Testing Recommendations

1. **Test Reviews List**: Verify that project titles now display correctly instead of "Unknown Project"
2. **Test File Annotations**: 
   - Navigate to an individual review page
   - Switch to the "Files & Annotations" tab
   - Verify that files are loaded from the related submission
   - Test the annotation functionality

## Debug Script

Created `debug-quality-reviews.sql` to help investigate data relationships and identify any orphaned reviews or missing submissions.

## Next Steps

1. Run the debug script to verify data integrity
2. Test the fixes in the application
3. Ensure that the FileAnnotationTool component works correctly with the loaded files
4. Consider adding better error handling for cases where submissions have no files

## Notes

- The FileAnnotationTool component appears to be well-implemented and should work correctly once files are properly fetched
- Added proper TypeScript types to handle nullable relationships
- Included fallback mechanisms for edge cases where data relationships might be incomplete
  };
  } | null;
  designer: {
    full_name: string;
    email: string;
  } | null;
  submission_id?: string;
  // ... other fields
}
```

## Testing Recommendations

1. **Test Reviews List**: Verify that project titles now display correctly instead of "Unknown Project"
2. **Test File Annotations**: 
   - Navigate to an individual review page
   - Switch to the "Files & Annotations" tab
   - Verify that files are loaded from the related submission
   - Test the annotation functionality

## Debug Script

Created `debug-quality-reviews.sql` to help investigate data relationships and identify any orphaned reviews or missing submissions.

## Next Steps

1. Run the debug script to verify data integrity
2. Test the fixes in the application
3. Ensure that the FileAnnotationTool component works correctly with the loaded files
4. Consider adding better error handling for cases where submissions have no files

## Notes

- The FileAnnotationTool component appears to be well-implemented and should work correctly once files are properly fetched
- Added proper TypeScript types to handle nullable relationships
- Included fallback mechanisms for edge cases where data relationships might be incomplete
  };
  } | null;
  designer: {
    full_name: string;
    email: string;
  } | null;
  submission_id?: string;
  // ... other fields
}
```

## Testing Recommendations

1. **Test Reviews List**: Verify that project titles now display correctly instead of "Unknown Project"
2. **Test File Annotations**: 
   - Navigate to an individual review page
   - Switch to the "Files & Annotations" tab
   - Verify that files are loaded from the related submission
   - Test the annotation functionality

## Debug Script

Created `debug-quality-reviews.sql` to help investigate data relationships and identify any orphaned reviews or missing submissions.

## Next Steps

1. Run the debug script to verify data integrity
2. Test the fixes in the application
3. Ensure that the FileAnnotationTool component works correctly with the loaded files
4. Consider adding better error handling for cases where submissions have no files

## Notes

- The FileAnnotationTool component appears to be well-implemented and should work correctly once files are properly fetched
- Added proper TypeScript types to handle nullable relationships
- Included fallback mechanisms for edge cases where data relationships might be incomplete
  };
  } | null;
  designer: {
    full_name: string;
    email: string;
  } | null;
  submission_id?: string;
  // ... other fields
}
```

## Testing Recommendations

1. **Test Reviews List**: Verify that project titles now display correctly instead of "Unknown Project"
2. **Test File Annotations**: 
   - Navigate to an individual review page
   - Switch to the "Files & Annotations" tab
   - Verify that files are loaded from the related submission
   - Test the annotation functionality

## Debug Script

Created `debug-quality-reviews.sql` to help investigate data relationships and identify any orphaned reviews or missing submissions.

## Next Steps

1. Run the debug script to verify data integrity
2. Test the fixes in the application
3. Ensure that the FileAnnotationTool component works correctly with the loaded files
4. Consider adding better error handling for cases where submissions have no files

## Notes

- The FileAnnotationTool component appears to be well-implemented and should work correctly once files are properly fetched
- Added proper TypeScript types to handle nullable relationships
- Included fallback mechanisms for edge cases where data relationships might be incomplete
  };
  } | null;
  designer: {
    full_name: string;
    email: string;
  } | null;
  submission_id?: string;
  // ... other fields
}
```

## Testing Recommendations

1. **Test Reviews List**: Verify that project titles now display correctly instead of "Unknown Project"
2. **Test File Annotations**: 
   - Navigate to an individual review page
   - Switch to the "Files & Annotations" tab
   - Verify that files are loaded from the related submission
   - Test the annotation functionality

## Debug Script

Created `debug-quality-reviews.sql` to help investigate data relationships and identify any orphaned reviews or missing submissions.

## Next Steps

1. Run the debug script to verify data integrity
2. Test the fixes in the application
3. Ensure that the FileAnnotationTool component works correctly with the loaded files
4. Consider adding better error handling for cases where submissions have no files

## Notes

- The FileAnnotationTool component appears to be well-implemented and should work correctly once files are properly fetched
- Added proper TypeScript types to handle nullable relationships
- Included fallback mechanisms for edge cases where data relationships might be incomplete
  };
  } | null;
  designer: {
    full_name: string;
    email: string;
  } | null;
  submission_id?: string;
  // ... other fields
}
```

## Testing Recommendations

1. **Test Reviews List**: Verify that project titles now display correctly instead of "Unknown Project"
2. **Test File Annotations**: 
   - Navigate to an individual review page
   - Switch to the "Files & Annotations" tab
   - Verify that files are loaded from the related submission
   - Test the annotation functionality

## Debug Script

Created `debug-quality-reviews.sql` to help investigate data relationships and identify any orphaned reviews or missing submissions.

## Next Steps

1. Run the debug script to verify data integrity
2. Test the fixes in the application
3. Ensure that the FileAnnotationTool component works correctly with the loaded files
4. Consider adding better error handling for cases where submissions have no files

## Notes

- The FileAnnotationTool component appears to be well-implemented and should work correctly once files are properly fetched
- Added proper TypeScript types to handle nullable relationships
- Included fallback mechanisms for edge cases where data relationships might be incomplete
  };
  } | null;
  designer: {
    full_name: string;
    email: string;
  } | null;
  submission_id?: string;
  // ... other fields
}
```

## Testing Recommendations

1. **Test Reviews List**: Verify that project titles now display correctly instead of "Unknown Project"
2. **Test File Annotations**: 
   - Navigate to an individual review page
   - Switch to the "Files & Annotations" tab
   - Verify that files are loaded from the related submission
   - Test the annotation functionality

## Debug Script

Created `debug-quality-reviews.sql` to help investigate data relationships and identify any orphaned reviews or missing submissions.

## Next Steps

1. Run the debug script to verify data integrity
2. Test the fixes in the application
3. Ensure that the FileAnnotationTool component works correctly with the loaded files
4. Consider adding better error handling for cases where submissions have no files

## Notes

- The FileAnnotationTool component appears to be well-implemented and should work correctly once files are properly fetched
- Added proper TypeScript types to handle nullable relationships
- Included fallback mechanisms for edge cases where data relationships might be incomplete
  };
  } | null;
  designer: {
    full_name: string;
    email: string;
  } | null;
  submission_id?: string;
  // ... other fields
}
```

## Testing Recommendations

1. **Test Reviews List**: Verify that project titles now display correctly instead of "Unknown Project"
2. **Test File Annotations**: 
   - Navigate to an individual review page
   - Switch to the "Files & Annotations" tab
   - Verify that files are loaded from the related submission
   - Test the annotation functionality

## Debug Script

Created `debug-quality-reviews.sql` to help investigate data relationships and identify any orphaned reviews or missing submissions.

## Next Steps

1. Run the debug script to verify data integrity
2. Test the fixes in the application
3. Ensure that the FileAnnotationTool component works correctly with the loaded files
4. Consider adding better error handling for cases where submissions have no files

## Notes

- The FileAnnotationTool component appears to be well-implemented and should work correctly once files are properly fetched
- Added proper TypeScript types to handle nullable relationships
- Included fallback mechanisms for edge cases where data relationships might be incomplete
  };
  } | null;
  designer: {
    full_name: string;
    email: string;
  } | null;
  submission_id?: string;
  // ... other fields
}
```

## Testing Recommendations

1. **Test Reviews List**: Verify that project titles now display correctly instead of "Unknown Project"
2. **Test File Annotations**: 
   - Navigate to an individual review page
   - Switch to the "Files & Annotations" tab
   - Verify that files are loaded from the related submission
   - Test the annotation functionality

## Debug Script

Created `debug-quality-reviews.sql` to help investigate data relationships and identify any orphaned reviews or missing submissions.

## Next Steps

1. Run the debug script to verify data integrity
2. Test the fixes in the application
3. Ensure that the FileAnnotationTool component works correctly with the loaded files
4. Consider adding better error handling for cases where submissions have no files

## Notes

- The FileAnnotationTool component appears to be well-implemented and should work correctly once files are properly fetched
- Added proper TypeScript types to handle nullable relationships
- Included fallback mechanisms for edge cases where data relationships might be incomplete
  };
  } | null;
  designer: {
    full_name: string;
    email: string;
  } | null;
  submission_id?: string;
  // ... other fields
}
```

## Testing Recommendations

1. **Test Reviews List**: Verify that project titles now display correctly instead of "Unknown Project"
2. **Test File Annotations**: 
   - Navigate to an individual review page
   - Switch to the "Files & Annotations" tab
   - Verify that files are loaded from the related submission
   - Test the annotation functionality

## Debug Script

Created `debug-quality-reviews.sql` to help investigate data relationships and identify any orphaned reviews or missing submissions.

## Next Steps

1. Run the debug script to verify data integrity
2. Test the fixes in the application
3. Ensure that the FileAnnotationTool component works correctly with the loaded files
4. Consider adding better error handling for cases where submissions have no files

## Notes

- The FileAnnotationTool component appears to be well-implemented and should work correctly once files are properly fetched
- Added proper TypeScript types to handle nullable relationships
- Included fallback mechanisms for edge cases where data relationships might be incomplete
  };
  } | null;
  designer: {
    full_name: string;
    email: string;
  } | null;
  submission_id?: string;
  // ... other fields
}
```

## Testing Recommendations

1. **Test Reviews List**: Verify that project titles now display correctly instead of "Unknown Project"
2. **Test File Annotations**: 
   - Navigate to an individual review page
   - Switch to the "Files & Annotations" tab
   - Verify that files are loaded from the related submission
   - Test the annotation functionality

## Debug Script

Created `debug-quality-reviews.sql` to help investigate data relationships and identify any orphaned reviews or missing submissions.

## Next Steps

1. Run the debug script to verify data integrity
2. Test the fixes in the application
3. Ensure that the FileAnnotationTool component works correctly with the loaded files
4. Consider adding better error handling for cases where submissions have no files

## Notes

- The FileAnnotationTool component appears to be well-implemented and should work correctly once files are properly fetched
- Added proper TypeScript types to handle nullable relationships
- Included fallback mechanisms for edge cases where data relationships might be incomplete
  };
  } | null;
  designer: {
    full_name: string;
    email: string;
  } | null;
  submission_id?: string;
  // ... other fields
}
```

## Testing Recommendations

1. **Test Reviews List**: Verify that project titles now display correctly instead of "Unknown Project"
2. **Test File Annotations**: 
   - Navigate to an individual review page
   - Switch to the "Files & Annotations" tab
   - Verify that files are loaded from the related submission
   - Test the annotation functionality

## Debug Script

Created `debug-quality-reviews.sql` to help investigate data relationships and identify any orphaned reviews or missing submissions.

## Next Steps

1. Run the debug script to verify data integrity
2. Test the fixes in the application
3. Ensure that the FileAnnotationTool component works correctly with the loaded files
4. Consider adding better error handling for cases where submissions have no files

## Notes

- The FileAnnotationTool component appears to be well-implemented and should work correctly once files are properly fetched
- Added proper TypeScript types to handle nullable relationships
- Included fallback mechanisms for edge cases where data relationships might be incomplete
  };
  } | null;
  designer: {
    full_name: string;
    email: string;
  } | null;
  submission_id?: string;
  // ... other fields
}
```

## Testing Recommendations

1. **Test Reviews List**: Verify that project titles now display correctly instead of "Unknown Project"
2. **Test File Annotations**: 
   - Navigate to an individual review page
   - Switch to the "Files & Annotations" tab
   - Verify that files are loaded from the related submission
   - Test the annotation functionality

## Debug Script

Created `debug-quality-reviews.sql` to help investigate data relationships and identify any orphaned reviews or missing submissions.

## Next Steps

1. Run the debug script to verify data integrity
2. Test the fixes in the application
3. Ensure that the FileAnnotationTool component works correctly with the loaded files
4. Consider adding better error handling for cases where submissions have no files

## Notes

- The FileAnnotationTool component appears to be well-implemented and should work correctly once files are properly fetched
- Added proper TypeScript types to handle nullable relationships
- Included fallback mechanisms for edge cases where data relationships might be incomplete
  };
  } | null;
  designer: {
    full_name: string;
    email: string;
  } | null;
  submission_id?: string;
  // ... other fields
}
```

## Testing Recommendations

1. **Test Reviews List**: Verify that project titles now display correctly instead of "Unknown Project"
2. **Test File Annotations**: 
   - Navigate to an individual review page
   - Switch to the "Files & Annotations" tab
   - Verify that files are loaded from the related submission
   - Test the annotation functionality

## Debug Script

Created `debug-quality-reviews.sql` to help investigate data relationships and identify any orphaned reviews or missing submissions.

## Next Steps

1. Run the debug script to verify data integrity
2. Test the fixes in the application
3. Ensure that the FileAnnotationTool component works correctly with the loaded files
4. Consider adding better error handling for cases where submissions have no files

## Notes

- The FileAnnotationTool component appears to be well-implemented and should work correctly once files are properly fetched
- Added proper TypeScript types to handle nullable relationships
- Included fallback mechanisms for edge cases where data relationships might be incomplete
  };
  } | null;
  designer: {
    full_name: string;
    email: string;
  } | null;
  submission_id?: string;
  // ... other fields
}
```

## Testing Recommendations

1. **Test Reviews List**: Verify that project titles now display correctly instead of "Unknown Project"
2. **Test File Annotations**: 
   - Navigate to an individual review page
   - Switch to the "Files & Annotations" tab
   - Verify that files are loaded from the related submission
   - Test the annotation functionality

## Debug Script

Created `debug-quality-reviews.sql` to help investigate data relationships and identify any orphaned reviews or missing submissions.

## Next Steps

1. Run the debug script to verify data integrity
2. Test the fixes in the application
3. Ensure that the FileAnnotationTool component works correctly with the loaded files
4. Consider adding better error handling for cases where submissions have no files

## Notes

- The FileAnnotationTool component appears to be well-implemented and should work correctly once files are properly fetched
- Added proper TypeScript types to handle nullable relationships
- Included fallback mechanisms for edge cases where data relationships might be incomplete
  };
  } | null;
  designer: {
    full_name: string;
    email: string;
  } | null;
  submission_id?: string;
  // ... other fields
}
```

## Testing Recommendations

1. **Test Reviews List**: Verify that project titles now display correctly instead of "Unknown Project"
2. **Test File Annotations**: 
   - Navigate to an individual review page
   - Switch to the "Files & Annotations" tab
   - Verify that files are loaded from the related submission
   - Test the annotation functionality

## Debug Script

Created `debug-quality-reviews.sql` to help investigate data relationships and identify any orphaned reviews or missing submissions.

## Next Steps

1. Run the debug script to verify data integrity
2. Test the fixes in the application
3. Ensure that the FileAnnotationTool component works correctly with the loaded files
4. Consider adding better error handling for cases where submissions have no files

## Notes

- The FileAnnotationTool component appears to be well-implemented and should work correctly once files are properly fetched
- Added proper TypeScript types to handle nullable relationships
- Included fallback mechanisms for edge cases where data relationships might be incomplete
  };
  } | null;
  designer: {
    full_name: string;
    email: string;
  } | null;
  submission_id?: string;
  // ... other fields
}
```

## Testing Recommendations

1. **Test Reviews List**: Verify that project titles now display correctly instead of "Unknown Project"
2. **Test File Annotations**: 
   - Navigate to an individual review page
   - Switch to the "Files & Annotations" tab
   - Verify that files are loaded from the related submission
   - Test the annotation functionality

## Debug Script

Created `debug-quality-reviews.sql` to help investigate data relationships and identify any orphaned reviews or missing submissions.

## Next Steps

1. Run the debug script to verify data integrity
2. Test the fixes in the application
3. Ensure that the FileAnnotationTool component works correctly with the loaded files
4. Consider adding better error handling for cases where submissions have no files

## Notes

- The FileAnnotationTool component appears to be well-implemented and should work correctly once files are properly fetched
- Added proper TypeScript types to handle nullable relationships
- Included fallback mechanisms for edge cases where data relationships might be incomplete
  };
  } | null;
  designer: {
    full_name: string;
    email: string;
  } | null;
  submission_id?: string;
  // ... other fields
}
```

## Testing Recommendations

1. **Test Reviews List**: Verify that project titles now display correctly instead of "Unknown Project"
2. **Test File Annotations**: 
   - Navigate to an individual review page
   - Switch to the "Files & Annotations" tab
   - Verify that files are loaded from the related submission
   - Test the annotation functionality

## Debug Script

Created `debug-quality-reviews.sql` to help investigate data relationships and identify any orphaned reviews or missing submissions.

## Next Steps

1. Run the debug script to verify data integrity
2. Test the fixes in the application
3. Ensure that the FileAnnotationTool component works correctly with the loaded files
4. Consider adding better error handling for cases where submissions have no files

## Notes

- The FileAnnotationTool component appears to be well-implemented and should work correctly once files are properly fetched
- Added proper TypeScript types to handle nullable relationships
- Included fallback mechanisms for edge cases where data relationships might be incomplete
  };
  } | null;
  designer: {
    full_name: string;
    email: string;
  } | null;
  submission_id?: string;
  // ... other fields
}
```

## Testing Recommendations

1. **Test Reviews List**: Verify that project titles now display correctly instead of "Unknown Project"
2. **Test File Annotations**: 
   - Navigate to an individual review page
   - Switch to the "Files & Annotations" tab
   - Verify that files are loaded from the related submission
   - Test the annotation functionality

## Debug Script

Created `debug-quality-reviews.sql` to help investigate data relationships and identify any orphaned reviews or missing submissions.

## Next Steps

1. Run the debug script to verify data integrity
2. Test the fixes in the application
3. Ensure that the FileAnnotationTool component works correctly with the loaded files
4. Consider adding better error handling for cases where submissions have no files

## Notes

- The FileAnnotationTool component appears to be well-implemented and should work correctly once files are properly fetched
- Added proper TypeScript types to handle nullable relationships
- Included fallback mechanisms for edge cases where data relationships might be incomplete
  };
  } | null;
  designer: {
    full_name: string;
    email: string;
  } | null;
  submission_id?: string;
  // ... other fields
}
```

## Testing Recommendations

1. **Test Reviews List**: Verify that project titles now display correctly instead of "Unknown Project"
2. **Test File Annotations**: 
   - Navigate to an individual review page
   - Switch to the "Files & Annotations" tab
   - Verify that files are loaded from the related submission
   - Test the annotation functionality

## Debug Script

Created `debug-quality-reviews.sql` to help investigate data relationships and identify any orphaned reviews or missing submissions.

## Next Steps

1. Run the debug script to verify data integrity
2. Test the fixes in the application
3. Ensure that the FileAnnotationTool component works correctly with the loaded files
4. Consider adding better error handling for cases where submissions have no files

## Notes

- The FileAnnotationTool component appears to be well-implemented and should work correctly once files are properly fetched
- Added proper TypeScript types to handle nullable relationships
- Included fallback mechanisms for edge cases where data relationships might be incomplete
  };
  } | null;
  designer: {
    full_name: string;
    email: string;
  } | null;
  submission_id?: string;
  // ... other fields
}
```

## Testing Recommendations

1. **Test Reviews List**: Verify that project titles now display correctly instead of "Unknown Project"
2. **Test File Annotations**: 
   - Navigate to an individual review page
   - Switch to the "Files & Annotations" tab
   - Verify that files are loaded from the related submission
   - Test the annotation functionality

## Debug Script

Created `debug-quality-reviews.sql` to help investigate data relationships and identify any orphaned reviews or missing submissions.

## Next Steps

1. Run the debug script to verify data integrity
2. Test the fixes in the application
3. Ensure that the FileAnnotationTool component works correctly with the loaded files
4. Consider adding better error handling for cases where submissions have no files

## Notes

- The FileAnnotationTool component appears to be well-implemented and should work correctly once files are properly fetched
- Added proper TypeScript types to handle nullable relationships
- Included fallback mechanisms for edge cases where data relationships might be incomplete
  };
  } | null;
  designer: {
    full_name: string;
    email: string;
  } | null;
  submission_id?: string;
  // ... other fields
}
```

## Testing Recommendations

1. **Test Reviews List**: Verify that project titles now display correctly instead of "Unknown Project"
2. **Test File Annotations**: 
   - Navigate to an individual review page
   - Switch to the "Files & Annotations" tab
   - Verify that files are loaded from the related submission
   - Test the annotation functionality

## Debug Script

Created `debug-quality-reviews.sql` to help investigate data relationships and identify any orphaned reviews or missing submissions.

## Next Steps

1. Run the debug script to verify data integrity
2. Test the fixes in the application
3. Ensure that the FileAnnotationTool component works correctly with the loaded files
4. Consider adding better error handling for cases where submissions have no files

## Notes

- The FileAnnotationTool component appears to be well-implemented and should work correctly once files are properly fetched
- Added proper TypeScript types to handle nullable relationships
- Included fallback mechanisms for edge cases where data relationships might be incomplete
  };
  } | null;
  designer: {
    full_name: string;
    email: string;
  } | null;
  submission_id?: string;
  // ... other fields
}
```

## Testing Recommendations

1. **Test Reviews List**: Verify that project titles now display correctly instead of "Unknown Project"
2. **Test File Annotations**: 
   - Navigate to an individual review page
   - Switch to the "Files & Annotations" tab
   - Verify that files are loaded from the related submission
   - Test the annotation functionality

## Debug Script

Created `debug-quality-reviews.sql` to help investigate data relationships and identify any orphaned reviews or missing submissions.

## Next Steps

1. Run the debug script to verify data integrity
2. Test the fixes in the application
3. Ensure that the FileAnnotationTool component works correctly with the loaded files
4. Consider adding better error handling for cases where submissions have no files

## Notes

- The FileAnnotationTool component appears to be well-implemented and should work correctly once files are properly fetched
- Added proper TypeScript types to handle nullable relationships
- Included fallback mechanisms for edge cases where data relationships might be incomplete
  };
  } | null;
  designer: {
    full_name: string;
    email: string;
  } | null;
  submission_id?: string;
  // ... other fields
}
```

## Testing Recommendations

1. **Test Reviews List**: Verify that project titles now display correctly instead of "Unknown Project"
2. **Test File Annotations**: 
   - Navigate to an individual review page
   - Switch to the "Files & Annotations" tab
   - Verify that files are loaded from the related submission
   - Test the annotation functionality

## Debug Script

Created `debug-quality-reviews.sql` to help investigate data relationships and identify any orphaned reviews or missing submissions.

## Next Steps

1. Run the debug script to verify data integrity
2. Test the fixes in the application
3. Ensure that the FileAnnotationTool component works correctly with the loaded files
4. Consider adding better error handling for cases where submissions have no files

## Notes

- The FileAnnotationTool component appears to be well-implemented and should work correctly once files are properly fetched
- Added proper TypeScript types to handle nullable relationships
- Included fallback mechanisms for edge cases where data relationships might be incomplete
  };
  } | null;
  designer: {
    full_name: string;
    email: string;
  } | null;
  submission_id?: string;
  // ... other fields
}
```

## Testing Recommendations

1. **Test Reviews List**: Verify that project titles now display correctly instead of "Unknown Project"
2. **Test File Annotations**: 
   - Navigate to an individual review page
   - Switch to the "Files & Annotations" tab
   - Verify that files are loaded from the related submission
   - Test the annotation functionality

## Debug Script

Created `debug-quality-reviews.sql` to help investigate data relationships and identify any orphaned reviews or missing submissions.

## Next Steps

1. Run the debug script to verify data integrity
2. Test the fixes in the application
3. Ensure that the FileAnnotationTool component works correctly with the loaded files
4. Consider adding better error handling for cases where submissions have no files

## Notes

- The FileAnnotationTool component appears to be well-implemented and should work correctly once files are properly fetched
- Added proper TypeScript types to handle nullable relationships
- Included fallback mechanisms for edge cases where data relationships might be incomplete
  };
  } | null;
  designer: {
    full_name: string;
    email: string;
  } | null;
  submission_id?: string;
  // ... other fields
}
```

## Testing Recommendations

1. **Test Reviews List**: Verify that project titles now display correctly instead of "Unknown Project"
2. **Test File Annotations**: 
   - Navigate to an individual review page
   - Switch to the "Files & Annotations" tab
   - Verify that files are loaded from the related submission
   - Test the annotation functionality

## Debug Script

Created `debug-quality-reviews.sql` to help investigate data relationships and identify any orphaned reviews or missing submissions.

## Next Steps

1. Run the debug script to verify data integrity
2. Test the fixes in the application
3. Ensure that the FileAnnotationTool component works correctly with the loaded files
4. Consider adding better error handling for cases where submissions have no files

## Notes

- The FileAnnotationTool component appears to be well-implemented and should work correctly once files are properly fetched
- Added proper TypeScript types to handle nullable relationships
- Included fallback mechanisms for edge cases where data relationships might be incomplete
  };
  } | null;
  designer: {
    full_name: string;
    email: string;
  } | null;
  submission_id?: string;
  // ... other fields
}
```

## Testing Recommendations

1. **Test Reviews List**: Verify that project titles now display correctly instead of "Unknown Project"
2. **Test File Annotations**: 
   - Navigate to an individual review page
   - Switch to the "Files & Annotations" tab
   - Verify that files are loaded from the related submission
   - Test the annotation functionality

## Debug Script

Created `debug-quality-reviews.sql` to help investigate data relationships and identify any orphaned reviews or missing submissions.

## Next Steps

1. Run the debug script to verify data integrity
2. Test the fixes in the application
3. Ensure that the FileAnnotationTool component works correctly with the loaded files
4. Consider adding better error handling for cases where submissions have no files

## Notes

- The FileAnnotationTool component appears to be well-implemented and should work correctly once files are properly fetched
- Added proper TypeScript types to handle nullable relationships
- Included fallback mechanisms for edge cases where data relationships might be incomplete
  };
  } | null;
  designer: {
    full_name: string;
    email: string;
  } | null;
  submission_id?: string;
  // ... other fields
}
```

## Testing Recommendations

1. **Test Reviews List**: Verify that project titles now display correctly instead of "Unknown Project"
2. **Test File Annotations**: 
   - Navigate to an individual review page
   - Switch to the "Files & Annotations" tab
   - Verify that files are loaded from the related submission
   - Test the annotation functionality

## Debug Script

Created `debug-quality-reviews.sql` to help investigate data relationships and identify any orphaned reviews or missing submissions.

## Next Steps

1. Run the debug script to verify data integrity
2. Test the fixes in the application
3. Ensure that the FileAnnotationTool component works correctly with the loaded files
4. Consider adding better error handling for cases where submissions have no files

## Notes

- The FileAnnotationTool component appears to be well-implemented and should work correctly once files are properly fetched
- Added proper TypeScript types to handle nullable relationships
- Included fallback mechanisms for edge cases where data relationships might be incomplete
  };
  } | null;
  designer: {
    full_name: string;
    email: string;
  } | null;
  submission_id?: string;
  // ... other fields
}
```

## Testing Recommendations

1. **Test Reviews List**: Verify that project titles now display correctly instead of "Unknown Project"
2. **Test File Annotations**: 
   - Navigate to an individual review page
   - Switch to the "Files & Annotations" tab
   - Verify that files are loaded from the related submission
   - Test the annotation functionality

## Debug Script

Created `debug-quality-reviews.sql` to help investigate data relationships and identify any orphaned reviews or missing submissions.

## Next Steps

1. Run the debug script to verify data integrity
2. Test the fixes in the application
3. Ensure that the FileAnnotationTool component works correctly with the loaded files
4. Consider adding better error handling for cases where submissions have no files

## Notes

- The FileAnnotationTool component appears to be well-implemented and should work correctly once files are properly fetched
- Added proper TypeScript types to handle nullable relationships
- Included fallback mechanisms for edge cases where data relationships might be incomplete
  };
  } | null;
  designer: {
    full_name: string;
    email: string;
  } | null;
  submission_id?: string;
  // ... other fields
}
```

## Testing Recommendations

1. **Test Reviews List**: Verify that project titles now display correctly instead of "Unknown Project"
2. **Test File Annotations**: 
   - Navigate to an individual review page
   - Switch to the "Files & Annotations" tab
   - Verify that files are loaded from the related submission
   - Test the annotation functionality

## Debug Script

Created `debug-quality-reviews.sql` to help investigate data relationships and identify any orphaned reviews or missing submissions.

## Next Steps

1. Run the debug script to verify data integrity
2. Test the fixes in the application
3. Ensure that the FileAnnotationTool component works correctly with the loaded files
4. Consider adding better error handling for cases where submissions have no files

## Notes

- The FileAnnotationTool component appears to be well-implemented and should work correctly once files are properly fetched
- Added proper TypeScript types to handle nullable relationships
- Included fallback mechanisms for edge cases where data relationships might be incomplete
  };
  } | null;
  designer: {
    full_name: string;
    email: string;
  } | null;
  submission_id?: string;
  // ... other fields
}
```

## Testing Recommendations

1. **Test Reviews List**: Verify that project titles now display correctly instead of "Unknown Project"
2. **Test File Annotations**: 
   - Navigate to an individual review page
   - Switch to the "Files & Annotations" tab
   - Verify that files are loaded from the related submission
   - Test the annotation functionality

## Debug Script

Created `debug-quality-reviews.sql` to help investigate data relationships and identify any orphaned reviews or missing submissions.

## Next Steps

1. Run the debug script to verify data integrity
2. Test the fixes in the application
3. Ensure that the FileAnnotationTool component works correctly with the loaded files
4. Consider adding better error handling for cases where submissions have no files

## Notes

- The FileAnnotationTool component appears to be well-implemented and should work correctly once files are properly fetched
- Added proper TypeScript types to handle nullable relationships
- Included fallback mechanisms for edge cases where data relationships might be incomplete
  };
  } | null;
  designer: {
    full_name: string;
    email: string;
  } | null;
  submission_id?: string;
  // ... other fields
}
```

## Testing Recommendations

1. **Test Reviews List**: Verify that project titles now display correctly instead of "Unknown Project"
2. **Test File Annotations**: 
   - Navigate to an individual review page
   - Switch to the "Files & Annotations" tab
   - Verify that files are loaded from the related submission
   - Test the annotation functionality

## Debug Script

Created `debug-quality-reviews.sql` to help investigate data relationships and identify any orphaned reviews or missing submissions.

## Next Steps

1. Run the debug script to verify data integrity
2. Test the fixes in the application
3. Ensure that the FileAnnotationTool component works correctly with the loaded files
4. Consider adding better error handling for cases where submissions have no files

## Notes

- The FileAnnotationTool component appears to be well-implemented and should work correctly once files are properly fetched
- Added proper TypeScript types to handle nullable relationships
- Included fallback mechanisms for edge cases where data relationships might be incomplete
  };
  } | null;
  designer: {
    full_name: string;
    email: string;
  } | null;
  submission_id?: string;
  // ... other fields
}
```

## Testing Recommendations

1. **Test Reviews List**: Verify that project titles now display correctly instead of "Unknown Project"
2. **Test File Annotations**: 
   - Navigate to an individual review page
   - Switch to the "Files & Annotations" tab
   - Verify that files are loaded from the related submission
   - Test the annotation functionality

## Debug Script

Created `debug-quality-reviews.sql` to help investigate data relationships and identify any orphaned reviews or missing submissions.

## Next Steps

1. Run the debug script to verify data integrity
2. Test the fixes in the application
3. Ensure that the FileAnnotationTool component works correctly with the loaded files
4. Consider adding better error handling for cases where submissions have no files

## Notes

- The FileAnnotationTool component appears to be well-implemented and should work correctly once files are properly fetched
- Added proper TypeScript types to handle nullable relationships
- Included fallback mechanisms for edge cases where data relationships might be incomplete
  };
  } | null;
  designer: {
    full_name: string;
    email: string;
  } | null;
  submission_id?: string;
  // ... other fields
}
```

## Testing Recommendations

1. **Test Reviews List**: Verify that project titles now display correctly instead of "Unknown Project"
2. **Test File Annotations**: 
   - Navigate to an individual review page
   - Switch to the "Files & Annotations" tab
   - Verify that files are loaded from the related submission
   - Test the annotation functionality

## Debug Script

Created `debug-quality-reviews.sql` to help investigate data relationships and identify any orphaned reviews or missing submissions.

## Next Steps

1. Run the debug script to verify data integrity
2. Test the fixes in the application
3. Ensure that the FileAnnotationTool component works correctly with the loaded files
4. Consider adding better error handling for cases where submissions have no files

## Notes

- The FileAnnotationTool component appears to be well-implemented and should work correctly once files are properly fetched
- Added proper TypeScript types to handle nullable relationships
- Included fallback mechanisms for edge cases where data relationships might be incomplete
  };
  } | null;
  designer: {
    full_name: string;
    email: string;
  } | null;
  submission_id?: string;
  // ... other fields
}
```

## Testing Recommendations

1. **Test Reviews List**: Verify that project titles now display correctly instead of "Unknown Project"
2. **Test File Annotations**: 
   - Navigate to an individual review page
   - Switch to the "Files & Annotations" tab
   - Verify that files are loaded from the related submission
   - Test the annotation functionality

## Debug Script

Created `debug-quality-reviews.sql` to help investigate data relationships and identify any orphaned reviews or missing submissions.

## Next Steps

1. Run the debug script to verify data integrity
2. Test the fixes in the application
3. Ensure that the FileAnnotationTool component works correctly with the loaded files
4. Consider adding better error handling for cases where submissions have no files

## Notes

- The FileAnnotationTool component appears to be well-implemented and should work correctly once files are properly fetched
- Added proper TypeScript types to handle nullable relationships
- Included fallback mechanisms for edge cases where data relationships might be incomplete
  };
  } | null;
  designer: {
    full_name: string;
    email: string;
  } | null;
  submission_id?: string;
  // ... other fields
}
```

## Testing Recommendations

1. **Test Reviews List**: Verify that project titles now display correctly instead of "Unknown Project"
2. **Test File Annotations**: 
   - Navigate to an individual review page
   - Switch to the "Files & Annotations" tab
   - Verify that files are loaded from the related submission
   - Test the annotation functionality

## Debug Script

Created `debug-quality-reviews.sql` to help investigate data relationships and identify any orphaned reviews or missing submissions.

## Next Steps

1. Run the debug script to verify data integrity
2. Test the fixes in the application
3. Ensure that the FileAnnotationTool component works correctly with the loaded files
4. Consider adding better error handling for cases where submissions have no files

## Notes

- The FileAnnotationTool component appears to be well-implemented and should work correctly once files are properly fetched
- Added proper TypeScript types to handle nullable relationships
- Included fallback mechanisms for edge cases where data relationships might be incomplete
  };
  } | null;
  designer: {
    full_name: string;
    email: string;
  } | null;
  submission_id?: string;
  // ... other fields
}
```

## Testing Recommendations

1. **Test Reviews List**: Verify that project titles now display correctly instead of "Unknown Project"
2. **Test File Annotations**: 
   - Navigate to an individual review page
   - Switch to the "Files & Annotations" tab
   - Verify that files are loaded from the related submission
   - Test the annotation functionality

## Debug Script

Created `debug-quality-reviews.sql` to help investigate data relationships and identify any orphaned reviews or missing submissions.

## Next Steps

1. Run the debug script to verify data integrity
2. Test the fixes in the application
3. Ensure that the FileAnnotationTool component works correctly with the loaded files
4. Consider adding better error handling for cases where submissions have no files

## Notes

- The FileAnnotationTool component appears to be well-implemented and should work correctly once files are properly fetched
- Added proper TypeScript types to handle nullable relationships
- Included fallback mechanisms for edge cases where data relationships might be incomplete
  };
  } | null;
  designer: {
    full_name: string;
    email: string;
  } | null;
  submission_id?: string;
  // ... other fields
}
```

## Testing Recommendations

1. **Test Reviews List**: Verify that project titles now display correctly instead of "Unknown Project"
2. **Test File Annotations**: 
   - Navigate to an individual review page
   - Switch to the "Files & Annotations" tab
   - Verify that files are loaded from the related submission
   - Test the annotation functionality

## Debug Script

Created `debug-quality-reviews.sql` to help investigate data relationships and identify any orphaned reviews or missing submissions.

## Next Steps

1. Run the debug script to verify data integrity
2. Test the fixes in the application
3. Ensure that the FileAnnotationTool component works correctly with the loaded files
4. Consider adding better error handling for cases where submissions have no files

## Notes

- The FileAnnotationTool component appears to be well-implemented and should work correctly once files are properly fetched
- Added proper TypeScript types to handle nullable relationships
- Included fallback mechanisms for edge cases where data relationships might be incomplete
  };
  } | null;
  designer: {
    full_name: string;
    email: string;
  } | null;
  submission_id?: string;
  // ... other fields
}
```

## Testing Recommendations

1. **Test Reviews List**: Verify that project titles now display correctly instead of "Unknown Project"
2. **Test File Annotations**: 
   - Navigate to an individual review page
   - Switch to the "Files & Annotations" tab
   - Verify that files are loaded from the related submission
   - Test the annotation functionality

## Debug Script

Created `debug-quality-reviews.sql` to help investigate data relationships and identify any orphaned reviews or missing submissions.

## Next Steps

1. Run the debug script to verify data integrity
2. Test the fixes in the application
3. Ensure that the FileAnnotationTool component works correctly with the loaded files
4. Consider adding better error handling for cases where submissions have no files

## Notes

- The FileAnnotationTool component appears to be well-implemented and should work correctly once files are properly fetched
- Added proper TypeScript types to handle nullable relationships
- Included fallback mechanisms for edge cases where data relationships might be incomplete
  };
  } | null;
  designer: {
    full_name: string;
    email: string;
  } | null;
  submission_id?: string;
  // ... other fields
}
```

## Testing Recommendations

1. **Test Reviews List**: Verify that project titles now display correctly instead of "Unknown Project"
2. **Test File Annotations**: 
   - Navigate to an individual review page
   - Switch to the "Files & Annotations" tab
   - Verify that files are loaded from the related submission
   - Test the annotation functionality

## Debug Script

Created `debug-quality-reviews.sql` to help investigate data relationships and identify any orphaned reviews or missing submissions.

## Next Steps

1. Run the debug script to verify data integrity
2. Test the fixes in the application
3. Ensure that the FileAnnotationTool component works correctly with the loaded files
4. Consider adding better error handling for cases where submissions have no files

## Notes

- The FileAnnotationTool component appears to be well-implemented and should work correctly once files are properly fetched
- Added proper TypeScript types to handle nullable relationships
- Included fallback mechanisms for edge cases where data relationships might be incomplete
  };
  } | null;
  designer: {
    full_name: string;
    email: string;
  } | null;
  submission_id?: string;
  // ... other fields
}
```

## Testing Recommendations

1. **Test Reviews List**: Verify that project titles now display correctly instead of "Unknown Project"
2. **Test File Annotations**: 
   - Navigate to an individual review page
   - Switch to the "Files & Annotations" tab
   - Verify that files are loaded from the related submission
   - Test the annotation functionality

## Debug Script

Created `debug-quality-reviews.sql` to help investigate data relationships and identify any orphaned reviews or missing submissions.

## Next Steps

1. Run the debug script to verify data integrity
2. Test the fixes in the application
3. Ensure that the FileAnnotationTool component works correctly with the loaded files
4. Consider adding better error handling for cases where submissions have no files

## Notes

- The FileAnnotationTool component appears to be well-implemented and should work correctly once files are properly fetched
- Added proper TypeScript types to handle nullable relationships
- Included fallback mechanisms for edge cases where data relationships might be incomplete
  };
  } | null;
  designer: {
    full_name: string;
    email: string;
  } | null;
  submission_id?: string;
  // ... other fields
}
```

## Testing Recommendations

1. **Test Reviews List**: Verify that project titles now display correctly instead of "Unknown Project"
2. **Test File Annotations**: 
   - Navigate to an individual review page
   - Switch to the "Files & Annotations" tab
   - Verify that files are loaded from the related submission
   - Test the annotation functionality

## Debug Script

Created `debug-quality-reviews.sql` to help investigate data relationships and identify any orphaned reviews or missing submissions.

## Next Steps

1. Run the debug script to verify data integrity
2. Test the fixes in the application
3. Ensure that the FileAnnotationTool component works correctly with the loaded files
4. Consider adding better error handling for cases where submissions have no files

## Notes

- The FileAnnotationTool component appears to be well-implemented and should work correctly once files are properly fetched
- Added proper TypeScript types to handle nullable relationships
- Included fallback mechanisms for edge cases where data relationships might be incomplete
  };
  } | null;
  designer: {
    full_name: string;
    email: string;
  } | null;
  submission_id?: string;
  // ... other fields
}
```

## Testing Recommendations

1. **Test Reviews List**: Verify that project titles now display correctly instead of "Unknown Project"
2. **Test File Annotations**: 
   - Navigate to an individual review page
   - Switch to the "Files & Annotations" tab
   - Verify that files are loaded from the related submission
   - Test the annotation functionality

## Debug Script

Created `debug-quality-reviews.sql` to help investigate data relationships and identify any orphaned reviews or missing submissions.

## Next Steps

1. Run the debug script to verify data integrity
2. Test the fixes in the application
3. Ensure that the FileAnnotationTool component works correctly with the loaded files
4. Consider adding better error handling for cases where submissions have no files

## Notes

- The FileAnnotationTool component appears to be well-implemented and should work correctly once files are properly fetched
- Added proper TypeScript types to handle nullable relationships
- Included fallback mechanisms for edge cases where data relationships might be incomplete
  };
  } | null;
  designer: {
    full_name: string;
    email: string;
  } | null;
  submission_id?: string;
  // ... other fields
}
```

## Testing Recommendations

1. **Test Reviews List**: Verify that project titles now display correctly instead of "Unknown Project"
2. **Test File Annotations**: 
   - Navigate to an individual review page
   - Switch to the "Files & Annotations" tab
   - Verify that files are loaded from the related submission
   - Test the annotation functionality

## Debug Script

Created `debug-quality-reviews.sql` to help investigate data relationships and identify any orphaned reviews or missing submissions.

## Next Steps

1. Run the debug script to verify data integrity
2. Test the fixes in the application
3. Ensure that the FileAnnotationTool component works correctly with the loaded files
4. Consider adding better error handling for cases where submissions have no files

## Notes

- The FileAnnotationTool component appears to be well-implemented and should work correctly once files are properly fetched
- Added proper TypeScript types to handle nullable relationships
- Included fallback mechanisms for edge cases where data relationships might be incomplete
  };
  } | null;
  designer: {
    full_name: string;
    email: string;
  } | null;
  submission_id?: string;
  // ... other fields
}
```

## Testing Recommendations

1. **Test Reviews List**: Verify that project titles now display correctly instead of "Unknown Project"
2. **Test File Annotations**: 
   - Navigate to an individual review page
   - Switch to the "Files & Annotations" tab
   - Verify that files are loaded from the related submission
   - Test the annotation functionality

## Debug Script

Created `debug-quality-reviews.sql` to help investigate data relationships and identify any orphaned reviews or missing submissions.

## Next Steps

1. Run the debug script to verify data integrity
2. Test the fixes in the application
3. Ensure that the FileAnnotationTool component works correctly with the loaded files
4. Consider adding better error handling for cases where submissions have no files

## Notes

- The FileAnnotationTool component appears to be well-implemented and should work correctly once files are properly fetched
- Added proper TypeScript types to handle nullable relationships
- Included fallback mechanisms for edge cases where data relationships might be incomplete
  };
  } | null;
  designer: {
    full_name: string;
    email: string;
  } | null;
  submission_id?: string;
  // ... other fields
}
```

## Testing Recommendations

1. **Test Reviews List**: Verify that project titles now display correctly instead of "Unknown Project"
2. **Test File Annotations**: 
   - Navigate to an individual review page
   - Switch to the "Files & Annotations" tab
   - Verify that files are loaded from the related submission
   - Test the annotation functionality

## Debug Script

Created `debug-quality-reviews.sql` to help investigate data relationships and identify any orphaned reviews or missing submissions.

## Next Steps

1. Run the debug script to verify data integrity
2. Test the fixes in the application
3. Ensure that the FileAnnotationTool component works correctly with the loaded files
4. Consider adding better error handling for cases where submissions have no files

## Notes

- The FileAnnotationTool component appears to be well-implemented and should work correctly once files are properly fetched
- Added proper TypeScript types to handle nullable relationships
- Included fallback mechanisms for edge cases where data relationships might be incomplete
  };
  } | null;
  designer: {
    full_name: string;
    email: string;
  } | null;
  submission_id?: string;
  // ... other fields
}
```

## Testing Recommendations

1. **Test Reviews List**: Verify that project titles now display correctly instead of "Unknown Project"
2. **Test File Annotations**: 
   - Navigate to an individual review page
   - Switch to the "Files & Annotations" tab
   - Verify that files are loaded from the related submission
   - Test the annotation functionality

## Debug Script

Created `debug-quality-reviews.sql` to help investigate data relationships and identify any orphaned reviews or missing submissions.

## Next Steps

1. Run the debug script to verify data integrity
2. Test the fixes in the application
3. Ensure that the FileAnnotationTool component works correctly with the loaded files
4. Consider adding better error handling for cases where submissions have no files

## Notes

- The FileAnnotationTool component appears to be well-implemented and should work correctly once files are properly fetched
- Added proper TypeScript types to handle nullable relationships
- Included fallback mechanisms for edge cases where data relationships might be incomplete
  };
  } | null;
  designer: {
    full_name: string;
    email: string;
  } | null;
  submission_id?: string;
  // ... other fields
}
```

## Testing Recommendations

1. **Test Reviews List**: Verify that project titles now display correctly instead of "Unknown Project"
2. **Test File Annotations**: 
   - Navigate to an individual review page
   - Switch to the "Files & Annotations" tab
   - Verify that files are loaded from the related submission
   - Test the annotation functionality

## Debug Script

Created `debug-quality-reviews.sql` to help investigate data relationships and identify any orphaned reviews or missing submissions.

## Next Steps

1. Run the debug script to verify data integrity
2. Test the fixes in the application
3. Ensure that the FileAnnotationTool component works correctly with the loaded files
4. Consider adding better error handling for cases where submissions have no files

## Notes

- The FileAnnotationTool component appears to be well-implemented and should work correctly once files are properly fetched
- Added proper TypeScript types to handle nullable relationships
- Included fallback mechanisms for edge cases where data relationships might be incomplete
  };
  } | null;
  designer: {
    full_name: string;
    email: string;
  } | null;
  submission_id?: string;
  // ... other fields
}
```

## Testing Recommendations

1. **Test Reviews List**: Verify that project titles now display correctly instead of "Unknown Project"
2. **Test File Annotations**: 
   - Navigate to an individual review page
   - Switch to the "Files & Annotations" tab
   - Verify that files are loaded from the related submission
   - Test the annotation functionality

## Debug Script

Created `debug-quality-reviews.sql` to help investigate data relationships and identify any orphaned reviews or missing submissions.

## Next Steps

1. Run the debug script to verify data integrity
2. Test the fixes in the application
3. Ensure that the FileAnnotationTool component works correctly with the loaded files
4. Consider adding better error handling for cases where submissions have no files

## Notes

- The FileAnnotationTool component appears to be well-implemented and should work correctly once files are properly fetched
- Added proper TypeScript types to handle nullable relationships
- Included fallback mechanisms for edge cases where data relationships might be incomplete
}

export default function QualityReviewPage({ params }: { params: Promise<{ id: string }> }) {
  const { user, profile } = useOptimizedAuth();
  const resolvedParams = React.use(params);
  const [review, setReview] = useState<QualityReview | null>(null);
  const [standards, setStandards] = useState<QualityStandard[]>([]);
  const [feedback, setFeedback] = useState<Record<string, QualityFeedback>>({});
  const [overallScore, setOverallScore] = useState<number>(3);
  const [generalFeedback, setGeneralFeedback] = useState<string>('');
  const [revisionNotes, setRevisionNotes] = useState<string>('');
  const [loading, setLoading] = useState(true);
  const [submitting, setSubmitting] = useState(false);
  const [activeTab, setActiveTab] = useState("standards");
  const [files, setFiles] = useState<AnnotationFile[]>([]);
  const [selectedFile, setSelectedFile] = useState<AnnotationFile | null>(null);
  const [loadingFiles, setLoadingFiles] = useState(false);

  useEffect(() => {
    if (user && profile?.role === 'quality_team') {
      fetchReview();
      fetchStandards();
    }
  }, [user, profile, resolvedParams.id]);

  // Fetch files when review is loaded
  useEffect(() => {
    if (review && activeTab === 'files') {
      fetchAnnotationFiles();
    }
  }, [review, activeTab]);

  const fetchReview = async () => {
    try {
      console.log('🔍 [QUALITY REVIEW] Fetching review:', resolvedParams.id);

      // First, check if the review exists in quality_reviews_new
      const { data, error } = await supabase
        .from('quality_reviews_new')
        .select(`
          *,
          project:projects(title, description, budget, client:profiles!client_id(full_name)),
          designer:profiles!quality_reviews_new_designer_id_fkey(full_name, email)
        `)
        .eq('id', resolvedParams.id)
        .maybeSingle(); // Use maybeSingle() instead of single() to handle no results gracefully

      console.log('🔍 [QUALITY REVIEW] Query result:', { data, error });

      if (error) {
        console.error('❌ [QUALITY REVIEW] Database error:', error);
        throw error;
      }

      if (!data) {
        console.error('❌ [QUALITY REVIEW] Review not found with ID:', resolvedParams.id);
        throw new Error(`Quality review not found with ID: ${resolvedParams.id}`);
      }

      console.log('✅ [QUALITY REVIEW] Review found:', data.id);
      setReview(data);

      if (data.feedback) setGeneralFeedback(data.feedback);
      if (data.revision_notes) setRevisionNotes(data.revision_notes);
      if (data.overall_score) setOverallScore(data.overall_score);

      // Fetch existing feedback
      const { data: existingFeedback } = await supabase
        .from('quality_feedback')
        .select('*')
        .eq('review_id', resolvedParams.id);

      if (existingFeedback) {
        const feedbackMap: Record<string, QualityFeedback> = {};
        existingFeedback.forEach(item => {
          feedbackMap[item.standard_id] = {
            standard_id: item.standard_id,
            passed: item.passed,
            score: item.score,
            comments: item.comments || '',
            suggestions: item.suggestions || ''
          };
        });
        setFeedback(feedbackMap);
      }
    } catch (error) {
      console.error('❌ [QUALITY REVIEW] Error fetching review:', error);

      // Set a user-friendly error message
      if (error instanceof Error) {
        if (error.message.includes('not found')) {
          console.error('Review not found - this review may not exist in quality_reviews_new table');
          // Optionally redirect to quality dashboard
          // window.location.href = '/quality/dashboard';
        }
      }

      setLoading(false);
    }
  };

  const fetchStandards = async () => {
    try {
      const { data, error } = await supabase
        .from('quality_standards')
        .select('*')
        .order('category', { ascending: true })
        .order('weight', { ascending: false });

      if (error) throw error;

      // Parse criteria JSON
      const parsedData = (data || []).map(standard => {
        let criteria = [];
        try {
          if (typeof standard.criteria === 'string') {
            criteria = JSON.parse(standard.criteria);
          } else if (Array.isArray(standard.criteria)) {
            criteria = standard.criteria;
          }
        } catch (e) {
          console.error('Error parsing criteria for standard:', standard.id, e);
          criteria = [];
        }

        return {
          ...standard,
          criteria
        };
      });

      setStandards(parsedData);
    } catch (error) {
      console.error('Error fetching standards:', error);
    } finally {
      setLoading(false);
    }
  };

  const fetchAnnotationFiles = async () => {
    if (!review) return;

    setLoadingFiles(true);
    try {
      console.log('🔍 [FILE ANNOTATIONS] Fetching files for review:', {
        reviewId: review.id,
        submissionId: review.submission_id,
        projectId: review.project_id
      });

      const annotationFiles: AnnotationFile[] = [];

      // The relationship is: quality_reviews_new.submission_id -> project_submissions.id
      // So we need to fetch submissions using the submission_id from the review
      if (review.submission_id) {
        const { data: submissions, error: submissionsError } = await supabase
          .from('project_submissions')
          .select(`
            id,
            description,
            project_id,
            files,
            submitted_at,
            status,
            projects!inner(
              title,
              client_id,
              profiles!client_id(full_name)
            )
          `)
          .eq('id', review.submission_id)
          .order('submitted_at', { ascending: false });

        if (submissionsError) {
          console.error('❌ [FILE ANNOTATIONS] Error fetching submissions:', submissionsError);
          throw submissionsError;
        }

        console.log('✅ [FILE ANNOTATIONS] Submissions fetched:', submissions?.length || 0);

        // Debug: Environment and configuration
        console.log('🔧 [DEBUG] Cloudflare R2 Configuration:', {
          NEXT_PUBLIC_CLOUDFLARE_R2_PUBLIC_URL: process.env.NEXT_PUBLIC_CLOUDFLARE_R2_PUBLIC_URL,
          NODE_ENV: process.env.NODE_ENV
        });

        // Debug: Log submission details
        submissions?.forEach((submission, subIndex) => {
          console.log(`🔍 [FILE ANNOTATIONS] Submission ${subIndex}:`, {
            id: submission.id,
            files: submission.files,
            filesType: typeof submission.files,
            filesLength: Array.isArray(submission.files) ? submission.files.length : 'not array',
            projectTitle: (submission.projects as any)?.title
          });
        });

        // Process submissions and extract files
        submissions?.forEach(submission => {
          if (submission.files && Array.isArray(submission.files)) {
            console.log(`🔍 [FILE ANNOTATIONS] Processing ${submission.files.length} files from submission ${submission.id}`);

            submission.files.forEach((file: any, index: number) => {
              console.log(`🔍 [FILE ANNOTATIONS] File ${index}:`, {
                raw: file,
                name: file.name,
                fileName: file.fileName,
                url: file.url,
                fileUrl: file.fileUrl,
                path: file.path,
                key: file.key
              });

              const fileName = file.name || file.fileName || file.filename || `file-${index}`;
              
              // Enhanced URL construction for Cloudflare R2
              let fileUrl = null;
              
              // Try different URL properties
              if (file.url) {
                fileUrl = file.url;
              } else if (file.fileUrl) {
                fileUrl = file.fileUrl;
              } else if (file.path) {
                fileUrl = file.path;
              } else if (file.key) {
                // Construct Cloudflare R2 URL from key
                const r2BaseUrl = process.env.NEXT_PUBLIC_CLOUDFLARE_R2_PUBLIC_URL;
                if (r2BaseUrl) {
                  fileUrl = `${r2BaseUrl}/${file.key}`;
                } else {
                  console.warn('⚠️ [FILE ANNOTATIONS] NEXT_PUBLIC_CLOUDFLARE_R2_PUBLIC_URL not configured');
                }
              } else if (file.cloudflare_url) {
                fileUrl = file.cloudflare_url;
              } else if (file.storage_path) {
                const r2BaseUrl = process.env.NEXT_PUBLIC_CLOUDFLARE_R2_PUBLIC_URL;
                if (r2BaseUrl) {
                  fileUrl = `${r2BaseUrl}/${file.storage_path}`;
                } else {
                  console.warn('⚠️ [FILE ANNOTATIONS] NEXT_PUBLIC_CLOUDFLARE_R2_PUBLIC_URL not configured');
                }
              }

              console.log(`🔍 [FILE ANNOTATIONS] File ${index} URL construction:`, {
                fileName,
                originalFile: file,
                constructedUrl: fileUrl,
                r2BaseUrl: process.env.NEXT_PUBLIC_CLOUDFLARE_R2_PUBLIC_URL,
                availableProperties: Object.keys(file),
                fileSize: file.size || 'unknown',
                lastModified: file.lastModified || file.last_modified || 'unknown'
              });

              if (fileUrl && fileName) {
                const fileExtension = fileName.split('.').pop()?.toLowerCase() || '';
                const isAnnotatable = ['jpg', 'jpeg', 'png', 'gif', 'pdf', 'dwg', 'dxf'].includes(fileExtension);

                if (isAnnotatable) {
                  annotationFiles.push({
                    id: `submission-${submission.id}-${index}`,
                    file_url: fileUrl,
                    file_name: fileName,
                    file_type: getFileTypeFromUrl(fileUrl),
                    project_title: (submission.projects as any)?.title || 'Unknown Project',
                    review_id: review.id,
                    annotations_count: 0, // TODO: Count from annotations table
                    created_at: submission.submitted_at
                  });
                }
              }
            });
          }
        });
      } else {
        // Fallback: If no submission_id, try to find submissions by project_id
        console.log('⚠️ [FILE ANNOTATIONS] No submission_id in review, trying project_id fallback');
        
        const { data: submissions, error: submissionsError } = await supabase
          .from('project_submissions')
          .select(`
            id,
            description,
            project_id,
            files,
            submitted_at,
            status,
            projects!inner(
              title,
              client_id,
              profiles!client_id(full_name)
            )
          `)
          .eq('project_id', review.project_id)
          .order('submitted_at', { ascending: false })
          .limit(10); // Limit to recent submissions

        if (submissionsError) {
          console.error('❌ [FILE ANNOTATIONS] Error fetching submissions by project:', submissionsError);
          throw submissionsError;
        }

        console.log('✅ [FILE ANNOTATIONS] Fallback submissions fetched:', submissions?.length || 0);

        // Process submissions and extract files
        submissions?.forEach(submission => {
          if (submission.files && Array.isArray(submission.files)) {
            submission.files.forEach((file: any, index: number) => {
              console.log(`🔍 [FILE ANNOTATIONS] Raw file ${index}:`, JSON.stringify(file, null, 2));

              const fileName = file.name || file.fileName || file.filename || `file-${index}`;
              
              // Enhanced URL construction for Cloudflare R2
              let fileUrl = null;
              
              // Try different URL properties and log attempts
              if (file.url) {
                fileUrl = file.url;
                console.log(`✅ [FILE ANNOTATIONS] Using file.url: ${fileUrl}`);
              } else if (file.fileUrl) {
                fileUrl = file.fileUrl;
                console.log(`✅ [FILE ANNOTATIONS] Using file.fileUrl: ${fileUrl}`);
              } else if (file.path) {
                fileUrl = file.path;
                console.log(`✅ [FILE ANNOTATIONS] Using file.path: ${fileUrl}`);
              } else if (file.key) {
                // Construct Cloudflare R2 URL from key
                const r2BaseUrl = process.env.NEXT_PUBLIC_CLOUDFLARE_R2_PUBLIC_URL;
                if (r2BaseUrl) {
                  fileUrl = `${r2BaseUrl}/${file.key}`;
                  console.log(`✅ [FILE ANNOTATIONS] Constructed URL from key: ${fileUrl}`);
                } else {
                  console.warn('⚠️ [FILE ANNOTATIONS] NEXT_PUBLIC_CLOUDFLARE_R2_PUBLIC_URL not configured');
                }
              } else if (file.cloudflare_url) {
                fileUrl = file.cloudflare_url;
                console.log(`✅ [FILE ANNOTATIONS] Using file.cloudflare_url: ${fileUrl}`);
              } else if (file.storage_path) {
                const r2BaseUrl = process.env.NEXT_PUBLIC_CLOUDFLARE_R2_PUBLIC_URL;
                if (r2BaseUrl) {
                  fileUrl = `${r2BaseUrl}/${file.storage_path}`;
                  console.log(`✅ [FILE ANNOTATIONS] Constructed URL from storage_path: ${fileUrl}`);
                } else {
                  console.warn('⚠️ [FILE ANNOTATIONS] NEXT_PUBLIC_CLOUDFLARE_R2_PUBLIC_URL not configured');
                }
              } else {
                console.warn(`⚠️ [FILE ANNOTATIONS] No suitable URL property found for file ${index}:`, Object.keys(file));
              }

              if (fileUrl && fileName) {
                const fileExtension = fileName.split('.').pop()?.toLowerCase() || '';
                const isAnnotatable = ['jpg', 'jpeg', 'png', 'gif', 'pdf', 'dwg', 'dxf'].includes(fileExtension);

                if (isAnnotatable) {
                  annotationFiles.push({
                    id: `submission-${submission.id}-${index}`,
                    file_url: fileUrl,
                    file_name: fileName,
                    file_type: getFileTypeFromUrl(fileUrl),
                    project_title: (submission.projects as any)?.title || 'Unknown Project',
                    review_id: review.id,
                    annotations_count: 0, // TODO: Count from annotations table
                    created_at: submission.submitted_at
                  });
                }
              }
            });
          } else {
            console.log(`⚠️ [FILE ANNOTATIONS] Submission ${submission.id} has no files or files is not an array:`, {
              files: submission.files,
              type: typeof submission.files
            });
          }
        });
      }

      // Sort by creation date
      annotationFiles.sort((a, b) =>
        new Date(b.created_at).getTime() - new Date(a.created_at).getTime()
      );

      console.log('✅ [FILE ANNOTATIONS] Final processed files:', {
        count: annotationFiles.length,
        files: annotationFiles.map(f => ({ name: f.file_name, url: f.file_url, type: f.file_type }))
      });
      setFiles(annotationFiles);

      // Auto-select first file if available
      if (annotationFiles.length > 0 && !selectedFile) {
        setSelectedFile(annotationFiles[0]);
      }

    } catch (error) {
      console.error('❌ [FILE ANNOTATIONS] Error fetching files:', error);
      setFiles([]);
    } finally {
      setLoadingFiles(false);
    }
  };

  const getFileTypeFromUrl = (url: string): string => {
    const extension = url.split('.').pop()?.toLowerCase() || '';
    const imageTypes = ['jpg', 'jpeg', 'png', 'gif'];
    const documentTypes = ['pdf'];
    const cadTypes = ['dwg', 'dxf'];

    if (imageTypes.includes(extension)) return 'image';
    if (documentTypes.includes(extension)) return 'document';
    if (cadTypes.includes(extension)) return 'cad';
    return 'unknown';
  };

  const handleSaveAnnotations = async (annotations: any[]) => {
    try {
      console.log('💾 [FILE ANNOTATIONS] Saving annotations:', annotations);
      // This will be handled by the FileAnnotationTool component
      // The component already has the API integration
    } catch (error) {
      console.error('❌ [FILE ANNOTATIONS] Error saving annotations:', error);
    }
  };

  const updateFeedback = (standardId: string, updates: Partial<QualityFeedback>) => {
    setFeedback(prev => ({
      ...prev,
      [standardId]: {
        ...prev[standardId],
        standard_id: standardId,
        passed: prev[standardId]?.passed ?? true,
        score: prev[standardId]?.score ?? 3,
        comments: prev[standardId]?.comments ?? '',
        suggestions: prev[standardId]?.suggestions ?? '',
        ...updates
      }
    }));
  };

  const saveReview = async (status: 'approved' | 'rejected' | 'needs_revision') => {
    if (!review) return;
    
    setSubmitting(true);
    try {
      // Update review
      const { error: reviewError } = await supabase
        .from('quality_reviews_new')
        .update({
          status,
          overall_score: overallScore,
          feedback: generalFeedback,
          revision_notes: revisionNotes,
          reviewed_at: new Date().toISOString(),
          revision_count: status === 'needs_revision' ? review.revision_count + 1 : review.revision_count
        })
        .eq('id', resolvedParams.id);

      if (reviewError) throw reviewError;

      // Save feedback for each standard
      for (const [standardId, feedbackItem] of Object.entries(feedback)) {
        const { error: feedbackError } = await supabase
          .from('quality_feedback')
          .upsert({
            review_id: resolvedParams.id,
            standard_id: standardId,
            passed: feedbackItem.passed,
            score: feedbackItem.score,
            comments: feedbackItem.comments,
            suggestions: feedbackItem.suggestions
          });

        if (feedbackError) throw feedbackError;
      }

      // Create notifications based on status
      if (status === 'approved') {
        // Notify designer of approval
        await supabase.from('workflow_notifications').insert({
          recipient_id: review.designer_id,
          notification_type: 'quality_approved',
          title: 'Quality Review Approved',
          message: `Your submission for "${review.project.title}" has been approved by the quality team.`,
          priority: 'normal'
        });
      } else if (status === 'rejected' || status === 'needs_revision') {
        // Notify designer of rejection/revision needed
        await supabase.from('workflow_notifications').insert({
          recipient_id: review.designer_id,
          notification_type: 'quality_revision_needed',
          title: 'Revision Required',
          message: `Your submission for "${review.project.title}" requires revision. Please check the feedback and resubmit.`,
          priority: 'high'
        });
      }

      // Redirect back to dashboard
      window.location.href = '/quality/dashboard';
    } catch (error) {
      console.error('Error saving review:', error);
      alert('Error saving review. Please try again.');
    } finally {
      setSubmitting(false);
    }
  };

  const calculateOverallProgress = () => {
    const totalStandards = standards.length;
    const reviewedStandards = Object.keys(feedback).length;
    return totalStandards > 0 ? (reviewedStandards / totalStandards) * 100 : 0;
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <RefreshCw className="h-8 w-8 animate-spin text-brown-600" />
      </div>
    );
  }

  if (!review) {
    return (
      <div className="max-w-4xl mx-auto p-6">
        <div className="text-center py-12">
          <div className="mb-4">
            <svg className="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
            </svg>
          </div>
          <h3 className="text-lg font-medium text-gray-900 mb-2">Quality Review Not Found</h3>
          <p className="text-gray-600 mb-4">
            The quality review with ID <code className="bg-gray-100 px-2 py-1 rounded text-sm">{resolvedParams.id}</code> could not be found.
          </p>
          <p className="text-sm text-gray-500 mb-6">
            This review may not exist in the system or may have been moved to a different location.
          </p>
          <div className="space-x-4">
            <button
              onClick={() => window.location.href = '/quality/dashboard'}
              className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-primary hover:bg-primary/90 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary"
            >
              Go to Quality Dashboard
            </button>
            <button
              onClick={() => window.location.href = '/quality/submissions'}
              className="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary"
            >
              View Submissions
            </button>
          </div>
        </div>
      </div>
    );
  }

  if (!review) {
    return (
      <div className="max-w-4xl mx-auto p-6">
        <div className="text-center">
          <AlertTriangle className="h-12 w-12 text-red-500 mx-auto mb-4" />
          <h1 className="text-2xl font-bold text-gray-900 mb-2">Review Not Found</h1>
          <p className="text-gray-600 mb-4">The quality review you're looking for doesn't exist.</p>
          <Button onClick={() => window.location.href = '/quality/dashboard'}>
            Back to Dashboard
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-7xl mx-auto p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center gap-4">
        <Button
          variant="outline"
          onClick={() => window.location.href = '/quality/dashboard'}
          className="flex items-center gap-2"
        >
          <ArrowLeft className="h-4 w-4" />
          Back
        </Button>
        <div className="flex-1">
          <h1 className="text-3xl font-bold text-gray-900">Quality Review</h1>
          <p className="text-gray-600 mt-1">
            {review.project?.title} - {review.designer?.full_name}
          </p>
        </div>
        <div className="flex items-center gap-2">
          <Badge variant={review.status === 'approved' ? 'default' : 'secondary'}>
            {review.status}
          </Badge>
          <Badge variant="outline">
            {review.priority || 'normal'} priority
          </Badge>
        </div>
      </div>

      {/* Tabbed Interface */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="standards" className="flex items-center gap-2">
            <ClipboardList className="h-4 w-4" />
            Quality Standards
          </TabsTrigger>
          <TabsTrigger value="files" className="flex items-center gap-2">
            <FileImage className="h-4 w-4" />
            Files & Annotations
          </TabsTrigger>
          <TabsTrigger value="assessment" className="flex items-center gap-2">
            <BarChart3 className="h-4 w-4" />
            Overall Assessment
          </TabsTrigger>
        </TabsList>

        {/* Tab 1: Quality Standards */}
        <TabsContent value="standards" className="space-y-6 mt-6">
          {/* Project Info */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <FileText className="h-5 w-5" />
                Project Information
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <h3 className="text-lg font-medium text-gray-900 mb-2">{review.project.title}</h3>
                  <p className="text-gray-600 mb-4">{review.project.description}</p>
                  <div className="space-y-2 text-sm">
                    <div className="flex items-center gap-2">
                      <User className="h-4 w-4 text-gray-400" />
                      <span className="font-medium">Client:</span> {review.project.client.full_name}
                    </div>
                    <div className="flex items-center gap-2">
                      <User className="h-4 w-4 text-gray-400" />
                      <span className="font-medium">Designer:</span> {review.designer.full_name}
                    </div>
                    <div className="flex items-center gap-2">
                      <Calendar className="h-4 w-4 text-gray-400" />
                      <span className="font-medium">Submitted:</span> {new Date(review.created_at).toLocaleDateString()}
                    </div>
                  </div>
                </div>
                <div>
                  <div className="bg-gray-50 rounded-lg p-4">
                    <h4 className="font-medium text-gray-900 mb-2">Review Progress</h4>
                    <div className="w-full bg-gray-200 rounded-full h-2 mb-2">
                      <div
                        className="bg-brown-600 h-2 rounded-full transition-all duration-300"
                        style={{ width: `${calculateOverallProgress()}%` }}
                      ></div>
                    </div>
                    <p className="text-sm text-gray-600">
                      {Object.keys(feedback).length} of {standards.length} standards reviewed
                    </p>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Quality Standards Review */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <ClipboardList className="h-5 w-5" />
                Quality Standards Checklist
              </CardTitle>
              <p className="text-gray-600 mt-1">Review each standard and provide feedback</p>
            </CardHeader>
            <CardContent className="p-0">
              <div className="divide-y divide-gray-200">
                {standards.map((standard) => (
                  <div key={standard.id} className="p-6">
                    <div className="flex items-start justify-between mb-4">
                      <div className="flex-1">
                        <div className="flex items-center gap-3 mb-2">
                          <h3 className="text-lg font-semibold text-gray-900">{standard.standard_name}</h3>
                          {standard.is_mandatory && (
                            <span className="inline-flex items-center px-2 py-1 text-xs font-medium bg-red-100 text-red-800 rounded">
                              Mandatory
                            </span>
                          )}
                          <span className="inline-flex items-center px-2 py-1 text-xs font-medium bg-blue-100 text-blue-800 rounded">
                            {standard.category}
                          </span>
                        </div>
                        <p className="text-gray-600 mb-3">{standard.description}</p>
                        <div className="bg-gray-50 rounded-lg p-3">
                          <h4 className="font-medium text-gray-900 mb-2">Criteria:</h4>
                          <ul className="list-disc list-inside space-y-1 text-sm text-gray-600">
                            {Array.isArray(standard.criteria) ? standard.criteria.map((criterion, index) => (
                              <li key={index}>
                                {typeof criterion === 'object' && criterion && (criterion as any).criterion
                                  ? (criterion as any).criterion
                                  : typeof criterion === 'string'
                                  ? criterion
                                  : 'Criterion'}
                              </li>
                            )) : (
                              <li>No criteria defined</li>
                            )}
                          </ul>
                        </div>
                      </div>
                    </div>

                    <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          Pass/Fail Status
                        </label>
                        <div className="flex gap-3">
                          <Button
                            variant={feedback[standard.id]?.passed === true ? "default" : "outline"}
                            size="sm"
                            onClick={() => updateFeedback(standard.id, { passed: true })}
                            className="flex items-center gap-2"
                          >
                            <CheckCircle className="h-4 w-4" />
                            Pass
                          </Button>
                          <Button
                            variant={feedback[standard.id]?.passed === false ? "default" : "outline"}
                            size="sm"
                            onClick={() => updateFeedback(standard.id, { passed: false })}
                            className="flex items-center gap-2"
                          >
                            <XCircle className="h-4 w-4" />
                            Fail
                          </Button>
                        </div>

                        <label className="block text-sm font-medium text-gray-700 mt-4 mb-2">
                          Score (1-5)
                        </label>
                        <div className="flex gap-2">
                          {[1, 2, 3, 4, 5].map((score) => (
                            <Button
                              key={score}
                              variant={feedback[standard.id]?.score === score ? "default" : "outline"}
                              size="sm"
                              onClick={() => updateFeedback(standard.id, { score })}
                              className="w-10 h-10 p-0"
                            >
                              {score}
                            </Button>
                          ))}
                        </div>
                      </div>

                      <div className="space-y-4">
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-2">
                            Comments
                          </label>
                          <textarea
                            value={feedback[standard.id]?.comments || ''}
                            onChange={(e) => updateFeedback(standard.id, { comments: e.target.value })}
                            placeholder="Provide specific feedback about this standard..."
                            className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-brown-500 focus:border-brown-500"
                            rows={3}
                          />
                        </div>

                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-2">
                            Suggestions for Improvement
                          </label>
                          <textarea
                            value={feedback[standard.id]?.suggestions || ''}
                            onChange={(e) => updateFeedback(standard.id, { suggestions: e.target.value })}
                            placeholder="Suggest specific improvements..."
                            className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-brown-500 focus:border-brown-500"
                            rows={2}
                          />
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Tab 2: Files & Annotations */}
        <TabsContent value="files" className="space-y-6 mt-6">
          <div className="grid grid-cols-1 xl:grid-cols-4 gap-6">
            {/* Files List */}
            <Card className="xl:col-span-1">
              <CardHeader className="pb-3">
                <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-2">
                  <CardTitle className="flex items-center gap-2 text-sm sm:text-base">
                    <FileImage className="h-4 w-4 sm:h-5 sm:w-5" />
                    <span className="truncate">Files to Review</span>
                  </CardTitle>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={fetchAnnotationFiles}
                    disabled={loadingFiles}
                    className="self-start sm:self-auto"
                  >
                    <RefreshCw className={`h-4 w-4 ${loadingFiles ? 'animate-spin' : ''}`} />
                    <span className="ml-1 hidden sm:inline">Refresh</span>
                  </Button>
                </div>
              </CardHeader>
              <CardContent className="p-0">
                {loadingFiles ? (
                  <div className="flex items-center justify-center py-8">
                    <RefreshCw className="h-6 w-6 text-gray-400 animate-spin" />
                  </div>
                ) : files.length === 0 ? (
                  <div className="text-center py-8 text-gray-500">
                    <FileImage className="h-12 w-12 mx-auto mb-4" />
                    <p>No files to annotate</p>
                    <p className="text-sm mt-2">Files will appear when designers submit work for review</p>
                  </div>
                ) : (
                  <div className="space-y-1">
                    {files.map((file) => (
                      <button
                        key={file.id}
                        onClick={() => setSelectedFile(file)}
                        className={`w-full text-left p-3 sm:p-4 hover:bg-gray-50 border-b transition-colors ${
                          selectedFile?.id === file.id ? 'bg-purple-50 border-purple-200' : ''
                        }`}
                      >
                        <div className="flex items-start gap-2 sm:gap-3">
                          <FileImage className="h-4 w-4 sm:h-5 sm:w-5 text-purple-500 mt-1 flex-shrink-0" />
                          <div className="min-w-0 flex-1">
                            <p className="text-sm font-medium text-gray-900 truncate">{file.file_name}</p>
                            <p className="text-xs text-gray-500 truncate">{file.project_title}</p>
                            <div className="flex items-center gap-2 mt-1">
                              <Badge variant="outline" className="text-xs">
                                {file.file_type}
                              </Badge>
                              {file.annotations_count > 0 && (
                                <Badge variant="secondary" className="text-xs">
                                  {file.annotations_count} annotations
                                </Badge>
                              )}
                            </div>
                          </div>
                        </div>
                      </button>
                    ))}
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Annotation Tool */}
            <div className="xl:col-span-3">
              {selectedFile ? (
                <div className="h-full">
                  <FileAnnotationTool
                    fileUrl={selectedFile.file_url}
                    fileName={selectedFile.file_name}
                    fileType={selectedFile.file_type}
                    existingAnnotations={[]} // Loaded automatically by the component
                    onSaveAnnotations={handleSaveAnnotations}
                    readOnly={false}
                    reviewerName={profile?.full_name || 'Quality Reviewer'}
                    qualityReviewId={selectedFile.review_id}
                    submissionId={selectedFile.id.split('-')[1]} // Extract submission ID from composite ID
                    submissionType={selectedFile.id.startsWith('work-') ? 'work' : 'project'}
                  />
                </div>
              ) : (
                <Card className="h-full min-h-[400px] sm:min-h-[500px]">
                  <CardContent className="flex items-center justify-center h-full p-6">
                    <div className="text-center text-gray-500">
                      <FileImage className="h-16 w-16 mx-auto mb-4" />
                      <h3 className="text-lg font-medium mb-2">Select a file to annotate</h3>
                      <p className="text-sm">Choose a file from the list to start adding annotations and feedback</p>
                    </div>
                  </CardContent>
                </Card>
              )}
            </div>
          </div>
        </TabsContent>

        {/* Tab 3: Overall Assessment */}
        <TabsContent value="assessment" className="space-y-6 mt-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <BarChart3 className="h-5 w-5" />
                Overall Assessment
              </CardTitle>
              <p className="text-gray-600">Provide your final assessment and decision</p>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* Overall Score */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-3">
                  Overall Score (1-5)
                </label>
                <div className="flex gap-2 mb-4">
                  {[1, 2, 3, 4, 5].map((score) => (
                    <Button
                      key={score}
                      variant={overallScore === score ? "default" : "outline"}
                      size="lg"
                      onClick={() => setOverallScore(score)}
                      className="w-12 h-12 p-0 flex items-center justify-center"
                    >
                      <Star className={`h-5 w-5 ${overallScore >= score ? 'fill-current' : ''}`} />
                    </Button>
                  ))}
                </div>
              </div>

              {/* General Feedback */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  General Feedback
                </label>
                <Textarea
                  value={generalFeedback}
                  onChange={(e) => setGeneralFeedback(e.target.value)}
                  placeholder="Provide overall feedback about the submission..."
                  className="min-h-[120px]"
                />
              </div>

              {/* Revision Notes */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Revision Notes (if applicable)
                </label>
                <Textarea
                  value={revisionNotes}
                  onChange={(e) => setRevisionNotes(e.target.value)}
                  placeholder="Specific notes for revisions..."
                  className="min-h-[100px]"
                />
              </div>

              {/* Action Buttons */}
              <div className="flex flex-wrap gap-3 pt-4 border-t">
                <Button
                  variant="outline"
                  onClick={() => saveReview('needs_revision')}
                  disabled={submitting}
                  className="flex items-center gap-2 border-yellow-200 text-yellow-600 hover:bg-yellow-50"
                >
                  <MessageSquare className="h-4 w-4" />
                  Request Revision
                </Button>

                <Button
                  variant="outline"
                  onClick={() => saveReview('rejected')}
                  disabled={submitting}
                  className="flex items-center gap-2 border-red-200 text-red-600 hover:bg-red-50"
                >
                  <XCircle className="h-4 w-4" />
                  Reject Submission
                </Button>

                <Button
                  onClick={() => saveReview('approved')}
                  disabled={submitting}
                  className="flex items-center gap-2 bg-green-600 hover:bg-green-700"
                >
                  <CheckCircle className="h-4 w-4" />
                  Approve Submission
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
