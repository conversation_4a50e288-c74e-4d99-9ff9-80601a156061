-- Debug script to check quality reviews and submissions relationships

-- Check the structure of quality_reviews_new table
SELECT 
    'quality_reviews_new structure' as check_name,
    COUNT(*) as total_reviews,
    COUNT(DISTINCT project_id) as unique_projects,
    COUNT(DISTINCT submission_id) as unique_submissions,
    COUNT(CASE WHEN submission_id IS NOT NULL THEN 1 END) as reviews_with_submissions
FROM quality_reviews_new;

-- Check sample quality reviews with their relationships
SELECT 
    qr.id,
    qr.status,
    qr.submission_id,
    qr.project_id,
    p.title as project_title,
    ps.id as submission_id,
    CASE 
        WHEN ps.files IS NOT NULL THEN jsonb_array_length(ps.files::jsonb)
        ELSE 0 
    END as file_count
FROM quality_reviews_new qr
LEFT JOIN projects p ON p.id = qr.project_id
LEFT JOIN project_submissions ps ON ps.id = qr.submission_id
ORDER BY qr.created_at DESC
LIMIT 10;

-- Check project_submissions table
SELECT 
    'project_submissions structure' as check_name,
    COUNT(*) as total_submissions,
    COUNT(CASE WHEN files IS NOT NULL AND files != '[]' THEN 1 END) as submissions_with_files,
    COUNT(DISTINCT project_id) as unique_projects
FROM project_submissions;

-- Check sample submissions with files
SELECT 
    ps.id,
    ps.project_id,
    p.title as project_title,
    CASE 
        WHEN ps.files IS NOT NULL THEN jsonb_array_length(ps.files::jsonb)
        ELSE 0 
    END as file_count,
    ps.submitted_at
FROM project_submissions ps
LEFT JOIN projects p ON p.id = ps.project_id
WHERE ps.files IS NOT NULL AND ps.files != '[]'
ORDER BY ps.submitted_at DESC
LIMIT 5;

-- Check for orphaned reviews (reviews without submissions)
SELECT 
    'orphaned_reviews' as check_name,
    COUNT(*) as count
FROM quality_reviews_new qr
WHERE qr.submission_id IS NULL;

-- Check for reviews that should have files
SELECT 
    qr.id as review_id,
    qr.status,
    qr.project_id,
    p.title as project_title,
    qr.submission_id,
    ps.id as actual_submission_id,
    CASE 
        WHEN ps.files IS NOT NULL THEN jsonb_array_length(ps.files::jsonb)
        ELSE 0 
    END as file_count
FROM quality_reviews_new qr
LEFT JOIN projects p ON p.id = qr.project_id
LEFT JOIN project_submissions ps ON ps.id = qr.submission_id
WHERE qr.status IN ('pending', 'in_review')
ORDER BY qr.created_at DESC
LIMIT 10;
