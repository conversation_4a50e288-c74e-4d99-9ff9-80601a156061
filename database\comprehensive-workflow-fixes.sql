-- =====================================================
-- COMPREHENSIVE WORKFLOW FIXES
-- Fixes deposit payment workflow, file storage, and quality team issues
-- Run this in Supabase Dashboard SQL Editor
-- =====================================================

-- 1. ADD MISSING COLUMNS TO PROJECTS TABLE
ALTER TABLE projects ADD COLUMN IF NOT EXISTS proposal_id UUID REFERENCES project_proposals_enhanced(id);
ALTER TABLE projects ADD COLUMN IF NOT EXISTS brief_id UUID REFERENCES project_briefs(id);
ALTER TABLE projects ADD COLUMN IF NOT EXISTS started_at TIMESTAMP WITH TIME ZONE;

-- Add new project statuses for deposit workflow
DO $$
BEGIN
    -- Drop existing constraint if it exists
    ALTER TABLE projects DROP CONSTRAINT IF EXISTS projects_status_check;
    
    -- Add new constraint with additional statuses
    ALTER TABLE projects ADD CONSTRAINT projects_status_check 
    CHECK (status IN (
        'draft', 'pending', 'pending_deposit', 'active', 'in_progress', 
        'completed', 'cancelled', 'on_hold', 'revision_requested'
    ));
EXCEPTION
    WHEN duplicate_object THEN NULL;
END $$;

-- 2. ADD MISSING COLUMNS TO PROJECT_MILESTONES TABLE
ALTER TABLE project_milestones ADD COLUMN IF NOT EXISTS paid_at TIMESTAMP WITH TIME ZONE;
ALTER TABLE project_milestones ADD COLUMN IF NOT EXISTS payment_id UUID;

-- Update milestone statuses to include 'paid'
DO $$
BEGIN
    -- Drop existing constraint if it exists
    ALTER TABLE project_milestones DROP CONSTRAINT IF EXISTS project_milestones_status_check;
    
    -- Add new constraint with additional statuses
    ALTER TABLE project_milestones ADD CONSTRAINT project_milestones_status_check 
    CHECK (status IN (
        'pending', 'active', 'in_progress', 'completed', 'approved', 'paid', 'inactive'
    ));
EXCEPTION
    WHEN duplicate_object THEN NULL;
END $$;

-- 3. ENSURE PAYMENTS TABLE EXISTS WITH PROPER STRUCTURE
CREATE TABLE IF NOT EXISTS payments (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    project_id UUID REFERENCES projects(id) ON DELETE CASCADE,
    milestone_id UUID REFERENCES project_milestones(id) ON DELETE CASCADE,
    client_id UUID REFERENCES profiles(id),
    designer_id UUID REFERENCES profiles(id),
    amount DECIMAL(10,2) NOT NULL,
    currency VARCHAR(3) DEFAULT 'USD',
    payment_method VARCHAR(50) NOT NULL, -- 'paypal', 'stripe', 'tappay', 'clickpay'
    status VARCHAR(50) DEFAULT 'pending' CHECK (status IN ('pending', 'processing', 'completed', 'failed', 'cancelled', 'refunded')),
    transaction_id VARCHAR(255), -- External payment provider transaction ID
    payment_type VARCHAR(50) DEFAULT 'milestone' CHECK (payment_type IN ('deposit', 'milestone', 'final', 'bonus')),
    description TEXT,
    metadata JSONB, -- Store payment provider specific data
    completed_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Add indexes for payments table
CREATE INDEX IF NOT EXISTS idx_payments_project_id ON payments(project_id);
CREATE INDEX IF NOT EXISTS idx_payments_milestone_id ON payments(milestone_id);
CREATE INDEX IF NOT EXISTS idx_payments_status ON payments(status);
CREATE INDEX IF NOT EXISTS idx_payments_payment_type ON payments(payment_type);
CREATE INDEX IF NOT EXISTS idx_payments_transaction_id ON payments(transaction_id);

-- 4. ENSURE ESCROW_TRANSACTIONS TABLE EXISTS
CREATE TABLE IF NOT EXISTS escrow_transactions (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    project_id UUID REFERENCES projects(id) ON DELETE CASCADE,
    milestone_id UUID REFERENCES project_milestones(id) ON DELETE CASCADE,
    payment_id UUID REFERENCES payments(id),
    client_id UUID REFERENCES profiles(id),
    designer_id UUID REFERENCES profiles(id),
    amount DECIMAL(10,2) NOT NULL,
    currency VARCHAR(3) DEFAULT 'USD',
    status VARCHAR(50) DEFAULT 'held' CHECK (status IN ('held', 'released', 'disputed', 'refunded')),
    transaction_type VARCHAR(50) DEFAULT 'milestone' CHECK (transaction_type IN ('deposit', 'milestone', 'final', 'bonus')),
    description TEXT,
    held_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    released_at TIMESTAMP WITH TIME ZONE,
    released_by UUID REFERENCES profiles(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Add indexes for escrow_transactions table
CREATE INDEX IF NOT EXISTS idx_escrow_project_id ON escrow_transactions(project_id);
CREATE INDEX IF NOT EXISTS idx_escrow_milestone_id ON escrow_transactions(milestone_id);
CREATE INDEX IF NOT EXISTS idx_escrow_status ON escrow_transactions(status);

-- 5. FIX PROJECT_SUBMISSIONS TABLE STRUCTURE
-- Ensure project_submissions table has the correct structure
DO $$
BEGIN
    -- Check if project_submissions table exists
    IF EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'project_submissions') THEN
        -- Add missing columns if they don't exist
        ALTER TABLE project_submissions ADD COLUMN IF NOT EXISTS quality_review_id UUID;
        ALTER TABLE project_submissions ADD COLUMN IF NOT EXISTS milestone_id UUID REFERENCES project_milestones(id);
        
        -- Update status constraint to include quality review statuses
        ALTER TABLE project_submissions DROP CONSTRAINT IF EXISTS project_submissions_status_check;
        ALTER TABLE project_submissions ADD CONSTRAINT project_submissions_status_check 
        CHECK (status IN (
            'submitted', 'under_review', 'under_quality_review', 'approved', 
            'needs_revision', 'rejected', 'quality_approved', 'quality_rejected'
        ));
    ELSE
        -- Create project_submissions table if it doesn't exist
        CREATE TABLE project_submissions (
            id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
            project_id UUID REFERENCES projects(id) ON DELETE CASCADE,
            milestone_id UUID REFERENCES project_milestones(id) ON DELETE CASCADE,
            designer_id UUID REFERENCES profiles(id),
            submission_type VARCHAR(50) DEFAULT 'milestone' CHECK (submission_type IN ('milestone', 'revision', 'final')),
            status VARCHAR(50) DEFAULT 'submitted' CHECK (status IN (
                'submitted', 'under_review', 'under_quality_review', 'approved', 
                'needs_revision', 'rejected', 'quality_approved', 'quality_rejected'
            )),
            files JSONB, -- Array of file URLs from R2
            description TEXT,
            quality_review_id UUID,
            submitted_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
            reviewed_at TIMESTAMP WITH TIME ZONE,
            approved_at TIMESTAMP WITH TIME ZONE,
            created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
            updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        );
        
        -- Add indexes
        CREATE INDEX IF NOT EXISTS idx_project_submissions_project_id ON project_submissions(project_id);
        CREATE INDEX IF NOT EXISTS idx_project_submissions_milestone_id ON project_submissions(milestone_id);
        CREATE INDEX IF NOT EXISTS idx_project_submissions_designer_id ON project_submissions(designer_id);
        CREATE INDEX IF NOT EXISTS idx_project_submissions_status ON project_submissions(status);
        CREATE INDEX IF NOT EXISTS idx_project_submissions_quality_review_id ON project_submissions(quality_review_id);
    END IF;
END $$;

-- 6. ADD FOREIGN KEY CONSTRAINT FOR QUALITY REVIEWS
-- Add foreign key constraint between file_annotations and quality_reviews_new
DO $$
BEGIN
    -- Add foreign key constraint if it doesn't exist
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.table_constraints 
        WHERE constraint_name = 'file_annotations_quality_review_id_fkey'
    ) THEN
        ALTER TABLE file_annotations 
        ADD CONSTRAINT file_annotations_quality_review_id_fkey 
        FOREIGN KEY (quality_review_id) REFERENCES quality_reviews_new(id) ON DELETE CASCADE;
    END IF;
    
    -- Add foreign key constraint for project_submissions
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.table_constraints 
        WHERE constraint_name = 'project_submissions_quality_review_id_fkey'
    ) THEN
        ALTER TABLE project_submissions 
        ADD CONSTRAINT project_submissions_quality_review_id_fkey 
        FOREIGN KEY (quality_review_id) REFERENCES quality_reviews_new(id) ON DELETE SET NULL;
    END IF;
EXCEPTION
    WHEN others THEN
        RAISE NOTICE 'Foreign key constraints may already exist or tables may not be ready';
END $$;

-- 7. CREATE NOTIFICATIONS TABLE IF NOT EXISTS
CREATE TABLE IF NOT EXISTS notifications (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES profiles(id) ON DELETE CASCADE,
    title VARCHAR(255) NOT NULL,
    message TEXT NOT NULL,
    type VARCHAR(50) DEFAULT 'info' CHECK (type IN ('info', 'success', 'warning', 'error', 'project_started', 'payment_received', 'milestone_completed')),
    related_id UUID, -- Can reference projects, payments, etc.
    read BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Add indexes for notifications
CREATE INDEX IF NOT EXISTS idx_notifications_user_id ON notifications(user_id);
CREATE INDEX IF NOT EXISTS idx_notifications_read ON notifications(read);
CREATE INDEX IF NOT EXISTS idx_notifications_type ON notifications(type);

-- 8. UPDATE EXISTING PROJECTS TO HANDLE DEPOSIT WORKFLOW
-- Update projects that don't have deposit payments to pending_deposit status
UPDATE projects 
SET status = 'pending_deposit' 
WHERE status = 'draft' 
AND id IN (
    SELECT p.id 
    FROM projects p
    JOIN project_milestones pm ON p.id = pm.project_id
    WHERE pm.order_index = 0 
    AND pm.status = 'pending'
    AND pm.paid_at IS NULL
);

-- 9. CREATE FUNCTION TO AUTOMATICALLY CREATE QUALITY REVIEW ON SUBMISSION
CREATE OR REPLACE FUNCTION create_quality_review_on_submission()
RETURNS TRIGGER AS $$
BEGIN
    -- Only create quality review for submitted status
    IF NEW.status = 'submitted' AND (OLD.status IS NULL OR OLD.status != 'submitted') THEN
        INSERT INTO quality_reviews_new (
            project_id,
            submission_id,
            milestone_id,
            designer_id,
            review_type,
            status,
            priority,
            created_at
        ) VALUES (
            NEW.project_id,
            NEW.id,
            NEW.milestone_id,
            NEW.designer_id,
            'submission',
            'pending',
            'medium',
            NOW()
        );
        
        -- Update submission with quality review status
        NEW.status = 'under_quality_review';
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger for automatic quality review creation
DROP TRIGGER IF EXISTS trigger_create_quality_review ON project_submissions;
CREATE TRIGGER trigger_create_quality_review
    BEFORE INSERT OR UPDATE ON project_submissions
    FOR EACH ROW
    EXECUTE FUNCTION create_quality_review_on_submission();

-- 10. CREATE FUNCTION TO UPDATE PROJECT STATUS ON DEPOSIT PAYMENT
CREATE OR REPLACE FUNCTION update_project_on_deposit_payment()
RETURNS TRIGGER AS $$
BEGIN
    -- Check if this is a deposit payment being marked as completed
    IF NEW.payment_type = 'deposit' AND NEW.status = 'completed' AND (OLD.status IS NULL OR OLD.status != 'completed') THEN
        -- Update project status to active
        UPDATE projects 
        SET status = 'active', started_at = NOW()
        WHERE id = NEW.project_id;
        
        -- Update milestone status to paid
        UPDATE project_milestones 
        SET status = 'paid', paid_at = NOW(), payment_id = NEW.id
        WHERE id = NEW.milestone_id;
        
        -- Activate next milestone if exists
        UPDATE project_milestones 
        SET status = 'active'
        WHERE project_id = NEW.project_id 
        AND order_index = 1
        AND status = 'inactive';
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger for deposit payment processing
DROP TRIGGER IF EXISTS trigger_update_project_on_deposit ON payments;
CREATE TRIGGER trigger_update_project_on_deposit
    AFTER INSERT OR UPDATE ON payments
    FOR EACH ROW
    EXECUTE FUNCTION update_project_on_deposit_payment();

-- =====================================================
-- VERIFICATION QUERIES
-- =====================================================

-- Check project statuses
SELECT status, COUNT(*) as count 
FROM projects 
GROUP BY status 
ORDER BY count DESC;

-- Check milestone statuses
SELECT status, COUNT(*) as count 
FROM project_milestones 
GROUP BY status 
ORDER BY count DESC;

-- Check payment types
SELECT payment_type, status, COUNT(*) as count 
FROM payments 
GROUP BY payment_type, status 
ORDER BY payment_type, status;

RAISE NOTICE '✅ Comprehensive workflow fixes completed successfully!';
RAISE NOTICE '📋 Summary:';
RAISE NOTICE '   - Updated project and milestone status constraints';
RAISE NOTICE '   - Created/updated payments and escrow tables';
RAISE NOTICE '   - Fixed project_submissions table structure';
RAISE NOTICE '   - Added foreign key constraints for quality reviews';
RAISE NOTICE '   - Created automatic triggers for workflow';
RAISE NOTICE '   - Updated existing projects for deposit workflow';
RAISE NOTICE '🚀 System ready for deposit payment workflow!';
