-- =====================================================
-- QUALITY TEAM SLA AUTOMATION SYSTEM
-- Automatic escalation, reassignment, and performance tracking
-- =====================================================

-- 1. QUALITY SLA CONFIGURATION TABLE
CREATE TABLE IF NOT EXISTS quality_sla_config (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    priority VARCHAR(20) NOT NULL UNIQUE CHECK (priority IN ('low', 'medium', 'high', 'urgent', 'emergency')),
    hours_to_deadline INTEGER NOT NULL,
    warning_threshold_hours INTEGER NOT NULL,
    critical_threshold_hours INTEGER NOT NULL,
    auto_escalate_after_hours INTEGER,
    auto_reassign_after_hours INTEGER,
    notification_frequency_hours INTEGER DEFAULT 4,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 2. QUALITY TEAM WORKLOAD TABLE
CREATE TABLE IF NOT EXISTS quality_team_workload (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    team_member_id UUID REFERENCES profiles(id) ON DELETE CASCADE,
    current_assignments INTEGER DEFAULT 0,
    max_capacity INTEGER DEFAULT 5,
    availability_status VARCHAR(20) DEFAULT 'available' CHECK (availability_status IN ('available', 'busy', 'unavailable', 'out_of_office')),
    last_assignment_at TIMESTAMP WITH TIME ZONE,
    average_review_time_minutes INTEGER,
    specialties TEXT[],
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 3. QUALITY SLA TRACKING TABLE
CREATE TABLE IF NOT EXISTS quality_sla_tracking (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    review_id UUID REFERENCES quality_reviews_new(id) ON DELETE CASCADE,
    original_deadline TIMESTAMP WITH TIME ZONE NOT NULL,
    current_deadline TIMESTAMP WITH TIME ZONE NOT NULL,
    priority VARCHAR(20) NOT NULL,
    status VARCHAR(20) DEFAULT 'on_track' CHECK (status IN ('on_track', 'at_risk', 'overdue', 'completed', 'escalated')),
    assigned_to UUID REFERENCES profiles(id),
    original_assignee UUID REFERENCES profiles(id),
    escalated_to UUID REFERENCES profiles(id),
    escalation_level INTEGER DEFAULT 0,
    escalation_reason TEXT,
    escalation_time TIMESTAMP WITH TIME ZONE,
    last_notification_at TIMESTAMP WITH TIME ZONE,
    completed_at TIMESTAMP WITH TIME ZONE,
    time_to_completion_minutes INTEGER,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 4. QUALITY SLA HISTORY TABLE
CREATE TABLE IF NOT EXISTS quality_sla_history (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    review_id UUID REFERENCES quality_reviews_new(id) ON DELETE CASCADE,
    event_type VARCHAR(50) NOT NULL CHECK (event_type IN ('created', 'assigned', 'reassigned', 'escalated', 'deadline_changed', 'status_changed', 'completed', 'notification_sent')),
    event_time TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    previous_value JSONB,
    new_value JSONB,
    performed_by UUID REFERENCES profiles(id),
    notes TEXT
);

-- 5. INSERT DEFAULT SLA CONFIGURATIONS
INSERT INTO quality_sla_config (priority, hours_to_deadline, warning_threshold_hours, critical_threshold_hours, auto_escalate_after_hours, auto_reassign_after_hours) VALUES
('low', 72, 24, 12, 80, 84),
('medium', 48, 16, 8, 52, 56),
('high', 24, 8, 4, 26, 28),
('urgent', 12, 4, 2, 13, 14),
('emergency', 4, 2, 1, 5, 6);

-- 6. CREATE FUNCTIONS FOR SLA AUTOMATION

-- Function to calculate SLA deadline based on priority
CREATE OR REPLACE FUNCTION calculate_sla_deadline(
    p_priority VARCHAR(20)
) RETURNS TIMESTAMP WITH TIME ZONE AS $$
DECLARE
    v_hours INTEGER;
BEGIN
    SELECT hours_to_deadline INTO v_hours
    FROM quality_sla_config
    WHERE priority = p_priority AND is_active = TRUE;
    
    IF v_hours IS NULL THEN
        -- Default to 24 hours if priority not found
        v_hours := 24;
    END IF;
    
    RETURN NOW() + (v_hours * INTERVAL '1 hour');
END;
$$ LANGUAGE plpgsql;

-- Function to assign review to quality team member
CREATE OR REPLACE FUNCTION assign_quality_review(
    p_review_id UUID,
    p_assignee_id UUID DEFAULT NULL
) RETURNS UUID AS $$
DECLARE
    v_assignee_id UUID;
    v_priority VARCHAR(20);
    v_deadline TIMESTAMP WITH TIME ZONE;
BEGIN
    -- Get review priority
    SELECT priority INTO v_priority
    FROM quality_reviews_new
    WHERE id = p_review_id;
    
    -- Calculate deadline
    v_deadline := calculate_sla_deadline(v_priority);
    
    -- If assignee not specified, find best available team member
    IF p_assignee_id IS NULL THEN
        SELECT team_member_id INTO v_assignee_id
        FROM quality_team_workload
        WHERE is_active = TRUE
        AND availability_status = 'available'
        AND current_assignments < max_capacity
        ORDER BY current_assignments ASC, last_assignment_at ASC
        LIMIT 1;
    ELSE
        v_assignee_id := p_assignee_id;
    END IF;
    
    -- Update review with assignee
    UPDATE quality_reviews_new SET
        reviewer_id = v_assignee_id,
        status = 'assigned',
        sla_deadline = v_deadline,
        updated_at = NOW()
    WHERE id = p_review_id;
    
    -- Create SLA tracking record
    INSERT INTO quality_sla_tracking (
        review_id,
        original_deadline,
        current_deadline,
        priority,
        assigned_to,
        original_assignee
    ) VALUES (
        p_review_id,
        v_deadline,
        v_deadline,
        v_priority,
        v_assignee_id,
        v_assignee_id
    );
    
    -- Update team member workload
    UPDATE quality_team_workload SET
        current_assignments = current_assignments + 1,
        last_assignment_at = NOW(),
        updated_at = NOW()
    WHERE team_member_id = v_assignee_id;
    
    -- Log assignment in history
    INSERT INTO quality_sla_history (
        review_id,
        event_type,
        new_value,
        performed_by
    ) VALUES (
        p_review_id,
        'assigned',
        jsonb_build_object(
            'assignee_id', v_assignee_id,
            'deadline', v_deadline,
            'priority', v_priority
        ),
        v_assignee_id
    );
    
    RETURN v_assignee_id;
END;
$$ LANGUAGE plpgsql;

-- Function to reassign review to another team member
CREATE OR REPLACE FUNCTION reassign_quality_review(
    p_review_id UUID,
    p_new_assignee_id UUID,
    p_reason TEXT DEFAULT NULL,
    p_performed_by UUID DEFAULT NULL
) RETURNS BOOLEAN AS $$
DECLARE
    v_old_assignee_id UUID;
    v_sla_tracking_id UUID;
BEGIN
    -- Get current assignee
    SELECT reviewer_id, id INTO v_old_assignee_id, v_sla_tracking_id
    FROM quality_reviews_new
    WHERE id = p_review_id;
    
    -- Update review with new assignee
    UPDATE quality_reviews_new SET
        reviewer_id = p_new_assignee_id,
        updated_at = NOW()
    WHERE id = p_review_id;
    
    -- Update SLA tracking
    UPDATE quality_sla_tracking SET
        assigned_to = p_new_assignee_id,
        updated_at = NOW()
    WHERE review_id = p_review_id;
    
    -- Update old assignee workload
    IF v_old_assignee_id IS NOT NULL THEN
        UPDATE quality_team_workload SET
            current_assignments = GREATEST(0, current_assignments - 1),
            updated_at = NOW()
        WHERE team_member_id = v_old_assignee_id;
    END IF;
    
    -- Update new assignee workload
    UPDATE quality_team_workload SET
        current_assignments = current_assignments + 1,
        last_assignment_at = NOW(),
        updated_at = NOW()
    WHERE team_member_id = p_new_assignee_id;
    
    -- Log reassignment in history
    INSERT INTO quality_sla_history (
        review_id,
        event_type,
        previous_value,
        new_value,
        performed_by,
        notes
    ) VALUES (
        p_review_id,
        'reassigned',
        jsonb_build_object('assignee_id', v_old_assignee_id),
        jsonb_build_object('assignee_id', p_new_assignee_id),
        COALESCE(p_performed_by, p_new_assignee_id),
        p_reason
    );
    
    RETURN TRUE;
END;
$$ LANGUAGE plpgsql;

-- Function to escalate review
CREATE OR REPLACE FUNCTION escalate_quality_review(
    p_review_id UUID,
    p_escalate_to UUID,
    p_reason TEXT,
    p_performed_by UUID DEFAULT NULL
) RETURNS BOOLEAN AS $$
DECLARE
    v_tracking_record RECORD;
BEGIN
    -- Get current tracking record
    SELECT * INTO v_tracking_record
    FROM quality_sla_tracking
    WHERE review_id = p_review_id;

    -- Update SLA tracking with escalation
    UPDATE quality_sla_tracking SET
        status = 'escalated',
        escalated_to = p_escalate_to,
        escalation_level = COALESCE(escalation_level, 0) + 1,
        escalation_reason = p_reason,
        escalation_time = NOW(),
        updated_at = NOW()
    WHERE review_id = p_review_id;

    -- Log escalation in history
    INSERT INTO quality_sla_history (
        review_id,
        event_type,
        previous_value,
        new_value,
        performed_by,
        notes
    ) VALUES (
        p_review_id,
        'escalated',
        jsonb_build_object(
            'status', v_tracking_record.status,
            'escalation_level', v_tracking_record.escalation_level
        ),
        jsonb_build_object(
            'status', 'escalated',
            'escalated_to', p_escalate_to,
            'escalation_level', COALESCE(v_tracking_record.escalation_level, 0) + 1
        ),
        COALESCE(p_performed_by, p_escalate_to),
        p_reason
    );

    RETURN TRUE;
END;
$$ LANGUAGE plpgsql;

-- Function to mark review as completed
CREATE OR REPLACE FUNCTION complete_quality_review(
    p_review_id UUID,
    p_reviewer_id UUID
) RETURNS BOOLEAN AS $$
DECLARE
    v_start_time TIMESTAMP WITH TIME ZONE;
    v_completion_minutes INTEGER;
BEGIN
    -- Get review creation time
    SELECT created_at INTO v_start_time
    FROM quality_reviews_new
    WHERE id = p_review_id;

    -- Calculate time to completion
    v_completion_minutes := EXTRACT(EPOCH FROM (NOW() - v_start_time)) / 60;

    -- Update SLA tracking
    UPDATE quality_sla_tracking SET
        status = 'completed',
        completed_at = NOW(),
        time_to_completion_minutes = v_completion_minutes,
        updated_at = NOW()
    WHERE review_id = p_review_id;

    -- Update team member workload
    UPDATE quality_team_workload SET
        current_assignments = GREATEST(0, current_assignments - 1),
        average_review_time_minutes = (
            COALESCE(average_review_time_minutes, 0) + v_completion_minutes
        ) / 2,
        updated_at = NOW()
    WHERE team_member_id = p_reviewer_id;

    -- Log completion in history
    INSERT INTO quality_sla_history (
        review_id,
        event_type,
        new_value,
        performed_by
    ) VALUES (
        p_review_id,
        'completed',
        jsonb_build_object(
            'completed_at', NOW(),
            'time_to_completion_minutes', v_completion_minutes
        ),
        p_reviewer_id
    );

    RETURN TRUE;
END;
$$ LANGUAGE plpgsql;

-- Function to check and update SLA status
CREATE OR REPLACE FUNCTION check_sla_status(
    p_review_id UUID
) RETURNS VARCHAR(20) AS $$
DECLARE
    v_tracking RECORD;
    v_config RECORD;
    v_hours_remaining DECIMAL;
    v_new_status VARCHAR(20);
BEGIN
    -- Get tracking record and config
    SELECT t.*, c.warning_threshold_hours, c.critical_threshold_hours
    INTO v_tracking
    FROM quality_sla_tracking t
    JOIN quality_sla_config c ON t.priority = c.priority
    WHERE t.review_id = p_review_id AND c.is_active = TRUE;

    IF v_tracking.status = 'completed' THEN
        RETURN v_tracking.status;
    END IF;

    -- Calculate hours remaining
    v_hours_remaining := EXTRACT(EPOCH FROM (v_tracking.current_deadline - NOW())) / 3600;

    -- Determine new status
    IF v_hours_remaining <= 0 THEN
        v_new_status := 'overdue';
    ELSIF v_hours_remaining <= v_tracking.critical_threshold_hours THEN
        v_new_status := 'at_risk';
    ELSE
        v_new_status := 'on_track';
    END IF;

    -- Update status if changed
    IF v_new_status <> v_tracking.status THEN
        UPDATE quality_sla_tracking SET
            status = v_new_status,
            updated_at = NOW()
        WHERE id = v_tracking.id;

        -- Log status change
        INSERT INTO quality_sla_history (
            review_id,
            event_type,
            previous_value,
            new_value
        ) VALUES (
            p_review_id,
            'status_changed',
            jsonb_build_object('status', v_tracking.status),
            jsonb_build_object('status', v_new_status)
        );
    END IF;

    RETURN v_new_status;
END;
$$ LANGUAGE plpgsql;

-- Function to mark review as completed
CREATE OR REPLACE FUNCTION complete_quality_review(
    p_review_id UUID,
    p_reviewer_id UUID
) RETURNS BOOLEAN AS $$
DECLARE
    v_start_time TIMESTAMP WITH TIME ZONE;
    v_completion_minutes INTEGER;
BEGIN
    -- Get review creation time
    SELECT created_at INTO v_start_time
    FROM quality_reviews_new
    WHERE id = p_review_id;
    
    -- Calculate time to completion
    v_completion_minutes := EXTRACT(EPOCH FROM (NOW() - v_start_time)) / 60;
    
    -- Update SLA tracking
    UPDATE quality_sla_tracking SET
        status = 'completed',
        completed_at = NOW(),
        time_to_completion_minutes = v_completion_minutes,
        updated_at = NOW()
    WHERE review_id = p_review_id;
    
    -- Update team member workload
    UPDATE quality_team_workload SET
        current_assignments = GREATEST(0, current_assignments - 1),
        average_review_time_minutes = (
            COALESCE(average_review_time_minutes, 0) + v_completion_minutes
        ) / 2,
        updated_at = NOW()
    WHERE team_member_id = p_reviewer_id;
    
    -- Log completion in history
    INSERT INTO quality_sla_history (
        review_id,
        event_type,
        new_value,
        performed_by
    ) VALUES (
        p_review_id,
        'completed',
        jsonb_build_object(
            'completed_at', NOW(),
            'time_to_completion_minutes', v_completion_minutes
        ),
        p_reviewer_id
    );
    
    RETURN TRUE;
END;
$$ LANGUAGE plpgsql;

-- Function to check and update SLA status
CREATE OR REPLACE FUNCTION check_sla_status(
    p_review_id UUID
) RETURNS VARCHAR(20) AS $$
DECLARE
    v_tracking RECORD;
    v_config RECORD;
    v_hours_remaining DECIMAL;
    v_new_status VARCHAR(20);
BEGIN
    -- Get tracking record and config
    SELECT t.*, c.warning_threshold_hours, c.critical_threshold_hours
    INTO v_tracking
    FROM quality_sla_tracking t
    JOIN quality_sla_config c ON t.priority = c.priority
    WHERE t.review_id = p_review_id AND c.is_active = TRUE;
    
    IF v_tracking.status = 'completed' THEN
        RETURN v_tracking.status;
    END IF;
    
    -- Calculate hours remaining
    v_hours_remaining := EXTRACT(EPOCH FROM (v_tracking.current_deadline - NOW())) / 3600;
    
    -- Determine new status
    IF v_hours_remaining <= 0 THEN
        v_new_status := 'overdue';
    ELSIF v_hours_remaining <= v_tracking.critical_threshold_hours THEN
        v_new_status := 'at_risk';
    ELSE
        v_new_status := 'on_track';
    END IF;
    
    -- Update status if changed
    IF v_new_status <> v_tracking.status THEN
        UPDATE quality_sla_tracking SET
            status = v_new_status,
            updated_at = NOW()
        WHERE id = v_tracking.id;
        
        -- Log status change
        INSERT INTO quality_sla_history (
            review_id,
            event_type,
            previous_value,
            new_value
        ) VALUES (
            p_review_id,
            'status_changed',
            jsonb_build_object('status', v_tracking.status),
            jsonb_build_object('status', v_new_status)
        );
    END IF;
    
    RETURN v_new_status;
END;
$$ LANGUAGE plpgsql;

-- 7. CREATE TRIGGERS FOR AUTOMATIC WORKFLOW

-- Trigger to automatically assign SLA when review is created
CREATE OR REPLACE FUNCTION trigger_assign_sla_on_creation()
RETURNS TRIGGER AS $$
BEGIN
    -- Calculate and set SLA deadline
    NEW.sla_deadline := calculate_sla_deadline(NEW.priority);
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER assign_sla_on_creation_trigger
    BEFORE INSERT ON quality_reviews_new
    FOR EACH ROW
    EXECUTE FUNCTION trigger_assign_sla_on_creation();

-- Trigger to update SLA tracking when review is completed
CREATE OR REPLACE FUNCTION trigger_update_sla_on_completion()
RETURNS TRIGGER AS $$
BEGIN
    IF NEW.status IN ('approved', 'rejected', 'needs_revision') AND 
       OLD.status NOT IN ('approved', 'rejected', 'needs_revision') THEN
        PERFORM complete_quality_review(NEW.id, NEW.reviewer_id);
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER update_sla_on_completion_trigger
    AFTER UPDATE ON quality_reviews_new
    FOR EACH ROW
    WHEN (NEW.status IN ('approved', 'rejected', 'needs_revision'))
    EXECUTE FUNCTION trigger_update_sla_on_completion();

-- 8. CREATE INDEXES FOR PERFORMANCE
CREATE INDEX IF NOT EXISTS idx_quality_sla_tracking_review_id ON quality_sla_tracking(review_id);
CREATE INDEX IF NOT EXISTS idx_quality_sla_tracking_status ON quality_sla_tracking(status);
CREATE INDEX IF NOT EXISTS idx_quality_sla_tracking_deadline ON quality_sla_tracking(current_deadline);
CREATE INDEX IF NOT EXISTS idx_quality_sla_tracking_assigned_to ON quality_sla_tracking(assigned_to);

CREATE INDEX IF NOT EXISTS idx_quality_team_workload_member ON quality_team_workload(team_member_id);
CREATE INDEX IF NOT EXISTS idx_quality_team_workload_status ON quality_team_workload(availability_status);
CREATE INDEX IF NOT EXISTS idx_quality_team_workload_active ON quality_team_workload(is_active);

CREATE INDEX IF NOT EXISTS idx_quality_sla_history_review_id ON quality_sla_history(review_id);
CREATE INDEX IF NOT EXISTS idx_quality_sla_history_event_type ON quality_sla_history(event_type);
CREATE INDEX IF NOT EXISTS idx_quality_sla_history_event_time ON quality_sla_history(event_time);

-- 9. CREATE SCHEDULED FUNCTIONS FOR AUTOMATION

-- Function to check all active reviews for SLA status
CREATE OR REPLACE FUNCTION check_all_active_sla_statuses()
RETURNS INTEGER AS $$
DECLARE
    v_review RECORD;
    v_count INTEGER := 0;
BEGIN
    FOR v_review IN 
        SELECT r.id
        FROM quality_reviews_new r
        JOIN quality_sla_tracking t ON r.id = t.review_id
        WHERE r.status NOT IN ('approved', 'rejected', 'needs_revision')
        AND t.status NOT IN ('completed')
    LOOP
        PERFORM check_sla_status(v_review.id);
        v_count := v_count + 1;
    END LOOP;
    
    RETURN v_count;
END;
$$ LANGUAGE plpgsql;

-- Function to auto-escalate overdue reviews
CREATE OR REPLACE FUNCTION auto_escalate_overdue_reviews()
RETURNS INTEGER AS $$
DECLARE
    v_review RECORD;
    v_config RECORD;
    v_manager_id UUID;
    v_count INTEGER := 0;
BEGIN
    FOR v_review IN 
        SELECT r.id, r.project_id, t.assigned_to, t.priority, 
               EXTRACT(EPOCH FROM (NOW() - t.current_deadline)) / 3600 AS hours_overdue
        FROM quality_reviews_new r
        JOIN quality_sla_tracking t ON r.id = t.review_id
        JOIN quality_sla_config c ON t.priority = c.priority
        WHERE r.status NOT IN ('approved', 'rejected', 'needs_revision')
        AND t.status = 'overdue'
        AND c.auto_escalate_after_hours IS NOT NULL
        AND EXTRACT(EPOCH FROM (NOW() - t.current_deadline)) / 3600 >= c.auto_escalate_after_hours
        AND (t.escalation_level IS NULL OR t.escalation_level = 0)
    LOOP
        -- Find project manager to escalate to
        SELECT manager_id INTO v_manager_id
        FROM projects
        WHERE id = v_review.project_id;
        
        IF v_manager_id IS NULL THEN
            -- Fallback to a quality team lead or admin
            SELECT id INTO v_manager_id
            FROM profiles
            WHERE role IN ('admin', 'quality_lead')
            LIMIT 1;
        END IF;
        
        IF v_manager_id IS NOT NULL THEN
            PERFORM escalate_quality_review(
                v_review.id,
                v_manager_id,
                'Automatically escalated due to SLA breach. Review is ' || 
                ROUND(v_review.hours_overdue) || ' hours overdue.'
            );
            v_count := v_count + 1;
        END IF;
    END LOOP;
    
    RETURN v_count;
END;
$$ LANGUAGE plpgsql;

-- Function to auto-reassign stalled reviews
CREATE OR REPLACE FUNCTION auto_reassign_stalled_reviews()
RETURNS INTEGER AS $$
DECLARE
    v_review RECORD;
    v_new_assignee_id UUID;
    v_count INTEGER := 0;
BEGIN
    FOR v_review IN 
        SELECT r.id, t.assigned_to, t.priority, 
               EXTRACT(EPOCH FROM (NOW() - t.current_deadline)) / 3600 AS hours_overdue
        FROM quality_reviews_new r
        JOIN quality_sla_tracking t ON r.id = t.review_id
        JOIN quality_sla_config c ON t.priority = c.priority
        WHERE r.status NOT IN ('approved', 'rejected', 'needs_revision')
        AND t.status = 'overdue'
        AND c.auto_reassign_after_hours IS NOT NULL
        AND EXTRACT(EPOCH FROM (NOW() - t.current_deadline)) / 3600 >= c.auto_reassign_after_hours
    LOOP
        -- Find new assignee
        SELECT team_member_id INTO v_new_assignee_id
        FROM quality_team_workload
        WHERE is_active = TRUE
        AND availability_status = 'available'
        AND current_assignments < max_capacity
        AND team_member_id <> v_review.assigned_to
        ORDER BY current_assignments ASC, last_assignment_at ASC
        LIMIT 1;
        
        IF v_new_assignee_id IS NOT NULL THEN
            PERFORM reassign_quality_review(
                v_review.id,
                v_new_assignee_id,
                'Automatically reassigned due to extended SLA breach. Review is ' || 
                ROUND(v_review.hours_overdue) || ' hours overdue.',
                NULL
            );
            v_count := v_count + 1;
        END IF;
    END LOOP;
    
    RETURN v_count;
END;
$$ LANGUAGE plpgsql;

-- 10. ROW LEVEL SECURITY POLICIES

-- Enable RLS on all SLA tables
ALTER TABLE quality_sla_config ENABLE ROW LEVEL SECURITY;
ALTER TABLE quality_team_workload ENABLE ROW LEVEL SECURITY;
ALTER TABLE quality_sla_tracking ENABLE ROW LEVEL SECURITY;
ALTER TABLE quality_sla_history ENABLE ROW LEVEL SECURITY;

-- Policies for quality_sla_config
CREATE POLICY "Everyone can read SLA configs"
ON quality_sla_config FOR SELECT
TO authenticated
USING (true);

CREATE POLICY "Only admins can manage SLA configs"
ON quality_sla_config FOR ALL
TO authenticated
USING (
    EXISTS (
        SELECT 1 FROM profiles 
        WHERE profiles.id = auth.uid() 
        AND profiles.role IN ('admin')
    )
);

-- Policies for quality_team_workload
CREATE POLICY "Quality team can view all workloads"
ON quality_team_workload FOR SELECT
TO authenticated
USING (
    EXISTS (
        SELECT 1 FROM profiles 
        WHERE profiles.id = auth.uid() 
        AND profiles.role IN ('quality_team', 'admin', 'manager')
    )
);

CREATE POLICY "Team members can view their own workload"
ON quality_team_workload FOR SELECT
TO authenticated
USING (team_member_id = auth.uid());

CREATE POLICY "Only admins can manage workloads"
ON quality_team_workload FOR ALL
TO authenticated
USING (
    EXISTS (
        SELECT 1 FROM profiles 
        WHERE profiles.id = auth.uid() 
        AND profiles.role IN ('admin')
    )
);

-- Policies for quality_sla_tracking
CREATE POLICY "Quality team can view all SLA tracking"
ON quality_sla_tracking FOR SELECT
TO authenticated
USING (
    EXISTS (
        SELECT 1 FROM profiles 
        WHERE profiles.id = auth.uid() 
        AND profiles.role IN ('quality_team', 'admin', 'manager')
    )
);

CREATE POLICY "Team members can view their assigned SLAs"
ON quality_sla_tracking FOR SELECT
TO authenticated
USING (assigned_to = auth.uid() OR escalated_to = auth.uid());

-- 11. COMMENTS FOR DOCUMENTATION
COMMENT ON TABLE quality_sla_config IS 'Configuration for quality review SLAs by priority level';
COMMENT ON TABLE quality_team_workload IS 'Workload tracking for quality team members';
COMMENT ON TABLE quality_sla_tracking IS 'SLA tracking for quality reviews';
COMMENT ON TABLE quality_sla_history IS 'History of SLA-related events';

COMMENT ON FUNCTION calculate_sla_deadline IS 'Calculates SLA deadline based on priority';
COMMENT ON FUNCTION assign_quality_review IS 'Assigns a quality review to a team member with SLA tracking';
COMMENT ON FUNCTION reassign_quality_review IS 'Reassigns a quality review to another team member';
COMMENT ON FUNCTION escalate_quality_review IS 'Escalates a quality review to a manager or lead';
COMMENT ON FUNCTION complete_quality_review IS 'Marks a quality review as completed and updates SLA tracking';
COMMENT ON FUNCTION check_sla_status IS 'Checks and updates the SLA status of a review';
COMMENT ON FUNCTION check_all_active_sla_statuses IS 'Checks SLA status for all active reviews';
COMMENT ON FUNCTION auto_escalate_overdue_reviews IS 'Automatically escalates overdue reviews';
COMMENT ON FUNCTION auto_reassign_stalled_reviews IS 'Automatically reassigns stalled reviews';
