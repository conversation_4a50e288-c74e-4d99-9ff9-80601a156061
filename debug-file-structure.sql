-- Debug file structure in project_submissions
SELECT 
  id,
  project_id,
  description,
  files::text as files_raw,
  jsonb_array_length(files) as file_count,
  jsonb_pretty(files) as files_formatted,
  submitted_at
FROM project_submissions 
WHERE files IS NOT NULL 
  AND jsonb_array_length(files) > 0
ORDER BY submitted_at DESC
LIMIT 3;

-- Check quality reviews with submission relationship
SELECT 
  qr.id as review_id,
  qr.submission_id,
  qr.project_id,
  ps.files::text as submission_files,
  qr.created_at
FROM quality_reviews_new qr
LEFT JOIN project_submissions ps ON ps.id = qr.submission_id
WHERE qr.id = '6a5b8e2a-2db9-4bb9-b3a0-bac0e1c84e8f'
ORDER BY qr.created_at DESC
LIMIT 1;
