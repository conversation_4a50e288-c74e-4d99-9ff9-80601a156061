'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { supabase } from '@/lib/supabase';
import FileAnnotationTool from '@/components/quality/FileAnnotationTool';
import {
  FileImage,
  Eye,
  ChevronDown,
  ChevronUp,
  AlertCircle,
  CheckCircle,
  Clock,
  MessageSquare
} from 'lucide-react';

interface AnnotatedFile {
  id: string;
  file_url: string;
  file_name: string;
  file_type: string;
  quality_status: 'pending' | 'approved' | 'needs_revision' | 'rejected';
  annotations: any[];
  quality_review_id?: string;
  created_at: string;
  feedback?: string;
}

interface DesignerAnnotatedFilesProps {
  projectId: string;
  designerId: string;
}

export default function DesignerAnnotatedFiles({ projectId, designerId }: DesignerAnnotatedFilesProps) {
  const [files, setFiles] = useState<AnnotatedFile[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [expandedFiles, setExpandedFiles] = useState<Set<string>>(new Set());

  useEffect(() => {
    loadAnnotatedFiles();
  }, [projectId, designerId]);

  const loadAnnotatedFiles = async () => {
    try {
      setLoading(true);
      setError(null);

      // Get all file annotations for this project and designer
      const { data: annotationsData, error: annotationsError } = await supabase
        .from('file_annotations')
        .select(`
          id,
          file_url,
          file_name,
          file_type,
          annotation_type,
          x_position,
          y_position,
          width,
          height,
          title,
          description,
          reviewer_name,
          quality_review_id,
          created_at
        `)
        .eq('submission_type', 'project')
        .order('created_at', { ascending: false });

      if (annotationsError) throw annotationsError;

      // Get quality reviews for this project and designer
      const { data: reviewsData, error: reviewsError } = await supabase
        .from('quality_reviews')
        .select('id, status, feedback, created_at')
        .eq('project_id', projectId)
        .eq('designer_id', designerId);

      if (reviewsError) throw reviewsError;

      // Group annotations by file and combine with quality review status
      const fileMap = new Map<string, AnnotatedFile>();

      annotationsData?.forEach((annotation) => {
        const fileKey = annotation.file_url;
        
        if (!fileMap.has(fileKey)) {
          // Find matching quality review
          const qualityReview = reviewsData?.find(r => r.id === annotation.quality_review_id);
          
          fileMap.set(fileKey, {
            id: annotation.id,
            file_url: annotation.file_url,
            file_name: annotation.file_name,
            file_type: annotation.file_type,
            quality_status: qualityReview?.status || 'pending',
            annotations: [],
            quality_review_id: annotation.quality_review_id,
            created_at: annotation.created_at,
            feedback: qualityReview?.feedback
          });
        }

        // Add annotation to the file
        const file = fileMap.get(fileKey)!;
        file.annotations.push({
          id: annotation.id,
          x: annotation.x_position,
          y: annotation.y_position,
          width: annotation.width || 0,
          height: annotation.height || 0,
          comment: annotation.description || annotation.title || '',
          type: annotation.annotation_type,
          timestamp: annotation.created_at,
          reviewer: annotation.reviewer_name
        });
      });

      setFiles(Array.from(fileMap.values()));

    } catch (error) {
      console.error('Error loading annotated files:', error);
      setError('Failed to load annotated files. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'approved':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'needs_revision':
        return <AlertCircle className="h-4 w-4 text-orange-500" />;
      case 'rejected':
        return <AlertCircle className="h-4 w-4 text-red-500" />;
      default:
        return <Clock className="h-4 w-4 text-gray-500" />;
    }
  };

  const getStatusVariant = (status: string): "default" | "secondary" | "destructive" | "outline" => {
    switch (status) {
      case 'approved':
        return 'default';
      case 'needs_revision':
        return 'secondary';
      case 'rejected':
        return 'destructive';
      default:
        return 'outline';
    }
  };

  const toggleFileExpansion = (fileId: string) => {
    const newExpanded = new Set(expandedFiles);
    if (newExpanded.has(fileId)) {
      newExpanded.delete(fileId);
    } else {
      newExpanded.add(fileId);
    }
    setExpandedFiles(newExpanded);
  };

  if (loading) {
    return (
      <Card>
        <CardContent className="p-8 text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-brown-600 mx-auto mb-4"></div>
          <p className="text-gray-500">Loading annotated files...</p>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card>
        <CardContent className="p-8 text-center">
          <AlertCircle className="h-12 w-12 text-red-500 mx-auto mb-4" />
          <p className="text-red-600 mb-4">{error}</p>
          <Button onClick={loadAnnotatedFiles} variant="outline">
            Try Again
          </Button>
        </CardContent>
      </Card>
    );
  }

  if (files.length === 0) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <FileImage className="h-5 w-5" />
            Quality Review Files
          </CardTitle>
        </CardHeader>
        <CardContent className="text-center py-8">
          <MessageSquare className="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <p className="text-gray-500">No annotated files found for this project.</p>
          <p className="text-sm text-gray-400 mt-2">
            Files will appear here after quality team reviews your submissions.
          </p>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <FileImage className="h-5 w-5" />
          Quality Review Files ({files.length})
        </CardTitle>
        <p className="text-sm text-gray-600">
          View files with quality team feedback and visual annotations
        </p>
      </CardHeader>
      <CardContent className="space-y-4">
        {files.map((file) => (
          <div key={file.id} className="border rounded-lg overflow-hidden">
            <div className="p-4 bg-gray-50">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <div className="flex items-center gap-2">
                    {getStatusIcon(file.quality_status)}
                    <span className="font-medium text-gray-900">
                      {file.file_name}
                    </span>
                  </div>
                  <Badge variant={getStatusVariant(file.quality_status)}>
                    {file.quality_status.replace('_', ' ').toUpperCase()}
                  </Badge>
                  <span className="text-sm text-gray-500">
                    {file.annotations.length} annotation{file.annotations.length !== 1 ? 's' : ''}
                  </span>
                </div>
                
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => toggleFileExpansion(file.id)}
                  className="flex items-center gap-2"
                >
                  <Eye className="h-4 w-4" />
                  {expandedFiles.has(file.id) ? 'Hide' : 'View'} Annotations
                  {expandedFiles.has(file.id) ? (
                    <ChevronUp className="h-4 w-4" />
                  ) : (
                    <ChevronDown className="h-4 w-4" />
                  )}
                </Button>
              </div>

              {/* Quality Feedback */}
              {file.feedback && (
                <div className="mt-3 p-3 bg-blue-50 rounded-lg border border-blue-200">
                  <h4 className="font-medium text-blue-900 mb-1">Quality Feedback:</h4>
                  <p className="text-sm text-blue-800">{file.feedback}</p>
                </div>
              )}

              <div className="mt-2 text-xs text-gray-500">
                Reviewed on {new Date(file.created_at).toLocaleDateString()}
              </div>
            </div>

            {/* Expanded File Annotation View */}
            {expandedFiles.has(file.id) && (
              <div className="p-4 border-t">
                <FileAnnotationTool
                  fileUrl={file.file_url}
                  fileName={file.file_name}
                  fileType={file.file_type}
                  existingAnnotations={file.annotations}
                  onSaveAnnotations={() => Promise.resolve()} // Read-only for designers
                  readOnly={true}
                  reviewerName="Quality Team"
                  qualityReviewId={file.quality_review_id}
                />
              </div>
            )}
          </div>
        ))}
      </CardContent>
    </Card>
  );
}
