'use client';

import type React from 'react';
import { useState, useEffect } from 'react';
import { useOptimizedAuth } from '@/hooks/useOptimizedAuth';
import { supabase } from '@/lib/supabase';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
    FileText,
    Image,
    Download,
    Trash2,
    Eye,
    Upload,
    Search,
    Filter,
    Grid,
    List,
    RefreshCw,
    FolderOpen,
    File
} from 'lucide-react';
import { uploadProjectFile, getProjectFileUrl } from '@/lib/r2-upload';

interface ProjectFile {
    id: string;
    file_url: string;
    file_name: string;
    file_type: string;
    file_size?: number;
    uploaded_at: string;
    uploaded_by: string;
    project_id: string;
    milestone_id?: string;
    projects?: {
        title: string;
    } | null;
    uploader?: {
        full_name: string;
        role: string;
    } | null;
}

interface UnifiedFileManagerProps {
    projectId?: string;
    role: string;
    compact?: boolean;
}

export default function UnifiedFileManager({ projectId, role, compact = false }: UnifiedFileManagerProps) {
    const { user } = useOptimizedAuth();
    const [files, setFiles] = useState<ProjectFile[]>([]);
    const [loading, setLoading] = useState(true);
    const [uploading, setUploading] = useState(false);
    const [viewMode, setViewMode] = useState<'grid' | 'list'>('list');
    const [searchTerm, setSearchTerm] = useState('');
    const [filterType, setFilterType] = useState<'all' | 'images' | 'documents' | 'deliverables'>('all');
    const [selectedFiles, setSelectedFiles] = useState<string[]>([]);

    useEffect(() => {
        if (user) {
            fetchFiles();
        }
    }, [user, projectId, filterType]);

    const fetchFiles = async () => {
        try {
            let query = supabase
                .from('project_submissions')
                .select(`
          id,
          files,
          submission_type,
          created_at,
          designer_id,
          project_id,
          milestone_id,
          projects:project_id (
            title,
            client_id
          ),
          designer:designer_id (
            full_name,
            role
          )
        `);

            // Filter based on role and context
            if (role === 'designer') {
                query = query.eq('designer_id', user?.id);
            } else if (role === 'client') {
                if (projectId) {
                    // Filter by specific project
                    query = query.eq('project_id', projectId);
                } else {
                    // Filter by all projects owned by this client
                    query = query.eq('projects.client_id', user?.id);
                }
            } else if (role === 'admin' || role === 'quality_team' || role === 'manager') {
                // Admin, quality team, and managers see all files
                if (projectId) {
                    query = query.eq('project_id', projectId);
                }
            } else {
                // Default: no access for unknown roles
                query = query.eq('id', 'none');
            }

            const { data, error } = await query
                .order('created_at', { ascending: false })
                .limit(compact ? 15 : 50);

            if (error) throw error;

            // Flatten files from submissions
            const flattenedFiles: ProjectFile[] = [];

            (data || []).forEach(submission => {
                if (submission.files && Array.isArray(submission.files)) {
                    submission.files.forEach((fileUrl: string, index: number) => {
                        flattenedFiles.push({
                            id: `${submission.id}-${index}`,
                            file_url: fileUrl,
                            file_name: `${submission.submission_type}-${index + 1}`,
                            file_type: getFileType(fileUrl),
                            uploaded_at: submission.created_at,
                            uploaded_by: submission.designer_id,
                            project_id: submission.project_id,
                            milestone_id: submission.milestone_id,
                            projects: submission.projects || null,
                            uploader: submission.designer || { full_name: 'Unknown', role: 'Unknown' }
                        });
                    });
                }
            });

            setFiles(flattenedFiles);
        } catch (error) {
            console.error('Error fetching files:', error);
        } finally {
            setLoading(false);
        }
    };

    const getFileType = (url: string | null | undefined): string => {
        // ✅ Fix: Add null/undefined check
        if (!url || typeof url !== 'string') {
            return 'file';
        }

        const extension = url.split('.').pop()?.toLowerCase();
        if (['jpg', 'jpeg', 'png', 'gif', 'svg', 'webp'].includes(extension || '')) {
            return 'image';
        } else if (['pdf', 'doc', 'docx', 'txt'].includes(extension || '')) {
            return 'document';
        } else if (['zip', 'rar', '7z'].includes(extension || '')) {
            return 'archive';
        }
        return 'file';
    };

    const getFileIcon = (fileType: string) => {
        switch (fileType) {
            case 'image':
                return <Image className="h-5 w-5 text-blue-500" />;
            case 'document':
                return <FileText className="h-5 w-5 text-red-500" />;
            case 'archive':
                return <FolderOpen className="h-5 w-5 text-yellow-500" />;
            default:
                return <File className="h-5 w-5 text-gray-500" />;
        }
    };

    const handleFileUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
        const file = event.target.files?.[0];
        if (!file || !projectId) return;

        setUploading(true);
        try {
            const fileKey = await uploadProjectFile(file, file.name, projectId, 'attachment');
            const fileUrl = getProjectFileUrl(fileKey);

            // Create a new submission record
            const { error } = await supabase
                .from('project_submissions')
                .insert({
                    project_id: projectId,
                    designer_id: user?.id,
                    submission_type: 'attachment',
                    files: [fileUrl],
                    description: `Uploaded file: ${file.name}`
                });

            if (error) throw error;

            // Refresh files
            await fetchFiles();
        } catch (error) {
            console.error('Error uploading file:', error);
        } finally {
            setUploading(false);
        }
    };

    const handleDownload = (fileUrl: string, fileName: string) => {
        const link = document.createElement('a');
        link.href = fileUrl;
        link.download = fileName;
        link.target = '_blank';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
    };

    const filteredFiles = files.filter(file => {
        const matchesSearch = file.file_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
            (file.projects?.title?.toLowerCase().includes(searchTerm.toLowerCase()) ?? false);

        const matchesFilter = filterType === 'all' ||
            (filterType === 'images' && file.file_type === 'image') ||
            (filterType === 'documents' && file.file_type === 'document') ||
            (filterType === 'deliverables' && file.file_name.includes('deliverable'));

        return matchesSearch && matchesFilter;
    });

    if (loading) {
        return (
            <Card>
                <CardContent className="p-6">
                    <div className="flex items-center justify-center">
                        <RefreshCw className="h-6 w-6 animate-spin text-brown-600" />
                    </div>
                </CardContent>
            </Card>
        );
    }

    if (compact) {
        return (
            <div className="space-y-4">
                {/* Compact File Stats */}
                <div className="grid grid-cols-1 sm:grid-cols-3 gap-4">
                    <Card className="hover:shadow-md transition-shadow">
                        <CardContent className="p-4">
                            <div className="flex items-center justify-between">
                                <div>
                                    <p className="text-sm text-gray-600 font-medium">Total Files</p>
                                    <p className="text-2xl font-bold text-blue-600">{files.length}</p>
                                </div>
                                <div className="bg-blue-50 p-2 rounded-full">
                                    <FileText className="h-5 w-5 text-blue-500" />
                                </div>
                            </div>
                        </CardContent>
                    </Card>

                    <Card className="hover:shadow-md transition-shadow">
                        <CardContent className="p-4">
                            <div className="flex items-center justify-between">
                                <div>
                                    <p className="text-sm text-gray-600 font-medium">Images</p>
                                    <p className="text-2xl font-bold text-green-600">{files.filter(f => f.file_type === 'image').length}</p>
                                </div>
                                <div className="bg-green-50 p-2 rounded-full">
                                    <Image className="h-5 w-5 text-green-500" />
                                </div>
                            </div>
                        </CardContent>
                    </Card>

                    <Card className="hover:shadow-md transition-shadow">
                        <CardContent className="p-4">
                            <div className="flex items-center justify-between">
                                <div>
                                    <p className="text-sm text-gray-600 font-medium">Documents</p>
                                    <p className="text-2xl font-bold text-purple-600">{files.filter(f => f.file_type === 'document').length}</p>
                                </div>
                                <div className="bg-purple-50 p-2 rounded-full">
                                    <FileText className="h-5 w-5 text-purple-500" />
                                </div>
                            </div>
                        </CardContent>
                    </Card>
                </div>

                {/* Recent Files */}
                <Card className="shadow-sm">
                    <CardHeader className="pb-3">
                        <CardTitle className="text-sm flex items-center gap-2">
                            <FileText className="h-4 w-4 text-brown-600" />
                            Recent Files
                        </CardTitle>
                    </CardHeader>
                    <CardContent>
                        {files.length === 0 ? (
                            <div className="text-center py-8">
                                <FolderOpen className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                                <p className="text-gray-500">No files found</p>
                                <p className="text-sm text-gray-400 mt-2">Files will appear here once uploaded</p>
                            </div>
                        ) : (
                            <div className="space-y-3">
                                {files.slice(0, compact ? 3 : 5).map((file) => (
                                    <div key={file.id} className={`flex items-center justify-between ${compact ? 'p-2' : 'p-3'} border border-gray-200 rounded-lg hover:bg-gray-50 hover:border-gray-300 transition-all duration-200`}>
                                        <div className="flex items-center gap-2 min-w-0 flex-1">
                                            {getFileIcon(file.file_type)}
                                            <div className="min-w-0 flex-1">
                                                <p className={`font-medium truncate ${compact ? 'text-xs' : 'text-sm'}`}>{file.file_name}</p>
                                                <p className="text-xs text-gray-500 truncate">{file.projects?.title || 'Unknown Project'}</p>
                                                {!compact && <p className="text-xs text-gray-400">{new Date(file.uploaded_at).toLocaleDateString()}</p>}
                                            </div>
                                        </div>
                                        <div className="flex gap-1 flex-shrink-0">
                                            <Button
                                                variant="outline"
                                                size="sm"
                                                onClick={() => window.open(file.file_url, '_blank')}
                                                className={compact ? 'h-6 w-6 p-0' : 'flex items-center gap-1 hover:bg-blue-50 hover:border-blue-200'}
                                            >
                                                <Eye className="h-3 w-3" />
                                                {!compact && <span className="hidden sm:inline">View</span>}
                                            </Button>
                                            {!compact && (
                                                <Button
                                                    variant="outline"
                                                    size="sm"
                                                    onClick={() => handleDownload(file.file_url, file.file_name)}
                                                    className="flex items-center gap-1 hover:bg-blue-50 hover:border-blue-200 ml-1"
                                                >
                                                    <Download className="h-3 w-3" />
                                                    <span className="hidden sm:inline">Download</span>
                                                </Button>
                                            )}
                                        </div>
                                    </div>
                                ))}
                                {files.length > 5 && (
                                    <div className="text-center pt-2">
                                        <Button variant="outline" size="sm" className="text-brown-600 hover:text-brown-700">
                                            View All Files ({files.length})
                                        </Button>
                                    </div>
                                )}
                            </div>
                        )}
                    </CardContent>
                </Card>
            </div>
        );
    }

    return (
        <Card>
            <CardHeader>
                <div className="flex items-center justify-between">
                    <CardTitle className="flex items-center gap-2">
                        <FileText className="h-5 w-5 text-brown-600" />
                        File Manager
                    </CardTitle>
                    <div className="flex items-center gap-2">
                        <Button
                            variant={viewMode === 'list' ? 'default' : 'outline'}
                            size="sm"
                            onClick={() => setViewMode('list')}
                        >
                            <List className="h-4 w-4" />
                        </Button>
                        <Button
                            variant={viewMode === 'grid' ? 'default' : 'outline'}
                            size="sm"
                            onClick={() => setViewMode('grid')}
                        >
                            <Grid className="h-4 w-4" />
                        </Button>
                    </div>
                </div>
            </CardHeader>
            <CardContent>
                {/* Search and Filters */}
                <div className="flex flex-col md:flex-row gap-4 mb-6">
                    <div className="flex-1">
                        <div className="relative">
                            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                            <input
                                type="text"
                                placeholder="Search files..."
                                value={searchTerm}
                                onChange={(e) => setSearchTerm(e.target.value)}
                                className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-brown-500 focus:border-transparent"
                            />
                        </div>
                    </div>

                    <select
                        value={filterType}
                        onChange={(e) => setFilterType(e.target.value as any)}
                        className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-brown-500 focus:border-transparent"
                    >
                        <option value="all">All Files</option>
                        <option value="images">Images</option>
                        <option value="documents">Documents</option>
                        <option value="deliverables">Deliverables</option>
                    </select>

                    {projectId && (
                        <div className="relative">
                            <input
                                type="file"
                                onChange={handleFileUpload}
                                disabled={uploading}
                                className="absolute inset-0 w-full h-full opacity-0 cursor-pointer"
                            />
                            <Button disabled={uploading} className="flex items-center gap-2">
                                <Upload className="h-4 w-4" />
                                {uploading ? 'Uploading...' : 'Upload'}
                            </Button>
                        </div>
                    )}
                </div>

                {/* File List/Grid */}
                {filteredFiles.length === 0 ? (
                    <div className="text-center py-8">
                        <FileText className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                        <p className="text-gray-500">No files found</p>
                    </div>
                ) : viewMode === 'list' ? (
                    <div className="space-y-2">
                        {filteredFiles.map((file) => (
                            <div
                                key={file.id}
                                className="flex items-center justify-between p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors"
                            >
                                <div className="flex items-center gap-3 flex-1">
                                    {getFileIcon(file.file_type)}
                                    <div className="flex-1">
                                        <h4 className="font-medium text-gray-900">{file.file_name}</h4>
                                        <div className="flex items-center gap-4 text-sm text-gray-600">
                                            <span>{file.projects?.title || 'Unknown Project'}</span>
                                            <span>by {file.uploader?.full_name || 'Unknown User'}</span>
                                            <span>{new Date(file.uploaded_at).toLocaleDateString()}</span>
                                        </div>
                                    </div>
                                </div>

                                <div className="flex items-center gap-2">
                                    <Button
                                        variant="outline"
                                        size="sm"
                                        onClick={() => window.open(file.file_url, '_blank')}
                                    >
                                        <Eye className="h-4 w-4" />
                                    </Button>
                                    <Button
                                        variant="outline"
                                        size="sm"
                                        onClick={() => handleDownload(file.file_url, file.file_name)}
                                    >
                                        <Download className="h-4 w-4" />
                                    </Button>
                                </div>
                            </div>
                        ))}
                    </div>
                ) : (
                    <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
                        {filteredFiles.map((file) => (
                            <Card key={file.id} className="hover:shadow-md transition-shadow">
                                <CardContent className="p-4">
                                    <div className="text-center">
                                        {getFileIcon(file.file_type)}
                                        <h4 className="font-medium text-gray-900 mt-2 truncate">{file.file_name}</h4>
                                        <p className="text-sm text-gray-600 truncate">{file.projects?.title || 'Unknown Project'}</p>
                                        <div className="flex justify-center gap-2 mt-3">
                                            <Button
                                                variant="outline"
                                                size="sm"
                                                onClick={() => window.open(file.file_url, '_blank')}
                                            >
                                                <Eye className="h-3 w-3" />
                                            </Button>
                                            <Button
                                                variant="outline"
                                                size="sm"
                                                onClick={() => handleDownload(file.file_url, file.file_name)}
                                            >
                                                <Download className="h-3 w-3" />
                                            </Button>
                                        </div>
                                    </div>
                                </CardContent>
                            </Card>
                        ))}
                    </div>
                )}
            </CardContent>
        </Card>
    );
}
