import { type NextRequest, NextResponse } from 'next/server';
import { createServerClient } from '@supabase/ssr';
import { cookies } from 'next/headers';
import { getBaseUrl } from '@/utils/environment';
import { getPayPalAccessToken } from '@/lib/paypal-auth';

export async function POST(request: NextRequest) {
  try {
    // Check for Bearer token authentication first (like other API routes)
    const authHeader = request.headers.get('Authorization');
    let supabase;
    let user;

    if (authHeader && authHeader.startsWith('Bearer ')) {
      // Use Bearer token authentication
      const token = authHeader.split(' ')[1];

      const { createClient } = await import('@supabase/supabase-js');
      supabase = createClient(
        process.env.NEXT_PUBLIC_SUPABASE_URL!,
        process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
        {
          auth: {
            persistSession: false,
            autoRefreshToken: false
          },
          global: {
            headers: {
              Authorization: `Bearer ${token}`
            }
          }
        }
      );

      // Verify the token and get the user
      const { data: { user: authUser }, error: authError } = await supabase.auth.getUser(token);
      if (authError || !authUser) {
        return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
      }
      user = authUser;
    } else {
      // Fall back to cookie-based authentication
      const cookieStore = await cookies();
      supabase = createServerClient(
        process.env.NEXT_PUBLIC_SUPABASE_URL!,
        process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
        {
          cookies: {
            getAll() {
              return cookieStore.getAll();
            },
            setAll(cookiesToSet) {
              cookiesToSet.forEach(({ name, value, options }) => {
                cookieStore.set(name, value, options);
              });
            },
          },
        }
      );

      const { data: { user: cookieUser }, error: authError } = await supabase.auth.getUser();
      if (authError || !cookieUser) {
        return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
      }
      user = cookieUser;
    }

    const body = await request.json();

    const {
      projectId,
      milestoneId,
      proposalId,
      clientId,
      designerId,
      amount, // Amount in cents
      description,
      paymentType = 'deposit'
    } = body;

    // Validate required fields
    if (!projectId || !milestoneId || !clientId || !designerId || !amount) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      );
    }

    // Verify user is the client
    if (user.id !== clientId) {
      return NextResponse.json({ error: 'Unauthorized - not project client' }, { status: 403 });
    }

    // Verify project and milestone exist
    const { data: milestone, error: milestoneError } = await supabase
      .from('project_milestones')
      .select(`
        id,
        title,
        amount,
        status,
        projects!inner(
          id,
          title,
          client_id,
          designer_id,
          status
        )
      `)
      .eq('id', milestoneId)
      .eq('project_id', projectId)
      .single();

    if (milestoneError || !milestone) {
      return NextResponse.json({ error: 'Milestone not found' }, { status: 404 });
    }

    if (milestone.status === 'paid') {
      return NextResponse.json({ error: 'Milestone already paid' }, { status: 400 });
    }

    // Get user's preferred payment method
    const { data: paymentMethods, error: paymentError } = await supabase
      .from('payment_methods')
      .select('*')
      .eq('user_id', clientId)
      .order('is_default', { ascending: false });

    if (paymentError) {
      console.error('Error fetching payment methods:', paymentError);
    }

    const preferredPaymentMethod = paymentMethods?.[0];
    const paymentMethodType = preferredPaymentMethod?.payment_type || 'paypal'; // Default to PayPal if no preference

    console.log('User preferred payment method:', paymentMethodType);

    // Handle different payment methods
    if (paymentMethodType === 'paypal' || !preferredPaymentMethod) {
      // PayPal payment flow
      return await createPayPalDepositPayment({
        projectId,
        milestoneId,
        proposalId,
        clientId,
        designerId,
        amount,
        description,
        paymentType,
        milestone,
        supabase
      });
    } else if (paymentMethodType === 'tappay') {
      // TapPay payment flow
      return await createTapPayDepositPayment({
        projectId,
        milestoneId,
        proposalId,
        clientId,
        designerId,
        amount,
        description,
        paymentType,
        milestone,
        supabase
      });
    } else if (paymentMethodType === 'clickpay') {
      // ClickPay payment flow
      return await createClickPayDepositPayment({
        projectId,
        milestoneId,
        proposalId,
        clientId,
        designerId,
        amount,
        description,
        paymentType,
        milestone,
        supabase
      });
    } else {
      // Fallback to PayPal for unknown payment methods
      return await createPayPalDepositPayment({
        projectId,
        milestoneId,
        proposalId,
        clientId,
        designerId,
        amount,
        description,
        paymentType,
        milestone,
        supabase
      });
    }

  } catch (error) {
    console.error('Error creating deposit payment:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// PayPal deposit payment creation
async function createPayPalDepositPayment(params: any) {
  const {
    projectId,
    milestoneId,
    proposalId,
    clientId,
    designerId,
    amount,
    description,
    paymentType,
    milestone,
    supabase
  } = params;

  // PayPal API base URL
  const PAYPAL_API_BASE = process.env.NODE_ENV === 'production'
    ? 'https://api-m.paypal.com'
    : 'https://api-m.sandbox.paypal.com';

  // Create PayPal payment
  const paypalResponse = await fetch(`${PAYPAL_API_BASE}/v2/checkout/orders`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${await getPayPalAccessToken()}`,
      'PayPal-Request-Id': `deposit-${projectId}-${milestoneId}-${Date.now()}`
    },
    body: JSON.stringify({
      intent: 'CAPTURE',
      purchase_units: [{
        reference_id: `deposit-${projectId}-${milestoneId}`,
        description: description || `Deposit payment for project ${projectId}`,
        amount: {
          currency_code: 'USD',
          value: (amount / 100).toFixed(2) // Convert cents to dollars
        },
        custom_id: JSON.stringify({
          projectId,
          milestoneId,
          clientId,
          designerId,
          paymentType: 'deposit'
        })
      }],
      application_context: {
        brand_name: "Senior's Archi-Firm",
        landing_page: 'NO_PREFERENCE',
        user_action: 'PAY_NOW',
        return_url: `${getBaseUrl()}/client/projects/${projectId}?payment=success&type=deposit`,
        cancel_url: `${getBaseUrl()}/client/projects/${projectId}?payment=cancelled&type=deposit`
      }
    })
  });

    if (!paypalResponse.ok) {
      const errorData = await paypalResponse.text();
      console.error('PayPal API Error:', errorData);
      return NextResponse.json(
        { error: 'Failed to create PayPal payment' },
        { status: 500 }
      );
    }

    const paypalData = await paypalResponse.json();
    const approvalUrl = paypalData.links?.find((link: any) => link.rel === 'approve')?.href;

    if (!approvalUrl) {
      return NextResponse.json(
        { error: 'No approval URL received from PayPal' },
        { status: 500 }
      );
    }

    // Store payment record
    const { error: paymentError } = await supabase
      .from('payments')
      .insert({
        project_id: projectId,
        milestone_id: milestoneId,
        proposal_id: proposalId || null, // ✅ Convert empty string to null
        quote_id: null,                  // ✅ Leave null for deposit payments
        client_id: clientId,
        designer_id: designerId,
        amount: amount / 100, // Store as dollars
        currency: 'USD',
        payment_method: 'paypal',
        status: 'pending',
        transaction_id: paypalData.id,
        payment_type: 'deposit',
        description: description || `Deposit payment for project ${projectId}`,
        metadata: {
          paypal_order_id: paypalData.id,
          proposal_id: proposalId || null
        }
      });

    if (paymentError) {
      console.error('Error storing payment record:', paymentError);
      // Continue anyway - payment was created in PayPal
    }

  return NextResponse.json({
    success: true,
    orderId: paypalData.id,
    approvalUrl,
    amount: amount / 100,
    paymentMethod: 'paypal'
  });
}

// TapPay deposit payment creation
async function createTapPayDepositPayment(params: any) {
  const {
    projectId,
    milestoneId,
    proposalId,
    clientId,
    designerId,
    amount,
    description,
    paymentType,
    milestone,
    supabase
  } = params;

  // Store payment record for TapPay
  const { error: paymentError } = await supabase
    .from('payments')
    .insert({
      project_id: projectId,
      milestone_id: milestoneId,
      proposal_id: proposalId || null,
      quote_id: null,
      client_id: clientId,
      designer_id: designerId,
      amount: amount / 100, // Store as dollars
      currency: 'USD',
      payment_method: 'tappay',
      status: 'pending',
      transaction_id: `tappay_deposit_${projectId}_${milestoneId}_${Date.now()}`,
      payment_type: 'deposit',
      description: description || `Deposit payment for project ${projectId}`,
      metadata: {
        payment_method: 'tappay',
        proposal_id: proposalId || null
      }
    });

  if (paymentError) {
    console.error('Error storing TapPay payment record:', paymentError);
    return NextResponse.json(
      { error: 'Failed to create payment record' },
      { status: 500 }
    );
  }

  // Return TapPay payment form URL or redirect
  return NextResponse.json({
    success: true,
    paymentMethod: 'tappay',
    redirectUrl: `/client/projects/${projectId}/payment/tappay?milestone=${milestoneId}&amount=${amount}&type=deposit`,
    amount: amount / 100
  });
}

// ClickPay deposit payment creation
async function createClickPayDepositPayment(params: any) {
  const {
    projectId,
    milestoneId,
    proposalId,
    clientId,
    designerId,
    amount,
    description,
    paymentType,
    milestone,
    supabase
  } = params;

  // Store payment record for ClickPay
  const { error: paymentError } = await supabase
    .from('payments')
    .insert({
      project_id: projectId,
      milestone_id: milestoneId,
      proposal_id: proposalId || null,
      quote_id: null,
      client_id: clientId,
      designer_id: designerId,
      amount: amount / 100, // Store as dollars
      currency: 'USD',
      payment_method: 'clickpay',
      status: 'pending',
      transaction_id: `clickpay_deposit_${projectId}_${milestoneId}_${Date.now()}`,
      payment_type: 'deposit',
      description: description || `Deposit payment for project ${projectId}`,
      metadata: {
        payment_method: 'clickpay',
        proposal_id: proposalId || null
      }
    });

  if (paymentError) {
    console.error('Error storing ClickPay payment record:', paymentError);
    return NextResponse.json(
      { error: 'Failed to create payment record' },
      { status: 500 }
    );
  }

  // Return ClickPay payment form URL or redirect
  return NextResponse.json({
    success: true,
    paymentMethod: 'clickpay',
    redirectUrl: `/client/projects/${projectId}/payment/clickpay?milestone=${milestoneId}&amount=${amount}&type=deposit`,
    amount: amount / 100
  });
}
