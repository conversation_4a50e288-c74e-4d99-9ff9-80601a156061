-- Migration Script: Harmonize Submissions Data
-- Purpose: Migrate legacy submissions to design_submissions table for consistency
-- Date: 2025-07-16

-- First, let's check what data exists in both tables
SELECT 'Legacy submissions count' as info, COUNT(*) as count FROM submissions
UNION ALL
SELECT 'Design submissions count' as info, COUNT(*) as count FROM design_submissions;

-- Check for overlapping project_ids to avoid duplicates
SELECT 'Overlapping projects' as info, COUNT(DISTINCT s.project_id) as count
FROM submissions s
INNER JOIN design_submissions ds ON s.project_id = ds.project_id;

-- Migration: Insert legacy submissions into design_submissions table
-- Only migrate if there are no conflicts (same project_id + designer_id combination)
INSERT INTO design_submissions (
    id,
    project_id,
    designer_id,
    title,
    description,
    version,
    status,
    created_at,
    updated_at
)
SELECT 
    s.id,
    s.project_id,
    s.designer_id,
    s.title,
    s.description,
    1 as version, -- Legacy submissions start at version 1
    CASE 
        WHEN s.status = 'pending' THEN 'pending'
        WHEN s.status = 'approved' THEN 'approved'
        WHEN s.status = 'rejected' THEN 'rejected'
        WHEN s.revision_requested = true OR s.status = 'needs_revision' THEN 'needs_revision'
        ELSE 'draft'
    END as status,
    s.created_at,
    s.updated_at
FROM submissions s
WHERE NOT EXISTS (
    -- Avoid duplicates: don't migrate if same project+designer already exists in design_submissions
    SELECT 1 FROM design_submissions ds 
    WHERE ds.project_id = s.project_id 
    AND ds.designer_id = s.designer_id
);

-- Create a backup table for the original submissions before cleanup
CREATE TABLE IF NOT EXISTS submissions_legacy_backup AS 
SELECT * FROM submissions;

-- Optional: Add metadata columns to track migration
ALTER TABLE design_submissions 
ADD COLUMN IF NOT EXISTS migrated_from_legacy BOOLEAN DEFAULT FALSE;

-- Mark migrated records
UPDATE design_submissions 
SET migrated_from_legacy = TRUE 
WHERE id IN (SELECT id FROM submissions);

-- Create a view that combines both tables for backward compatibility
CREATE OR REPLACE VIEW unified_submissions AS
SELECT 
    id,
    project_id,
    designer_id,
    title,
    description,
    version,
    status,
    CASE 
        WHEN status = 'needs_revision' THEN TRUE 
        ELSE FALSE 
    END as revision_requested,
    NULL as feedback, -- Legacy field not in new table
    created_at,
    updated_at,
    'design_submissions' as source_table
FROM design_submissions
UNION ALL
SELECT 
    id,
    project_id,
    designer_id,
    title,
    description,
    1 as version, -- Default version for legacy
    status,
    revision_requested,
    feedback,
    created_at,
    updated_at,
    'submissions' as source_table
FROM submissions
WHERE NOT EXISTS (
    -- Exclude if already migrated to design_submissions
    SELECT 1 FROM design_submissions ds 
    WHERE ds.id = submissions.id
);

-- Report migration results
SELECT 
    'Migration completed' as status,
    COUNT(*) as migrated_records
FROM design_submissions 
WHERE migrated_from_legacy = TRUE;

-- Show summary of all submissions across both tables
SELECT 
    source_table,
    status,
    COUNT(*) as count
FROM unified_submissions
GROUP BY source_table, status
ORDER BY source_table, status;

COMMENT ON VIEW unified_submissions IS 'Unified view of submissions from both legacy and new tables, avoiding duplicates';
