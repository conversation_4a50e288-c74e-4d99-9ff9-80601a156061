# Multiple Files Storage Design Issue & Solution

## Current Problem

Your `project_submissions` table has a **conflicting design** for file storage:

### Current Schema Issues:
```sql
-- ❌ PROBLEMATIC: These columns can only store ONE file's data
file_name           | character varying  -- Can only store one filename
file_path           | character varying  -- Can only store one file path  
file_type           | character varying  -- Can only store one file type
file_size           | bigint            -- Can only store one file size

-- ✅ CORRECT: This can store multiple files
files               | jsonb             -- Can store array of files
```

## Your Data Structure
```json
// ✅ This is CORRECT - files column stores array properly
"files": [
  {
    "file_url": "/api/r2-proxy?bucket=project-files&key=submissions%2F34b28d6b-dda8-4336-a6bb-617dade7e8ef%2Ffad637a3-8891-4c07-bda5-63574f754e24%2F1752610818779-Sayona.png",
    "file_name": "Sayona.png",
    "file_path": "submissions/34b28d6b-dda8-4336-a6bb-617dade7e8ef/fad637a3-8891-4c07-bda5-63574f754e24/1752610818779-Sayona.png",
    "file_size": 445362,
    "file_type": "image/png",
    "uploaded_at": "2025-07-15T20:20:20.574Z"
  }
  // Multiple files would be additional objects in this array
]
```

## Solutions

### Option 1: Remove Legacy Columns (Recommended)
```sql
-- Clean up the conflicting individual file columns
ALTER TABLE project_submissions 
DROP COLUMN file_name,
DROP COLUMN file_path, 
DROP COLUMN file_type,
DROP COLUMN file_size;

-- Keep only the JSONB files column for multiple file storage
```

### Option 2: Use Legacy Columns for Primary File Only
```sql
-- Keep legacy columns but populate them with the FIRST file's data only
-- This maintains backward compatibility but has limitations
```

### Option 3: Migrate Data to Proper Structure
```sql
-- Populate individual columns from the first file in JSONB array
UPDATE project_submissions 
SET 
  file_name = (files->>0->>'file_name'),
  file_path = (files->>0->>'file_path'),
  file_type = (files->>0->>'file_type'),
  file_size = (files->>0->>'file_size')::bigint
WHERE files IS NOT NULL AND jsonb_array_length(files) > 0;
```

## Multiple File Upload Behavior

### How it Currently Works:
1. Designer selects multiple files in the upload interface
2. Each file is uploaded to Cloudflare R2
3. File metadata is collected into an array
4. The entire array is stored in the `files` JSONB column
5. ❌ Individual columns (`file_name`, etc.) remain empty or store only one file's data

### How it Should Work:
1. Use only the `files` JSONB column for all file storage
2. Remove or repurpose the individual file columns
3. Application code reads from the `files` array consistently

## Code Changes Made

✅ **Fixed file parsing** to use correct property names:
- `file.file_name` instead of `file.name`
- `file.file_url` instead of trying multiple fallbacks
- `file.file_path` for path construction
- Added proper error handling and logging

✅ **Enhanced URL handling**:
- Direct use of `file_url` when available
- Fallback to constructing URLs from `file_path`
- Support for R2 proxy API URLs

✅ **Better debugging**:
- Added detailed console logging
- Shows exactly what data is being processed
- Identifies why files might not appear

## Recommendation

**Use Option 1** - Remove the legacy individual file columns since:
1. Your upload system already works correctly with JSONB
2. Multiple files are properly supported
3. No application code depends on individual columns
4. Cleaner, more maintainable design

```sql
-- Execute this to clean up the design:
ALTER TABLE project_submissions 
DROP COLUMN IF EXISTS file_name,
DROP COLUMN IF EXISTS file_path, 
DROP COLUMN IF EXISTS file_type,
DROP COLUMN IF EXISTS file_size;
```
