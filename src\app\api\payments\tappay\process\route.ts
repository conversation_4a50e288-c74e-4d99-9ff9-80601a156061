import { type NextRequest, NextResponse } from 'next/server';
import { supabase } from '@/lib/supabase';
import { TAPPAY_API_URLS } from '@/lib/tappay';
import { EscrowManager } from '@/lib/escrow-manager';
import { feeSettingsManager } from '@/lib/fee-settings';

interface TapPayPaymentRequest {
  projectId: string;
  milestoneId?: string;
  amount: number; // Amount in USD cents
  description: string;
  clientId: string;
  designerId?: string;
  paymentType: 'deposit' | 'milestone' | 'final';
  prime: string;
}

interface TapPayAPIResponse {
  status: number;
  msg: string;
  rec_trade_id?: string;
  bank_transaction_id?: string;
  amount?: number;
  currency?: string;
  auth_code?: string;
  acquirer?: string;
  card_info?: {
    bin_code: string;
    last_four: string;
    issuer: string;
    funding: number;
    type: number;
    level: string;
    country: string;
    country_code: string;
  };
  transaction_time_millis?: number;
  bank_result_code?: string;
  bank_result_msg?: string;
}

export async function POST(request: NextRequest) {
  try {
    const body: TapPayPaymentRequest = await request.json();
    const {
      projectId,
      milestoneId,
      amount,
      description,
      clientId,
      designerId,
      paymentType,
      prime
    } = body;

    // Validate required fields
    if (!projectId || !amount || !description || !clientId || !prime) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      );
    }

    // Check if this is a mock payment (development mode)
    const isMockPayment = prime.startsWith('mock_prime_');

    if (isMockPayment) {
      console.log('🔧 Processing mock TapPay payment for development');
      return await processMockPayment({
        projectId,
        milestoneId,
        amount,
        description,
        clientId,
        designerId,
        paymentType,
        prime
      });
    }

    // Get client information
    const { data: clientData, error: clientError } = await supabase
      .from('profiles')
      .select('full_name, email')
      .eq('id', clientId)
      .single();

    if (clientError || !clientData) {
      return NextResponse.json(
        { error: 'Client not found' },
        { status: 404 }
      );
    }

    // Get project information
    const { data: projectData, error: projectError } = await supabase
      .from('projects')
      .select('title, designer_id')
      .eq('id', projectId)
      .single();

    if (projectError || !projectData) {
      return NextResponse.json(
        { error: 'Project not found' },
        { status: 404 }
      );
    }

    // Calculate fees
    const feeCalculation = await feeSettingsManager.calculatePaymentBreakdown(amount / 100);

    // Prepare TapPay payment request
    const tapPayRequest = {
      prime: prime,
      partner_key: process.env.TAPPAY_PARTNER_KEY,
      merchant_id: process.env.TAPPAY_MERCHANT_ID,
      amount: amount, // TapPay expects amount in cents for USD
      currency: 'USD',
      details: description,
      cardholder: {
        phone_number: '', // Optional for international
        name: clientData.full_name,
        email: clientData.email,
        zip_code: '',
        address: '',
        national_id: ''
      },
      order_number: `${projectId}-${milestoneId || 'deposit'}-${Date.now()}`,
      bank_transaction_id: `tappay-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      three_domain_secure: true, // Enable 3D Secure for international transactions
      result_url: {
        frontend_redirect_url: `${process.env.NEXT_PUBLIC_APP_URL}/client/payments/success`,
        backend_notify_url: `${process.env.NEXT_PUBLIC_APP_URL}/api/payments/tappay/webhook`
      }
    };

    // Determine API URL based on environment
    const apiUrl = process.env.NODE_ENV === 'production'
      ? TAPPAY_API_URLS.prod.payByPrime
      : TAPPAY_API_URLS.sandbox.payByPrime;

    // Make request to TapPay API
    const tapPayResponse = await fetch(apiUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${process.env.TAPPAY_PARTNER_KEY}`
      },
      body: JSON.stringify(tapPayRequest)
    });

    const tapPayResult: TapPayAPIResponse = await tapPayResponse.json();

    // Check if payment was successful
    if (tapPayResult.status !== 0) {
      console.error('TapPay payment failed:', tapPayResult);
      return NextResponse.json(
        { error: tapPayResult.msg || 'Payment failed' },
        { status: 400 }
      );
    }

    // Create transaction record
    const { data: transaction, error: transactionError } = await supabase
      .from('transactions')
      .insert({
        transaction_id: tapPayResult.rec_trade_id,
        amount: amount / 100, // Store in dollars
        status: 'completed',
        type: 'payment',
        project_id: projectId,
        milestone_id: milestoneId,
        client_id: clientId,
        designer_id: designerId || projectData.designer_id,
        platform_fee: feeCalculation.platform_fee,
        processing_fee: feeCalculation.processing_fee,
        notes: `TapPay payment: ${description}`,
        payment_method: 'tappay',
        external_transaction_id: tapPayResult.bank_transaction_id,
        metadata: {
          tappay_rec_trade_id: tapPayResult.rec_trade_id,
          tappay_auth_code: tapPayResult.auth_code,
          tappay_acquirer: tapPayResult.acquirer,
          tappay_card_info: tapPayResult.card_info,
          order_number: tapPayRequest.order_number,
          bank_transaction_id: tapPayRequest.bank_transaction_id
        }
      })
      .select()
      .single();

    if (transactionError) {
      console.error('Failed to create transaction record:', transactionError);
      // Payment succeeded but we couldn't record it - this needs manual intervention
      return NextResponse.json(
        { error: 'Payment processed but failed to record. Please contact support.' },
        { status: 500 }
      );
    }

    // Create escrow hold for the payment
    try {
      const escrowResult = await EscrowManager.createEscrowHold({
        transactionId: transaction.id,
        projectId: projectId,
        milestoneId: milestoneId,
        grossAmount: amount / 100,
        platformFee: feeCalculation.platform_fee,
        processingFee: feeCalculation.processing_fee,
        holdReason: `TapPay payment for ${paymentType}`,
        requiresManagerApproval: true,
        requiresQualityApproval: false,
        autoReleaseDays: 30
      });

      if (!escrowResult.success) {
        console.error('Failed to create escrow hold:', escrowResult.error);
        // Payment and transaction recorded, but escrow failed - needs manual intervention
      }
    } catch (escrowError) {
      console.error('Escrow creation error:', escrowError);
    }

    // Update milestone status if applicable
    if (milestoneId) {
      await supabase
        .from('project_milestones')
        .update({ 
          status: 'paid',
          paid_at: new Date().toISOString()
        })
        .eq('id', milestoneId);
    }

    // Log successful payment
    console.log('TapPay payment successful:', {
      transactionId: transaction.id,
      tapPayTradeId: tapPayResult.rec_trade_id,
      amount: amount / 100,
      projectId,
      clientId
    });

    return NextResponse.json({
      success: true,
      transactionId: transaction.id,
      tapPayTradeId: tapPayResult.rec_trade_id,
      data: {
        amount: amount / 100,
        currency: 'USD',
        authCode: tapPayResult.auth_code,
        cardInfo: tapPayResult.card_info
      }
    });

  } catch (error) {
    console.error('TapPay payment processing error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

async function processMockPayment(params: TapPayPaymentRequest) {
  try {
    const {
      projectId,
      milestoneId,
      amount,
      description,
      clientId,
      designerId,
      paymentType,
      prime
    } = params;

    console.log('🔧 Processing mock payment:', { projectId, amount, paymentType });

    // Get client information
    const { data: clientData, error: clientError } = await supabase
      .from('profiles')
      .select('full_name, email')
      .eq('id', clientId)
      .single();

    if (clientError || !clientData) {
      return NextResponse.json(
        { error: 'Client not found' },
        { status: 404 }
      );
    }

    // Calculate fees
    const feeCalculation = await feeSettingsManager.calculatePaymentBreakdown(amount / 100);

    // Generate mock transaction data
    const mockTransactionId = `mock_tappay_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    const mockTradeId = `mock_trade_${Date.now()}`;

    // Create transaction record with mock data
    const { data: transaction, error: transactionError } = await supabase
      .from('transactions')
      .insert({
        transaction_id: mockTransactionId,
        project_id: projectId,
        milestone_id: milestoneId,
        client_id: clientId,
        designer_id: designerId,
        amount: amount,
        fee_amount: Math.round(feeCalculation.platformFee * 100),
        net_amount: Math.round(feeCalculation.designerAmount * 100),
        currency: 'USD',
        payment_method: 'tappay',
        payment_type: paymentType,
        status: 'completed',
        description: description,
        metadata: {
          mock_payment: true,
          prime: prime,
          mock_trade_id: mockTradeId,
          card_info: {
            bin_code: '424242',
            last_four: '4242',
            issuer: 'visa',
            funding: 1,
            type: 1,
            country: 'TW'
          }
        }
      })
      .select()
      .single();

    if (transactionError) {
      console.error('Mock transaction creation error:', transactionError);
      return NextResponse.json(
        { error: 'Failed to create transaction record' },
        { status: 500 }
      );
    }

    // Create escrow hold for mock payment
    const escrowManager = new EscrowManager();
    await escrowManager.createEscrowHold({
      transactionId: transaction.id,
      amount: feeCalculation.designerAmount,
      currency: 'USD',
      paymentMethod: 'tappay',
      projectId,
      clientId
    });

    console.log('✅ Mock payment processed successfully:', mockTransactionId);

    return NextResponse.json({
      success: true,
      transactionId: transaction.id,
      tapPayTradeId: mockTradeId,
      amount: amount / 100,
      currency: 'USD',
      authCode: 'MOCK_AUTH_' + Date.now(),
      cardInfo: {
        bin_code: '424242',
        last_four: '4242',
        issuer: 'visa',
        funding: 1,
        type: 1,
        country: 'TW'
      },
      mock: true
    });

  } catch (error) {
    console.error('Mock payment processing error:', error);
    return NextResponse.json(
      { error: 'Mock payment processing failed' },
      { status: 500 }
    );
  }
}
