#!/bin/bash

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

echo -e "${CYAN}"
echo "========================================"
echo "   Environment Variables Sync Script"
echo "========================================"
echo -e "${NC}"

# Check if Node.js is installed
if ! command -v node &> /dev/null; then
    echo -e "${RED}Error: Node.js is not installed or not in PATH${NC}"
    echo "Please install Node.js from https://nodejs.org/"
    exit 1
fi

# Check if we're in the right directory
if [ ! -f "package.json" ]; then
    echo -e "${RED}Error: package.json not found${NC}"
    echo "Please run this script from the project root directory"
    exit 1
fi

# Make the script executable if it isn't already
chmod +x scripts/env-sync.sh

# Run the sync script
echo -e "${BLUE}Running environment sync script...${NC}"
echo

node scripts/env-sync.js

if [ $? -eq 0 ]; then
    echo
    echo -e "${GREEN}"
    echo "========================================"
    echo "   Sync completed successfully!"
    echo "========================================"
    echo -e "${NC}"
    echo
    echo "Files generated:"
    echo "  - production.env"
    echo "  - env-comparison.json"
    echo
    echo -e "${YELLOW}Next steps:${NC}"
    echo "  1. Review production.env file"
    echo "  2. Import to Vercel: npx vercel env add < production.env"
    echo
else
    echo
    echo -e "${RED}"
    echo "========================================"
    echo "   Sync failed!"
    echo "========================================"
    echo -e "${NC}"
    echo
    echo "Please check the error messages above."
    echo "Make sure you are logged in to Vercel CLI."
    echo
fi
