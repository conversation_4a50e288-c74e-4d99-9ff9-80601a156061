-- Find and fix the trigger that's creating quality reviews with 'medium' priority

-- Step 1: Find all triggers on project_submissions table
SELECT 
    schemaname,
    tablename,
    triggername,
    triggerevent,
    triggerreference
FROM pg_trigger pt
JOIN pg_class pc ON pt.tgrelid = pc.oid
JOIN pg_namespace pn ON pc.relnamespace = pn.oid
WHERE pc.relname = 'project_submissions'
    AND NOT pt.tgisinternal;

-- Step 2: Find all functions that might be creating quality reviews
SELECT 
    p.proname as function_name,
    pg_get_functiondef(p.oid) as function_definition
FROM pg_proc p
JOIN pg_namespace n ON p.pronamespace = n.oid
WHERE n.nspname = 'public'
    AND (
        p.prosrc LIKE '%quality_reviews_new%' 
        OR p.prosrc LIKE '%medium%'
        OR p.proname LIKE '%quality%'
    );

-- Step 3: Drop any triggers that might be creating quality reviews with medium priority
DROP TRIGGER IF EXISTS trigger_create_quality_review ON project_submissions;
DROP TRIGGER IF EXISTS trigger_create_quality_assignment ON project_submissions;
DROP TRIGGER IF EXISTS trigger_create_quality_assignment_new ON project_submissions;
DROP TRIGGER IF EXISTS trigger_quality_review_auto_create ON project_submissions;
DROP TRIGGER IF EXISTS trigger_auto_assign_quality_review ON project_submissions;
DROP TRIGGER IF EXISTS trigger_auto_assign_quality_review_new ON project_submissions;

-- Step 4: Create a fixed trigger function that uses 'normal' priority
CREATE OR REPLACE FUNCTION create_quality_review_on_submission_fixed()
RETURNS TRIGGER AS $$
BEGIN
    -- Only create quality review for submitted status
    IF NEW.status = 'submitted' AND (OLD.status IS NULL OR OLD.status != 'submitted') THEN
        INSERT INTO quality_reviews_new (
            project_id,
            submission_id,
            milestone_id,
            designer_id,
            review_type,
            status,
            priority,
            created_at,
            updated_at
        ) VALUES (
            NEW.project_id,
            NEW.id,
            NEW.milestone_id,
            NEW.designer_id,
            'submission',
            'pending',
            'normal',  -- Fixed: Use 'normal' instead of 'medium'
            NOW(),
            NOW()
        );
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Step 5: Optionally recreate the trigger (commented out by default)
-- Uncomment the next 4 lines if you want automatic quality review creation
-- CREATE TRIGGER trigger_create_quality_review_fixed
--     AFTER INSERT OR UPDATE ON project_submissions
--     FOR EACH ROW
--     EXECUTE FUNCTION create_quality_review_on_submission_fixed();

SELECT 'Trigger investigation and fix completed!' as status;
