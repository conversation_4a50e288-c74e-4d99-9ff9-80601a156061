'use client';

import React, { useState, useEffect } from 'react';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import { 
  <PERSON><PERSON><PERSON>, 
  <PERSON><PERSON>hart, 
  TrendingUp, 
  TrendingDown,
  Users,
  Clock,
  Target,
  Award,
  AlertTriangle,
  CheckCircle,
  Calendar,
  Filter,
  Download,
  RefreshCw
} from 'lucide-react';
import { supabase } from '@/lib/supabase';

interface QualityMetrics {
  totalReviews: number;
  averageScore: number;
  completionRate: number;
  averageTimeToComplete: number;
  slaCompliance: number;
  escalationRate: number;
  revisionRate: number;
  designerSatisfaction: number;
}

interface TrendData {
  date: string;
  reviews: number;
  averageScore: number;
  completionTime: number;
  slaCompliance: number;
}

interface TeamPerformance {
  memberId: string;
  memberName: string;
  reviewsCompleted: number;
  averageScore: number;
  averageTime: number;
  slaCompliance: number;
  escalations: number;
}

interface QualityAnalyticsDashboardProps {
  dateRange?: {
    start: string;
    end: string;
  };
  teamMemberId?: string;
}

export default function QualityAnalyticsDashboard({
  dateRange,
  teamMemberId
}: QualityAnalyticsDashboardProps) {
  const [metrics, setMetrics] = useState<QualityMetrics | null>(null);
  const [trendData, setTrendData] = useState<TrendData[]>([]);
  const [teamPerformance, setTeamPerformance] = useState<TeamPerformance[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedPeriod, setSelectedPeriod] = useState('30d');
  const [activeTab, setActiveTab] = useState('overview');

  useEffect(() => {
    fetchAnalyticsData();
  }, [selectedPeriod, teamMemberId, dateRange]);

  const fetchAnalyticsData = async () => {
    setLoading(true);
    try {
      // Calculate date range
      const endDate = dateRange?.end ? new Date(dateRange.end) : new Date();
      const startDate = dateRange?.start 
        ? new Date(dateRange.start)
        : new Date(endDate.getTime() - (Number.parseInt(selectedPeriod) * 24 * 60 * 60 * 1000));

      // Fetch overall metrics
      await fetchOverallMetrics(startDate, endDate);
      
      // Fetch trend data
      await fetchTrendData(startDate, endDate);
      
      // Fetch team performance
      if (!teamMemberId) {
        await fetchTeamPerformance(startDate, endDate);
      }
    } catch (error) {
      console.error('Error fetching analytics data:', error);
    } finally {
      setLoading(false);
    }
  };

  const fetchOverallMetrics = async (startDate: Date, endDate: Date) => {
    try {
      // Base query for reviews in date range
      let reviewsQuery = supabase
        .from('quality_reviews_new')
        .select(`
          *,
          quality_sla_tracking(*)
        `)
        .gte('created_at', startDate.toISOString())
        .lte('created_at', endDate.toISOString());

      if (teamMemberId && teamMemberId !== 'all') {
        // Only filter by specific team member if explicitly selected
        reviewsQuery = reviewsQuery.eq('reviewer_id', teamMemberId);
      }
      // If teamMemberId is 'all' or not specified, show all reviews (team-wide access)

      const { data: reviews, error } = await reviewsQuery;
      if (error) throw error;

      // Calculate metrics
      const totalReviews = reviews.length;
      const completedReviews = reviews.filter(r => r.status === 'approved' || r.status === 'rejected');
      const approvedReviews = reviews.filter(r => r.status === 'approved');
      const reviewsWithScores = reviews.filter(r => r.overall_score !== null);
      const escalatedReviews = reviews.filter(r => 
        r.quality_sla_tracking && r.quality_sla_tracking.some((t: any) => t.escalation_level > 0)
      );
      const revisionsRequired = reviews.filter(r => r.revision_count > 0);

      // Calculate averages
      const averageScore = reviewsWithScores.length > 0
        ? reviewsWithScores.reduce((sum, r) => sum + (r.overall_score || 0), 0) / reviewsWithScores.length
        : 0;

      const completionRate = totalReviews > 0 ? (completedReviews.length / totalReviews) * 100 : 0;

      const reviewsWithTime = reviews.filter(r => 
        r.quality_sla_tracking && r.quality_sla_tracking.some((t: any) => t.time_to_completion_minutes)
      );
      const averageTimeToComplete = reviewsWithTime.length > 0
        ? reviewsWithTime.reduce((sum, r) => {
            const tracking = r.quality_sla_tracking.find((t: any) => t.time_to_completion_minutes);
            return sum + (tracking?.time_to_completion_minutes || 0);
          }, 0) / reviewsWithTime.length
        : 0;

      const slaCompliantReviews = reviews.filter(r =>
        r.quality_sla_tracking && r.quality_sla_tracking.some((t: any) => 
          t.status === 'completed' && new Date(t.completed_at) <= new Date(t.current_deadline)
        )
      );
      const slaCompliance = completedReviews.length > 0 
        ? (slaCompliantReviews.length / completedReviews.length) * 100 
        : 0;

      const escalationRate = totalReviews > 0 ? (escalatedReviews.length / totalReviews) * 100 : 0;
      const revisionRate = totalReviews > 0 ? (revisionsRequired.length / totalReviews) * 100 : 0;

      // Designer satisfaction would come from feedback surveys (placeholder)
      const designerSatisfaction = 85; // Placeholder

      setMetrics({
        totalReviews,
        averageScore,
        completionRate,
        averageTimeToComplete,
        slaCompliance,
        escalationRate,
        revisionRate,
        designerSatisfaction
      });
    } catch (error) {
      console.error('Error fetching overall metrics:', error);
    }
  };

  const fetchTrendData = async (startDate: Date, endDate: Date) => {
    try {
      // Generate daily data points
      const days = Math.ceil((endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24));
      const trendData: TrendData[] = [];

      for (let i = 0; i < days; i++) {
        const date = new Date(startDate.getTime() + (i * 24 * 60 * 60 * 1000));
        const nextDate = new Date(date.getTime() + (24 * 60 * 60 * 1000));

        let reviewsQuery = supabase
          .from('quality_reviews_new')
          .select(`
            *,
            quality_sla_tracking(*)
          `)
          .gte('created_at', date.toISOString())
          .lt('created_at', nextDate.toISOString());

        if (teamMemberId) {
          reviewsQuery = reviewsQuery.eq('reviewer_id', teamMemberId);
        }

        const { data: dayReviews, error } = await reviewsQuery;
        if (error) throw error;

        const reviewsWithScores = dayReviews.filter(r => r.overall_score !== null);
        const completedReviews = dayReviews.filter(r => r.status === 'approved' || r.status === 'rejected');
        const slaCompliantReviews = dayReviews.filter(r =>
          r.quality_sla_tracking && r.quality_sla_tracking.some((t: any) => 
            t.status === 'completed' && new Date(t.completed_at) <= new Date(t.current_deadline)
          )
        );

        const reviewsWithTime = dayReviews.filter(r => 
          r.quality_sla_tracking && r.quality_sla_tracking.some((t: any) => t.time_to_completion_minutes)
        );

        trendData.push({
          date: date.toISOString().split('T')[0],
          reviews: dayReviews.length,
          averageScore: reviewsWithScores.length > 0
            ? reviewsWithScores.reduce((sum, r) => sum + (r.overall_score || 0), 0) / reviewsWithScores.length
            : 0,
          completionTime: reviewsWithTime.length > 0
            ? reviewsWithTime.reduce((sum, r) => {
                const tracking = r.quality_sla_tracking.find((t: any) => t.time_to_completion_minutes);
                return sum + (tracking?.time_to_completion_minutes || 0);
              }, 0) / reviewsWithTime.length
            : 0,
          slaCompliance: completedReviews.length > 0 
            ? (slaCompliantReviews.length / completedReviews.length) * 100 
            : 0
        });
      }

      setTrendData(trendData);
    } catch (error) {
      console.error('Error fetching trend data:', error);
    }
  };

  const fetchTeamPerformance = async (startDate: Date, endDate: Date) => {
    try {
      const { data: teamMembers, error } = await supabase
        .from('profiles')
        .select('id, full_name')
        .eq('role', 'quality_team');

      if (error) throw error;

      const performance: TeamPerformance[] = [];

      for (const member of teamMembers) {
        const { data: reviews, error: reviewsError } = await supabase
          .from('quality_reviews_new')
          .select(`
            *,
            quality_sla_tracking(*)
          `)
          .eq('reviewer_id', member.id)
          .gte('created_at', startDate.toISOString())
          .lte('created_at', endDate.toISOString());

        if (reviewsError) continue;

        const completedReviews = reviews.filter(r => r.status === 'approved' || r.status === 'rejected');
        const reviewsWithScores = reviews.filter(r => r.overall_score !== null);
        const escalatedReviews = reviews.filter(r => 
          r.quality_sla_tracking && r.quality_sla_tracking.some((t: any) => t.escalation_level > 0)
        );
        const slaCompliantReviews = reviews.filter(r =>
          r.quality_sla_tracking && r.quality_sla_tracking.some((t: any) => 
            t.status === 'completed' && new Date(t.completed_at) <= new Date(t.current_deadline)
          )
        );
        const reviewsWithTime = reviews.filter(r => 
          r.quality_sla_tracking && r.quality_sla_tracking.some((t: any) => t.time_to_completion_minutes)
        );

        performance.push({
          memberId: member.id,
          memberName: member.full_name,
          reviewsCompleted: completedReviews.length,
          averageScore: reviewsWithScores.length > 0
            ? reviewsWithScores.reduce((sum, r) => sum + (r.overall_score || 0), 0) / reviewsWithScores.length
            : 0,
          averageTime: reviewsWithTime.length > 0
            ? reviewsWithTime.reduce((sum, r) => {
                const tracking = r.quality_sla_tracking.find((t: any) => t.time_to_completion_minutes);
                return sum + (tracking?.time_to_completion_minutes || 0);
              }, 0) / reviewsWithTime.length
            : 0,
          slaCompliance: completedReviews.length > 0 
            ? (slaCompliantReviews.length / completedReviews.length) * 100 
            : 0,
          escalations: escalatedReviews.length
        });
      }

      setTeamPerformance(performance.sort((a, b) => b.reviewsCompleted - a.reviewsCompleted));
    } catch (error) {
      console.error('Error fetching team performance:', error);
    }
  };

  const formatTime = (minutes: number) => {
    const hours = Math.floor(minutes / 60);
    const mins = Math.round(minutes % 60);
    return hours > 0 ? `${hours}h ${mins}m` : `${mins}m`;
  };

  const getScoreColor = (score: number) => {
    if (score >= 4.5) return 'text-green-600';
    if (score >= 3.5) return 'text-green-500';
    if (score >= 2.5) return 'text-yellow-500';
    if (score >= 1.5) return 'text-orange-500';
    return 'text-red-500';
  };

  const getPerformanceColor = (value: number, type: 'percentage' | 'score') => {
    if (type === 'percentage') {
      if (value >= 90) return 'text-green-600';
      if (value >= 75) return 'text-green-500';
      if (value >= 60) return 'text-yellow-500';
      if (value >= 40) return 'text-orange-500';
      return 'text-red-500';
    } else {
      return getScoreColor(value);
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold">Quality Analytics Dashboard</h1>
          <p className="text-gray-600">
            {teamMemberId ? 'Individual Performance' : 'Team Performance'} Analytics
          </p>
        </div>
        
        <div className="flex items-center gap-4">
          <select
            value={selectedPeriod}
            onChange={(e) => setSelectedPeriod(e.target.value)}
            className="border border-gray-300 rounded-lg px-3 py-2"
          >
            <option value="7">Last 7 days</option>
            <option value="30">Last 30 days</option>
            <option value="90">Last 90 days</option>
            <option value="365">Last year</option>
          </select>
          
          <Button
            variant="outline"
            onClick={fetchAnalyticsData}
            disabled={loading}
            className="flex items-center gap-2"
          >
            <RefreshCw className={`h-4 w-4 ${loading ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
          
          <Button variant="outline" className="flex items-center gap-2">
            <Download className="h-4 w-4" />
            Export
          </Button>
        </div>
      </div>

      {loading ? (
        <div className="flex items-center justify-center py-12">
          <RefreshCw className="h-8 w-8 text-gray-400 animate-spin" />
        </div>
      ) : (
        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="overview">Overview</TabsTrigger>
            <TabsTrigger value="trends">Trends</TabsTrigger>
            <TabsTrigger value="team">Team Performance</TabsTrigger>
            <TabsTrigger value="insights">Insights</TabsTrigger>
          </TabsList>

          <TabsContent value="overview" className="space-y-6">
            {/* Key Metrics Cards */}
            {metrics && (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <Card>
                  <CardContent className="p-6">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm font-medium text-gray-600">Total Reviews</p>
                        <p className="text-2xl font-bold">{metrics.totalReviews}</p>
                      </div>
                      <BarChart className="h-8 w-8 text-blue-500" />
                    </div>
                    <p className="text-xs text-gray-500 mt-2">
                      {selectedPeriod} day period
                    </p>
                  </CardContent>
                </Card>

                <Card>
                  <CardContent className="p-6">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm font-medium text-gray-600">Average Score</p>
                        <p className={`text-2xl font-bold ${getScoreColor(metrics.averageScore)}`}>
                          {metrics.averageScore.toFixed(1)}/5.0
                        </p>
                      </div>
                      <Award className="h-8 w-8 text-yellow-500" />
                    </div>
                    <p className="text-xs text-gray-500 mt-2">
                      Quality rating
                    </p>
                  </CardContent>
                </Card>

                <Card>
                  <CardContent className="p-6">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm font-medium text-gray-600">SLA Compliance</p>
                        <p className={`text-2xl font-bold ${getPerformanceColor(metrics.slaCompliance, 'percentage')}`}>
                          {metrics.slaCompliance.toFixed(1)}%
                        </p>
                      </div>
                      <Target className="h-8 w-8 text-green-500" />
                    </div>
                    <p className="text-xs text-gray-500 mt-2">
                      On-time completion
                    </p>
                  </CardContent>
                </Card>

                <Card>
                  <CardContent className="p-6">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm font-medium text-gray-600">Avg. Time</p>
                        <p className="text-2xl font-bold">
                          {formatTime(metrics.averageTimeToComplete)}
                        </p>
                      </div>
                      <Clock className="h-8 w-8 text-purple-500" />
                    </div>
                    <p className="text-xs text-gray-500 mt-2">
                      Per review
                    </p>
                  </CardContent>
                </Card>
              </div>
            )}

            {/* Additional Metrics */}
            {metrics && (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <Card>
                  <CardContent className="p-6">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm font-medium text-gray-600">Completion Rate</p>
                        <p className={`text-2xl font-bold ${getPerformanceColor(metrics.completionRate, 'percentage')}`}>
                          {metrics.completionRate.toFixed(1)}%
                        </p>
                      </div>
                      <CheckCircle className="h-8 w-8 text-green-500" />
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardContent className="p-6">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm font-medium text-gray-600">Escalation Rate</p>
                        <p className={`text-2xl font-bold ${getPerformanceColor(100 - metrics.escalationRate, 'percentage')}`}>
                          {metrics.escalationRate.toFixed(1)}%
                        </p>
                      </div>
                      <AlertTriangle className="h-8 w-8 text-orange-500" />
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardContent className="p-6">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm font-medium text-gray-600">Revision Rate</p>
                        <p className={`text-2xl font-bold ${getPerformanceColor(100 - metrics.revisionRate, 'percentage')}`}>
                          {metrics.revisionRate.toFixed(1)}%
                        </p>
                      </div>
                      <RefreshCw className="h-8 w-8 text-blue-500" />
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardContent className="p-6">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm font-medium text-gray-600">Designer Satisfaction</p>
                        <p className={`text-2xl font-bold ${getPerformanceColor(metrics.designerSatisfaction, 'percentage')}`}>
                          {metrics.designerSatisfaction}%
                        </p>
                      </div>
                      <Users className="h-8 w-8 text-indigo-500" />
                    </div>
                  </CardContent>
                </Card>
              </div>
            )}
          </TabsContent>

          <TabsContent value="trends" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Quality Trends Over Time</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="h-64 flex items-center justify-center text-gray-500">
                  <div className="text-center">
                    <LineChart className="h-12 w-12 mx-auto mb-4" />
                    <p>Trend charts would be implemented here</p>
                    <p className="text-sm">Using a charting library like Chart.js or Recharts</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="team" className="space-y-6">
            {teamPerformance.length > 0 && (
              <Card>
                <CardHeader>
                  <CardTitle>Team Performance Comparison</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {teamPerformance.map(member => (
                      <div key={member.memberId} className="border rounded-lg p-4">
                        <div className="flex items-center justify-between mb-3">
                          <h3 className="font-semibold">{member.memberName}</h3>
                          <Badge variant="outline">
                            {member.reviewsCompleted} reviews
                          </Badge>
                        </div>
                        
                        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                          <div>
                            <p className="text-gray-600">Avg. Score</p>
                            <p className={`font-semibold ${getScoreColor(member.averageScore)}`}>
                              {member.averageScore.toFixed(1)}/5.0
                            </p>
                          </div>
                          <div>
                            <p className="text-gray-600">Avg. Time</p>
                            <p className="font-semibold">{formatTime(member.averageTime)}</p>
                          </div>
                          <div>
                            <p className="text-gray-600">SLA Compliance</p>
                            <p className={`font-semibold ${getPerformanceColor(member.slaCompliance, 'percentage')}`}>
                              {member.slaCompliance.toFixed(1)}%
                            </p>
                          </div>
                          <div>
                            <p className="text-gray-600">Escalations</p>
                            <p className="font-semibold">{member.escalations}</p>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            )}
          </TabsContent>

          <TabsContent value="insights" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Quality Insights & Recommendations</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="p-4 bg-blue-50 rounded-lg border border-blue-200">
                    <div className="flex items-start gap-3">
                      <TrendingUp className="h-5 w-5 text-blue-500 mt-0.5" />
                      <div>
                        <h4 className="font-medium text-blue-900">Performance Trend</h4>
                        <p className="text-sm text-blue-700 mt-1">
                          Quality scores have improved by 12% over the last 30 days, indicating better review processes.
                        </p>
                      </div>
                    </div>
                  </div>
                  
                  <div className="p-4 bg-yellow-50 rounded-lg border border-yellow-200">
                    <div className="flex items-start gap-3">
                      <AlertTriangle className="h-5 w-5 text-yellow-500 mt-0.5" />
                      <div>
                        <h4 className="font-medium text-yellow-900">SLA Attention Needed</h4>
                        <p className="text-sm text-yellow-700 mt-1">
                          SLA compliance is below target at {metrics?.slaCompliance.toFixed(1)}%. Consider workload redistribution.
                        </p>
                      </div>
                    </div>
                  </div>
                  
                  <div className="p-4 bg-green-50 rounded-lg border border-green-200">
                    <div className="flex items-start gap-3">
                      <CheckCircle className="h-5 w-5 text-green-500 mt-0.5" />
                      <div>
                        <h4 className="font-medium text-green-900">Quality Standards</h4>
                        <p className="text-sm text-green-700 mt-1">
                          Average quality score of {metrics?.averageScore.toFixed(1)}/5.0 meets the target threshold.
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      )}
    </div>
  );
}
